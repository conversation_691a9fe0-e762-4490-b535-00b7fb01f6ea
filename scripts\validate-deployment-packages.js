#!/usr/bin/env node

/**
 * Deployment Package Validation Script
 * 
 * Validates both testing and production deployment packages to ensure:
 * - All required files are present
 * - Admin panel is properly built and accessible
 * - Configuration files are correct
 * - Scripts are executable
 * - Documentation is complete
 */

const fs = require('fs');
const path = require('path');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(70));
    console.log(`🔍 ${msg}`);
    console.log('='.repeat(70));
  }
};

function validatePackage(packageDir, packageName, expectedDomain) {
  log.header(`Validating ${packageName} Package`);
  
  let issues = 0;
  let warnings = 0;
  
  // Check if package directory exists
  if (!fs.existsSync(packageDir)) {
    log.error(`Package directory not found: ${packageDir}`);
    return { issues: 1, warnings: 0 };
  }
  
  log.info(`Validating package: ${packageDir}`);
  log.info(`Expected domain: ${expectedDomain}`);
  
  // Required files and directories
  const requiredItems = [
    // Frontend build
    { path: 'dist/index.html', type: 'file', description: 'Main frontend HTML' },
    { path: 'dist/admin/admin.html', type: 'file', description: 'Admin panel HTML' },
    { path: 'dist/assets', type: 'directory', description: 'Main frontend assets' },
    { path: 'dist/admin/assets', type: 'directory', description: 'Admin panel assets' },
    
    // Backend server
    { path: 'server/index.js', type: 'file', description: 'Main server entry point' },
    { path: 'server/package.json', type: 'file', description: 'Server package configuration' },
    { path: 'server/routes', type: 'directory', description: 'API routes' },
    { path: 'server/models', type: 'directory', description: 'Database models' },
    { path: 'server/controllers', type: 'directory', description: 'API controllers' },
    
    // Configuration files
    { path: 'package.json', type: 'file', description: 'Main package configuration' },
    { path: 'nginx', type: 'directory', description: 'Nginx configuration' },
    { path: 'config', type: 'directory', description: 'Environment configuration' },
    
    // Scripts
    { path: 'scripts', type: 'directory', description: 'Deployment and utility scripts' },
    { path: 'scripts/create-default-admin.js', type: 'file', description: 'Admin creation script' },
    { path: 'scripts/setup-database.js', type: 'file', description: 'Database setup script' },
    { path: 'scripts/test-database-connection.js', type: 'file', description: 'Database test script' },
    { path: 'scripts/system-status.js', type: 'file', description: 'System status script' },
    
    // Documentation
    { path: 'README.md', type: 'file', description: 'Deployment documentation' }
  ];
  
  // Validate required items
  log.info('Checking required files and directories...');
  requiredItems.forEach(item => {
    const fullPath = path.join(packageDir, item.path);
    const exists = fs.existsSync(fullPath);
    
    if (exists) {
      const stats = fs.statSync(fullPath);
      const isCorrectType = (item.type === 'file' && stats.isFile()) || 
                           (item.type === 'directory' && stats.isDirectory());
      
      if (isCorrectType) {
        log.success(`✓ ${item.description}: ${item.path}`);
      } else {
        log.error(`✗ ${item.description}: ${item.path} (wrong type)`);
        issues++;
      }
    } else {
      log.error(`✗ ${item.description}: ${item.path} (missing)`);
      issues++;
    }
  });
  
  // Validate admin panel build
  log.info('Validating admin panel build...');
  const adminHtmlPath = path.join(packageDir, 'dist/admin/admin.html');
  if (fs.existsSync(adminHtmlPath)) {
    const adminHtml = fs.readFileSync(adminHtmlPath, 'utf8');
    
    // Check for proper base href
    if (adminHtml.includes('base href="/admin/"')) {
      log.success('✓ Admin panel has correct base href');
    } else {
      log.warning('⚠ Admin panel may not have correct base href');
      warnings++;
    }
    
    // Check for admin assets
    if (adminHtml.includes('/admin/assets/')) {
      log.success('✓ Admin panel references correct asset paths');
    } else {
      log.warning('⚠ Admin panel asset paths may be incorrect');
      warnings++;
    }
  }
  
  // Validate environment configuration
  log.info('Validating environment configuration...');
  const envTemplatePath = path.join(packageDir, 'config', 
    packageName === 'Testing' ? '.env.testing.template' : '.env.production.template');
  
  if (fs.existsSync(envTemplatePath)) {
    const envContent = fs.readFileSync(envTemplatePath, 'utf8');
    
    // Check for required environment variables
    const requiredEnvVars = [
      'NODE_ENV',
      'PORT',
      'DB_HOST',
      'DB_NAME',
      'DB_USER',
      'DB_PASSWORD',
      'JWT_SECRET',
      'JWT_REFRESH_SECRET',
      'EMAIL_USER',
      'EMAIL_PASS',
      'SQUARE_ACCESS_TOKEN',
      'SQUARE_APPLICATION_ID'
    ];
    
    let missingVars = [];
    requiredEnvVars.forEach(varName => {
      if (!envContent.includes(varName + '=')) {
        missingVars.push(varName);
      }
    });
    
    if (missingVars.length === 0) {
      log.success('✓ All required environment variables present');
    } else {
      log.error(`✗ Missing environment variables: ${missingVars.join(', ')}`);
      issues++;
    }
    
    // Check for domain configuration
    if (envContent.includes(expectedDomain)) {
      log.success(`✓ Environment configured for domain: ${expectedDomain}`);
    } else {
      log.warning(`⚠ Domain ${expectedDomain} not found in environment template`);
      warnings++;
    }
  } else {
    log.error('✗ Environment template not found');
    issues++;
  }
  
  // Validate nginx configuration
  log.info('Validating nginx configuration...');
  const nginxFiles = fs.readdirSync(path.join(packageDir, 'nginx'));
  if (nginxFiles.length > 0) {
    const nginxFile = nginxFiles[0];
    const nginxPath = path.join(packageDir, 'nginx', nginxFile);
    const nginxContent = fs.readFileSync(nginxPath, 'utf8');
    
    // Check for admin panel configuration
    if (nginxContent.includes('location /admin/')) {
      log.success('✓ Nginx configured for admin panel routing');
    } else {
      log.error('✗ Nginx missing admin panel configuration');
      issues++;
    }
    
    // Check for API proxy configuration
    if (nginxContent.includes('location /api/')) {
      log.success('✓ Nginx configured for API proxy');
    } else {
      log.error('✗ Nginx missing API proxy configuration');
      issues++;
    }
    
    // Check for domain configuration
    if (nginxContent.includes(expectedDomain)) {
      log.success(`✓ Nginx configured for domain: ${expectedDomain}`);
    } else {
      log.warning(`⚠ Domain ${expectedDomain} not found in nginx configuration`);
      warnings++;
    }
  } else {
    log.error('✗ No nginx configuration files found');
    issues++;
  }
  
  // Validate PM2 configuration
  log.info('Validating PM2 configuration...');
  const pm2Files = fs.readdirSync(packageDir).filter(file => 
    file.startsWith('ecosystem.config') && file.endsWith('.js'));
  
  if (pm2Files.length > 0) {
    log.success(`✓ PM2 configuration found: ${pm2Files[0]}`);
  } else {
    log.error('✗ PM2 configuration not found');
    issues++;
  }
  
  // Validate deployment script
  log.info('Validating deployment script...');
  const deployScripts = fs.readdirSync(packageDir).filter(file => 
    file.startsWith('deploy-') && file.endsWith('.sh'));
  
  if (deployScripts.length > 0) {
    log.success(`✓ Deployment script found: ${deployScripts[0]}`);
  } else {
    log.error('✗ Deployment script not found');
    issues++;
  }
  
  return { issues, warnings };
}

function validateDeploymentPackages() {
  log.header('Nirvana Organics Deployment Package Validation');
  
  let totalIssues = 0;
  let totalWarnings = 0;
  
  // Validate testing package
  const testingResult = validatePackage(
    'test.deploy', 
    'Testing', 
    'test.shopnirvanaorganics.com'
  );
  totalIssues += testingResult.issues;
  totalWarnings += testingResult.warnings;
  
  // Validate production package
  const productionResult = validatePackage(
    'deploy', 
    'Production', 
    'shopnirvanaorganics.com'
  );
  totalIssues += productionResult.issues;
  totalWarnings += productionResult.warnings;
  
  // Summary
  log.header('Validation Summary');
  
  if (totalIssues === 0 && totalWarnings === 0) {
    log.success('🎉 All deployment packages validated successfully!');
    log.success('Both packages are ready for VPS deployment');
  } else if (totalIssues === 0) {
    log.success(`✅ Validation passed with ${totalWarnings} warnings`);
    log.warning('Review warnings before deployment');
  } else {
    log.error(`❌ Validation failed with ${totalIssues} issues and ${totalWarnings} warnings`);
    log.error('Fix all issues before deployment');
    process.exit(1);
  }
  
  // Display next steps
  log.header('Next Steps');
  log.info('1. Review any warnings and fix if necessary');
  log.info('2. Upload packages to your VPS servers');
  log.info('3. Run deployment scripts on respective servers');
  log.info('4. Configure SSL certificates');
  log.info('5. Test both environments thoroughly');
  
  log.header('Package Locations');
  log.info(`Testing Environment: ${path.resolve('test.deploy')}`);
  log.info(`Production Environment: ${path.resolve('deploy')}`);
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateDeploymentPackages();
}

module.exports = { validateDeploymentPackages };
