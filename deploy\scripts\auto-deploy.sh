#!/bin/bash

# ============================================================================
# Nirvana Backend Auto-Deployment Script
# Deploys the backend to root@srv928821
# ============================================================================

set -e  # Exit on any error

REMOTE_SERVER="root@srv928821"
REMOTE_PATH="/var/www/nirvana-backend"
LOCAL_PATH="."

echo "🚀 Starting Nirvana Backend Deployment"
echo "📡 Target Server: $REMOTE_SERVER"
echo "📁 Remote Path: $REMOTE_PATH"
echo ""

# Step 1: Prepare deployment package
echo "1️⃣ Preparing deployment package..."
node scripts/deploy-to-remote-server.js

# Step 2: Create remote directory
echo "2️⃣ Creating remote directory..."
ssh $REMOTE_SERVER "mkdir -p $REMOTE_PATH"

# Step 3: Copy files to remote server
echo "3️⃣ Copying files to remote server..."
rsync -avz --progress \
  --exclude='node_modules' \
  --exclude='.git' \
  --exclude='dist' \
  --exclude='dist-admin' \
  --exclude='logs' \
  --exclude='*.log' \
  --exclude='.DS_Store' \
  --exclude='Thumbs.db' \
  $LOCAL_PATH/ $REMOTE_SERVER:$REMOTE_PATH/

# Step 4: Install Node.js and dependencies
echo "4️⃣ Installing Node.js and dependencies..."
ssh $REMOTE_SERVER << 'EOF'
cd /var/www/nirvana-backend

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

# Install dependencies
echo "Installing npm dependencies..."
npm install --production

# Copy production environment
if [ -f .env.production ]; then
    cp .env.production .env
    echo "✅ Production environment configured"
fi

# Create necessary directories
mkdir -p uploads logs public/uploads
chmod 755 uploads logs public/uploads

# Make scripts executable
chmod +x scripts/*.js 2>/dev/null || true
EOF

# Step 5: Setup database
echo "5️⃣ Setting up database..."
ssh $REMOTE_SERVER << 'EOF'
cd /var/www/nirvana-backend

# Test database connection
echo "Testing database connection..."
node scripts/test-database-connection.js || echo "⚠️ Database connection failed - please configure database settings"

# Setup database tables (if connection works)
# node scripts/setup-database.js
# node scripts/create-database-tables.js
EOF

# Step 6: Install and configure PM2
echo "6️⃣ Installing and configuring PM2..."
ssh $REMOTE_SERVER << 'EOF'
cd /var/www/nirvana-backend

# Install PM2 globally
npm install -g pm2

# Stop existing process if running
pm2 stop nirvana-backend 2>/dev/null || true
pm2 delete nirvana-backend 2>/dev/null || true

# Start the application
pm2 start server/index.js --name nirvana-backend

# Setup PM2 to start on boot
pm2 startup
pm2 save

echo "✅ Backend server started with PM2"
EOF

# Step 7: Setup Nginx (optional)
echo "7️⃣ Setting up Nginx reverse proxy..."
ssh $REMOTE_SERVER << 'EOF'
# Install Nginx if not present
if ! command -v nginx &> /dev/null; then
    echo "Installing Nginx..."
    apt update
    apt install -y nginx
fi

# Create Nginx configuration
cat > /etc/nginx/sites-available/nirvana-backend << 'NGINX_EOF'
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /uploads {
        alias /var/www/nirvana-backend/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
NGINX_EOF

# Enable the site
ln -sf /etc/nginx/sites-available/nirvana-backend /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

echo "✅ Nginx configured"
EOF

# Step 8: Final checks
echo "8️⃣ Running final checks..."
ssh $REMOTE_SERVER << 'EOF'
cd /var/www/nirvana-backend

echo "📊 PM2 Status:"
pm2 status

echo ""
echo "🌐 Server Status:"
curl -s http://localhost:5000/api/health || echo "⚠️ Health check failed"

echo ""
echo "📁 Directory Structure:"
ls -la

echo ""
echo "🔧 Environment:"
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"
echo "PM2 version: $(pm2 --version)"
EOF

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Update .env file on the server with your production settings"
echo "2. Configure your domain in Nginx configuration"
echo "3. Setup SSL certificate (Let's Encrypt recommended)"
echo "4. Test your API endpoints"
echo ""
echo "🔧 Useful Commands:"
echo "ssh $REMOTE_SERVER 'pm2 status'"
echo "ssh $REMOTE_SERVER 'pm2 logs nirvana-backend'"
echo "ssh $REMOTE_SERVER 'pm2 restart nirvana-backend'"
echo ""
