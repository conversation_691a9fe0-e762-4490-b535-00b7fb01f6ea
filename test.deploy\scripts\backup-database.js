#!/usr/bin/env node

/**
 * Database Backup and Restore <PERSON>t
 * Creates backups of the production database
 */

require('dotenv').config();
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🔄${colors.reset} ${msg}`)
};

/**
 * Create database backup
 */
function createBackup() {
  log.step('Creating database backup...');
  
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(process.cwd(), 'database', 'backups');
    
    // Create backups directory if it doesn't exist
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const backupFile = path.join(backupDir, `backup-${timestamp}.sql`);
    
    // Build mysqldump command
    const command = `mysqldump -h ${process.env.DB_HOST} -P ${process.env.DB_PORT || 3306} -u ${process.env.DB_USER} -p${process.env.DB_PASSWORD} ${process.env.DB_NAME} > "${backupFile}"`;
    
    execSync(command, { stdio: 'inherit' });
    
    log.success(`Database backup created: ${backupFile}`);
    return backupFile;
    
  } catch (error) {
    log.error(`Backup failed: ${error.message}`);
    return null;
  }
}

/**
 * Restore database from backup
 */
function restoreBackup(backupFile) {
  log.step(`Restoring database from ${backupFile}...`);
  
  try {
    if (!fs.existsSync(backupFile)) {
      throw new Error(`Backup file not found: ${backupFile}`);
    }
    
    // Build mysql restore command
    const command = `mysql -h ${process.env.DB_HOST} -P ${process.env.DB_PORT || 3306} -u ${process.env.DB_USER} -p${process.env.DB_PASSWORD} ${process.env.DB_NAME} < "${backupFile}"`;
    
    execSync(command, { stdio: 'inherit' });
    
    log.success('Database restored successfully');
    return true;
    
  } catch (error) {
    log.error(`Restore failed: ${error.message}`);
    return false;
  }
}

/**
 * List available backups
 */
function listBackups() {
  log.step('Available backups:');
  
  const backupDir = path.join(process.cwd(), 'database', 'backups');
  
  if (!fs.existsSync(backupDir)) {
    log.warning('No backups directory found');
    return [];
  }
  
  const backups = fs.readdirSync(backupDir)
    .filter(file => file.endsWith('.sql'))
    .sort()
    .reverse();
  
  if (backups.length === 0) {
    log.warning('No backups found');
    return [];
  }
  
  backups.forEach((backup, index) => {
    const filePath = path.join(backupDir, backup);
    const stats = fs.statSync(filePath);
    const size = (stats.size / 1024).toFixed(2);
    
    console.log(`  ${index + 1}. ${backup} (${size} KB) - ${stats.mtime.toLocaleString()}`);
  });
  
  return backups.map(backup => path.join(backupDir, backup));
}

/**
 * Clean old backups (keep last 10)
 */
function cleanOldBackups() {
  log.step('Cleaning old backups...');
  
  const backupDir = path.join(process.cwd(), 'database', 'backups');
  
  if (!fs.existsSync(backupDir)) {
    log.warning('No backups directory found');
    return;
  }
  
  const backups = fs.readdirSync(backupDir)
    .filter(file => file.endsWith('.sql'))
    .map(file => ({
      name: file,
      path: path.join(backupDir, file),
      mtime: fs.statSync(path.join(backupDir, file)).mtime
    }))
    .sort((a, b) => b.mtime - a.mtime);
  
  if (backups.length <= 10) {
    log.info(`${backups.length} backups found, no cleanup needed`);
    return;
  }
  
  const toDelete = backups.slice(10);
  
  toDelete.forEach(backup => {
    fs.unlinkSync(backup.path);
    log.info(`Deleted old backup: ${backup.name}`);
  });
  
  log.success(`Cleaned ${toDelete.length} old backups`);
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  console.log(`${colors.cyan}🗄️ Database Backup & Restore Tool${colors.reset}`);
  console.log(`${colors.cyan}==================================${colors.reset}\n`);
  
  switch (command) {
    case 'backup':
      const backupFile = createBackup();
      if (backupFile) {
        cleanOldBackups();
      }
      break;
      
    case 'restore':
      const backupToRestore = args[1];
      if (!backupToRestore) {
        log.error('Please specify backup file to restore');
        log.info('Usage: node backup-database.js restore <backup-file>');
        listBackups();
        process.exit(1);
      }
      restoreBackup(backupToRestore);
      break;
      
    case 'list':
      listBackups();
      break;
      
    case 'clean':
      cleanOldBackups();
      break;
      
    default:
      console.log('Usage:');
      console.log('  node backup-database.js backup    - Create a new backup');
      console.log('  node backup-database.js restore <file> - Restore from backup');
      console.log('  node backup-database.js list      - List available backups');
      console.log('  node backup-database.js clean     - Clean old backups');
      break;
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { createBackup, restoreBackup, listBackups, cleanOldBackups };
