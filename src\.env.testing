# Frontend Testing Environment Configuration
# All variables must be prefixed with VITE_ to be accessible in the frontend

# API Configuration
VITE_API_URL=https://test.shopnirvanaorganics.com/api
VITE_ADMIN_API_URL=/api

# Application Configuration
VITE_APP_NAME="Nirvana Organics (Testing)"
VITE_ENVIRONMENT=testing

# Google OAuth Configuration (Frontend)
VITE_GOOGLE_CLIENT_ID=your-google-client-id-here

# Domain Configuration
VITE_FRONTEND_URL=https://test.shopnirvanaorganics.com
VITE_BACKEND_URL=https://test.shopnirvanaorganics.com

# Square Payment Configuration (Sandbox)
VITE_SQUARE_APPLICATION_ID=your-sandbox-square-application-id-here
VITE_SQUARE_ENVIRONMENT=sandbox

# Push Notifications (Public key only)
VITE_VAPID_PUBLIC_KEY=your-vapid-public-key-here

# Feature Flags
VITE_ENABLE_GOOGLE_OAUTH=true
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_SQUARE_PAYMENTS=true
VITE_ENABLE_ANALYTICS=true

# Debug Configuration (Testing - Limited)
VITE_DEBUG_MODE=false
VITE_ENABLE_CONSOLE_LOGS=false
VITE_ENABLE_REDUX_DEVTOOLS=false

# Performance Configuration
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_PWA=true
VITE_CACHE_STATIC_ASSETS=true

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_ANIMATIONS=true

# File Upload Configuration (Frontend limits)
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp
VITE_ALLOWED_DOCUMENT_TYPES=pdf,doc,docx

# Rate Limiting (Frontend display)
VITE_RATE_LIMIT_DISPLAY=true
VITE_RATE_LIMIT_WARNING_THRESHOLD=80

# Admin Configuration
VITE_ADMIN_SESSION_TIMEOUT=3600000
VITE_ENABLE_ADMIN_ANALYTICS=true
VITE_ENABLE_DATA_ENVIRONMENT=true

# Contact Information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_ORDERS_EMAIL=<EMAIL>
VITE_CUSTOMER_SERVICE_EMAIL=<EMAIL>

# Social Media Links
VITE_FACEBOOK_URL=https://facebook.com/nirvanaorganics
VITE_INSTAGRAM_URL=https://instagram.com/nirvanaorganics
VITE_TWITTER_URL=https://twitter.com/nirvanaorganics

# SEO Configuration
VITE_SITE_TITLE="Nirvana Organics - Testing Environment"
VITE_SITE_DESCRIPTION="Testing environment for Nirvana Organics e-commerce platform"
VITE_SITE_KEYWORDS="organic,natural,wellness,health,sustainable,eco-friendly,testing"

# Analytics Configuration (Testing - Test IDs)
VITE_GOOGLE_ANALYTICS_ID=your-test-ga-tracking-id-here
VITE_FACEBOOK_PIXEL_ID=your-test-facebook-pixel-id-here

# Error Reporting (Testing - Enabled)
VITE_SENTRY_DSN=your-sentry-public-dsn-here
VITE_ENABLE_ERROR_REPORTING=true

# Testing-specific Configuration
VITE_ENABLE_TEST_MODE=true
VITE_SHOW_TEST_BANNER=true
VITE_ENABLE_MOCK_DATA=true
