#!/usr/bin/env node

/**
 * Simple Database Connection Test
 * Tests if we can connect to the database with current credentials
 */

const mysql = require('mysql2/promise');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.blue}${msg}${colors.reset}`)
};

async function testConnection() {
  // Load environment variables
  require('dotenv').config();

  log.header('🔍 Testing Database Connection');

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || ''
  };

  log.info(`Attempting to connect to MySQL server:`);
  log.info(`Host: ${config.host}`);
  log.info(`Port: ${config.port}`);
  log.info(`User: ${config.user}`);
  log.info(`Password: ${config.password ? '[SET]' : '[NOT SET]'}`);

  try {
    // Test connection to MySQL server
    const connection = await mysql.createConnection(config);
    log.success('Successfully connected to MySQL server!');

    // Test basic query
    const [result] = await connection.execute('SELECT VERSION() as version');
    log.success(`MySQL Version: ${result[0].version}`);

    // Test database existence
    const dbName = process.env.DB_NAME || 'nirvana_organics_local';
    const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [dbName]);
    
    if (databases.length > 0) {
      log.success(`Database '${dbName}' exists`);
      
      // Test connection to specific database
      await connection.execute(`USE \`${dbName}\``);
      log.success(`Successfully connected to database '${dbName}'`);
      
      // Show tables
      const [tables] = await connection.execute('SHOW TABLES');
      if (tables.length > 0) {
        log.success(`Found ${tables.length} tables in database`);
        tables.forEach(table => {
          const tableName = Object.values(table)[0];
          log.info(`  - ${tableName}`);
        });
      } else {
        log.warning('Database exists but has no tables');
      }
    } else {
      log.warning(`Database '${dbName}' does not exist`);
      log.info('Run "node scripts/setup-local-database.js" to create it');
    }

    await connection.end();
    log.success('Connection test completed successfully!');

  } catch (error) {
    log.error(`Connection failed: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      log.error('MySQL server is not running or connection refused');
      log.info('Possible solutions:');
      log.info('1. Start MySQL/MariaDB service');
      log.info('2. Check if MySQL is installed');
      log.info('3. Verify host and port settings');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      log.error('Access denied - check username and password');
      log.info('Update DB_USER and DB_PASSWORD in .env file');
    } else if (error.code === 'ENOTFOUND') {
      log.error('Host not found - check DB_HOST setting');
    }
    
    process.exit(1);
  }
}

// Run test
testConnection().catch(error => {
  console.error('Test failed:', error.message);
  process.exit(1);
});
