import React from 'react';
import { Helmet } from 'react-helmet-async';

interface OpenGraphMetaProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  siteName?: string;
  locale?: string;
  // Article specific
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  // Product specific
  price?: string;
  currency?: string;
  availability?: 'in stock' | 'out of stock' | 'preorder';
  brand?: string;
  // Twitter specific
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  twitterSite?: string;
  twitterCreator?: string;
  // Additional meta
  keywords?: string[];
  canonical?: string;
}

const OpenGraphMeta: React.FC<OpenGraphMetaProps> = ({
  title = 'Nirvana Organics - Premium Organic Products',
  description = 'Discover premium organic products at Nirvana Organics. From fresh produce to natural supplements, we offer the finest organic goods for a healthier lifestyle.',
  image = '/images/og-default.jpg',
  url = window.location.href,
  type = 'website',
  siteName = 'Nirvana Organics',
  locale = 'en_US',
  author,
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  price,
  currency = 'USD',
  availability,
  brand = 'Nirvana Organics',
  twitterCard = 'summary_large_image',
  twitterSite = '@nirvanaorganics',
  twitterCreator = '@nirvanaorganics',
  keywords = ['organic', 'natural', 'healthy', 'sustainable', 'eco-friendly'],
  canonical
}) => {
  // Ensure absolute URL for image
  const absoluteImage = image.startsWith('http') 
    ? image 
    : `${window.location.origin}${image}`;

  // Ensure absolute URL for canonical
  const absoluteCanonical = canonical || url;

  // Generate structured data for products
  const generateProductStructuredData = () => {
    if (type === 'product' && price) {
      return {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": title,
        "description": description,
        "image": absoluteImage,
        "brand": {
          "@type": "Brand",
          "name": brand
        },
        "offers": {
          "@type": "Offer",
          "price": price,
          "priceCurrency": currency,
          "availability": availability === 'in stock' 
            ? "https://schema.org/InStock"
            : availability === 'out of stock'
            ? "https://schema.org/OutOfStock"
            : "https://schema.org/PreOrder",
          "seller": {
            "@type": "Organization",
            "name": siteName
          }
        }
      };
    }
    return null;
  };

  // Generate structured data for articles
  const generateArticleStructuredData = () => {
    if (type === 'article' && author) {
      return {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": title,
        "description": description,
        "image": absoluteImage,
        "author": {
          "@type": "Person",
          "name": author
        },
        "publisher": {
          "@type": "Organization",
          "name": siteName,
          "logo": {
            "@type": "ImageObject",
            "url": `${window.location.origin}/images/logo.png`
          }
        },
        "datePublished": publishedTime,
        "dateModified": modifiedTime || publishedTime,
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": url
        }
      };
    }
    return null;
  };

  // Generate organization structured data
  const generateOrganizationStructuredData = () => {
    return {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": siteName,
      "url": window.location.origin,
      "logo": `${window.location.origin}/images/logo.png`,
      "description": "Premium organic products for a healthier lifestyle",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-0123",
        "contactType": "customer service",
        "email": "<EMAIL>"
      },
      "sameAs": [
        "https://facebook.com/nirvanaorganics",
        "https://twitter.com/nirvanaorganics",
        "https://instagram.com/nirvanaorganics",
        "https://linkedin.com/company/nirvanaorganics"
      ]
    };
  };

  const productStructuredData = generateProductStructuredData();
  const articleStructuredData = generateArticleStructuredData();
  const organizationStructuredData = generateOrganizationStructuredData();

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <link rel="canonical" href={absoluteCanonical} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={absoluteImage} />
      <meta property="og:image:alt" content={title} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />

      {/* Article specific Open Graph tags */}
      {type === 'article' && author && (
        <>
          <meta property="article:author" content={author} />
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          {tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}

      {/* Product specific Open Graph tags */}
      {type === 'product' && (
        <>
          {price && <meta property="product:price:amount" content={price} />}
          {currency && <meta property="product:price:currency" content={currency} />}
          {availability && <meta property="product:availability" content={availability} />}
          {brand && <meta property="product:brand" content={brand} />}
        </>
      )}

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content={twitterSite} />
      <meta name="twitter:creator" content={twitterCreator} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={absoluteImage} />
      <meta name="twitter:image:alt" content={title} />

      {/* Additional Meta Tags */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Mobile and Responsive */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Theme and App */}
      <meta name="theme-color" content="#10b981" />
      <meta name="msapplication-TileColor" content="#10b981" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={siteName} />

      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />

      {/* Structured Data */}
      {productStructuredData && (
        <script type="application/ld+json">
          {JSON.stringify(productStructuredData)}
        </script>
      )}
      
      {articleStructuredData && (
        <script type="application/ld+json">
          {JSON.stringify(articleStructuredData)}
        </script>
      )}
      
      <script type="application/ld+json">
        {JSON.stringify(organizationStructuredData)}
      </script>

      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://connect.squareup.com" />

      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//connect.squareup.com" />

      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    </Helmet>
  );
};

export default OpenGraphMeta;
