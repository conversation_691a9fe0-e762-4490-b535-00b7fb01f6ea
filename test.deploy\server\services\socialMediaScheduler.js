const cron = require('node-cron');
const SocialMediaPost = require('../models/SocialMediaPost');
const SocialMediaAccount = require('../models/SocialMediaAccount');
const SocialMediaAnalytics = require('../models/SocialMediaAnalytics');
const socialMediaService = require('./socialMediaService');
const { appLogger } = require('../utils/logger');

/**
 * Social Media Scheduler Service
 * Handles scheduled posts and analytics collection
 */
class SocialMediaScheduler {
  constructor() {
    this.isRunning = false;
    this.jobs = new Map();
  }

  /**
   * Initialize the scheduler
   */
  init() {
    if (this.isRunning) {
      appLogger.warn('Social media scheduler is already running');
      return;
    }

    this.isRunning = true;
    appLogger.info('Initializing social media scheduler...');

    // Schedule post publishing (every minute)
    this.jobs.set('publishPosts', cron.schedule('* * * * *', async () => {
      await this.processScheduledPosts();
    }, { scheduled: false }));

    // Schedule analytics collection (every hour)
    this.jobs.set('collectAnalytics', cron.schedule('0 * * * *', async () => {
      await this.collectAnalytics();
    }, { scheduled: false }));

    // Schedule token refresh (every 6 hours)
    this.jobs.set('refreshTokens', cron.schedule('0 */6 * * *', async () => {
      await this.refreshExpiredTokens();
    }, { scheduled: false }));

    // Schedule daily analytics aggregation (at 2 AM)
    this.jobs.set('aggregateAnalytics', cron.schedule('0 2 * * *', async () => {
      await this.aggregateDailyAnalytics();
    }, { scheduled: false }));

    // Start all jobs
    this.startAllJobs();

    appLogger.info('Social media scheduler initialized successfully');
  }

  /**
   * Start all scheduled jobs
   */
  startAllJobs() {
    this.jobs.forEach((job, name) => {
      job.start();
      appLogger.info(`Started social media job: ${name}`);
    });
  }

  /**
   * Stop all scheduled jobs
   */
  stopAllJobs() {
    this.jobs.forEach((job, name) => {
      job.stop();
      appLogger.info(`Stopped social media job: ${name}`);
    });
    this.isRunning = false;
  }

  /**
   * Process scheduled posts that are ready to be published
   */
  async processScheduledPosts() {
    try {
      const scheduledPosts = await SocialMediaPost.findScheduledPosts();
      
      if (scheduledPosts.length === 0) {
        return;
      }

      appLogger.info(`Processing ${scheduledPosts.length} scheduled posts`);

      for (const post of scheduledPosts) {
        try {
          // Update status to publishing
          await post.update({ status: 'publishing' });

          // Publish to all platforms
          const results = await socialMediaService.publishPost(post);

          if (results.success) {
            await post.markAsPublished(results.platformPostIds);
            appLogger.info(`Successfully published post ${post.id} to platforms: ${post.platforms.join(', ')}`);
          } else {
            await post.markAsFailed(results.error);
            appLogger.error(`Failed to publish post ${post.id}: ${results.error}`);
          }
        } catch (error) {
          await post.markAsFailed(error);
          appLogger.error(`Error processing scheduled post ${post.id}:`, error);
        }
      }
    } catch (error) {
      appLogger.error('Error processing scheduled posts:', error);
    }
  }

  /**
   * Collect analytics data from all connected accounts
   */
  async collectAnalytics() {
    try {
      const accounts = await SocialMediaAccount.findActiveAccounts();
      
      if (accounts.length === 0) {
        return;
      }

      appLogger.info(`Collecting analytics for ${accounts.length} accounts`);

      for (const account of accounts) {
        try {
          // Skip if token is expired and can't be refreshed
          if (account.isTokenExpired() && !account.refreshToken) {
            appLogger.warn(`Skipping analytics collection for ${account.platform} account ${account.id} - token expired`);
            continue;
          }

          // Refresh token if needed
          if (account.needsRefresh() && account.refreshToken) {
            const newTokenData = await socialMediaService.refreshToken(account.platform, account.refreshToken);
            await account.update({
              accessToken: newTokenData.accessToken,
              refreshToken: newTokenData.refreshToken || account.refreshToken,
              tokenExpiresAt: newTokenData.expiresAt
            });
          }

          // Collect current metrics
          const metrics = await this.collectAccountMetrics(account);
          
          // Update account metrics
          await account.updateMetrics(metrics);

          appLogger.info(`Updated metrics for ${account.platform} account ${account.accountName}`);
        } catch (error) {
          await account.recordError(error);
          appLogger.error(`Error collecting analytics for account ${account.id}:`, error);
        }
      }
    } catch (error) {
      appLogger.error('Error collecting analytics:', error);
    }
  }

  /**
   * Collect metrics for a specific account
   */
  async collectAccountMetrics(account) {
    const platformMetrics = await this.getPlatformMetrics(account.platform, account.accessToken);
    
    return {
      followersCount: platformMetrics.followersCount || account.followersCount,
      followingCount: platformMetrics.followingCount || account.followingCount,
      postsCount: platformMetrics.postsCount || account.postsCount,
      engagementRate: platformMetrics.engagementRate || 0,
      recentPosts: platformMetrics.recentPosts || []
    };
  }

  /**
   * Get platform-specific metrics
   */
  async getPlatformMetrics(platform, accessToken) {
    try {
      switch (platform) {
        case 'facebook':
          return await this.getFacebookMetrics(accessToken);
        case 'twitter':
          return await this.getTwitterMetrics(accessToken);
        case 'instagram':
          return await this.getInstagramMetrics(accessToken);
        case 'linkedin':
          return await this.getLinkedInMetrics(accessToken);
        case 'youtube':
          return await this.getYouTubeMetrics(accessToken);
        case 'pinterest':
          return await this.getPinterestMetrics(accessToken);
        default:
          throw new Error(`Metrics collection not implemented for ${platform}`);
      }
    } catch (error) {
      appLogger.error(`Error collecting ${platform} metrics:`, error);
      return {};
    }
  }

  /**
   * Get Facebook metrics
   */
  async getFacebookMetrics(accessToken) {
    const axios = require('axios');
    
    // Get page info and insights
    const pageResponse = await axios.get(`https://graph.facebook.com/v18.0/me?fields=fan_count,posts.limit(10){message,created_time,likes.summary(true),comments.summary(true),shares}`, {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    const pageData = pageResponse.data;
    const posts = pageData.posts?.data || [];
    
    let totalLikes = 0;
    let totalComments = 0;
    let totalShares = 0;
    
    posts.forEach(post => {
      totalLikes += post.likes?.summary?.total_count || 0;
      totalComments += post.comments?.summary?.total_count || 0;
      totalShares += post.shares?.count || 0;
    });

    const engagementRate = posts.length > 0 ? 
      ((totalLikes + totalComments + totalShares) / (posts.length * (pageData.fan_count || 1))) * 100 : 0;

    return {
      followersCount: pageData.fan_count || 0,
      postsCount: posts.length,
      engagementRate,
      recentPosts: posts.map(post => ({
        id: post.id,
        content: post.message,
        createdAt: post.created_time,
        likes: post.likes?.summary?.total_count || 0,
        comments: post.comments?.summary?.total_count || 0,
        shares: post.shares?.count || 0
      }))
    };
  }

  /**
   * Get Twitter metrics
   */
  async getTwitterMetrics(accessToken) {
    const axios = require('axios');
    
    // Get user info and recent tweets
    const userResponse = await axios.get('https://api.twitter.com/2/users/me?user.fields=public_metrics', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    const tweetsResponse = await axios.get('https://api.twitter.com/2/users/me/tweets?tweet.fields=public_metrics,created_at&max_results=10', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    const userData = userResponse.data.data;
    const tweets = tweetsResponse.data.data || [];
    
    let totalEngagements = 0;
    let totalImpressions = 0;
    
    tweets.forEach(tweet => {
      const metrics = tweet.public_metrics;
      totalEngagements += (metrics.like_count + metrics.reply_count + metrics.retweet_count);
      totalImpressions += metrics.impression_count || 0;
    });

    const engagementRate = totalImpressions > 0 ? (totalEngagements / totalImpressions) * 100 : 0;

    return {
      followersCount: userData.public_metrics.followers_count,
      followingCount: userData.public_metrics.following_count,
      postsCount: userData.public_metrics.tweet_count,
      engagementRate,
      recentPosts: tweets.map(tweet => ({
        id: tweet.id,
        content: tweet.text,
        createdAt: tweet.created_at,
        likes: tweet.public_metrics.like_count,
        comments: tweet.public_metrics.reply_count,
        shares: tweet.public_metrics.retweet_count,
        impressions: tweet.public_metrics.impression_count
      }))
    };
  }

  /**
   * Get Instagram metrics (placeholder - requires business account)
   */
  async getInstagramMetrics(accessToken) {
    // Instagram Basic Display API has limited metrics
    // For full analytics, Instagram Business API is required
    return {
      followersCount: 0,
      postsCount: 0,
      engagementRate: 0,
      recentPosts: []
    };
  }

  /**
   * Get LinkedIn metrics
   */
  async getLinkedInMetrics(accessToken) {
    // LinkedIn API has limited free tier access
    // This would require LinkedIn Marketing API for full metrics
    return {
      followersCount: 0,
      postsCount: 0,
      engagementRate: 0,
      recentPosts: []
    };
  }

  /**
   * Get YouTube metrics
   */
  async getYouTubeMetrics(accessToken) {
    const axios = require('axios');
    
    // Get channel statistics
    const channelResponse = await axios.get('https://www.googleapis.com/youtube/v3/channels?part=statistics,snippet&mine=true', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    const channel = channelResponse.data.items?.[0];
    if (!channel) return {};

    const stats = channel.statistics;
    
    return {
      followersCount: parseInt(stats.subscriberCount) || 0,
      postsCount: parseInt(stats.videoCount) || 0,
      engagementRate: 0, // Would need video-specific analytics
      recentPosts: []
    };
  }

  /**
   * Get Pinterest metrics
   */
  async getPinterestMetrics(accessToken) {
    const axios = require('axios');
    
    // Get user account info
    const userResponse = await axios.get('https://api.pinterest.com/v5/user_account', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    const userData = userResponse.data;
    
    return {
      followersCount: userData.follower_count || 0,
      followingCount: userData.following_count || 0,
      postsCount: userData.pin_count || 0,
      engagementRate: 0,
      recentPosts: []
    };
  }

  /**
   * Refresh expired tokens
   */
  async refreshExpiredTokens() {
    try {
      const expiredAccounts = await SocialMediaAccount.findExpiredTokens();
      
      if (expiredAccounts.length === 0) {
        return;
      }

      appLogger.info(`Refreshing ${expiredAccounts.length} expired tokens`);

      for (const account of expiredAccounts) {
        try {
          if (!account.refreshToken) {
            appLogger.warn(`No refresh token available for ${account.platform} account ${account.id}`);
            continue;
          }

          const newTokenData = await socialMediaService.refreshToken(account.platform, account.refreshToken);
          
          await account.update({
            accessToken: newTokenData.accessToken,
            refreshToken: newTokenData.refreshToken || account.refreshToken,
            tokenExpiresAt: newTokenData.expiresAt,
            lastError: null,
            errorCount: 0
          });

          appLogger.info(`Refreshed token for ${account.platform} account ${account.accountName}`);
        } catch (error) {
          await account.recordError(error);
          appLogger.error(`Failed to refresh token for account ${account.id}:`, error);
        }
      }
    } catch (error) {
      appLogger.error('Error refreshing expired tokens:', error);
    }
  }

  /**
   * Aggregate daily analytics
   */
  async aggregateDailyAnalytics() {
    try {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);

      const accounts = await SocialMediaAccount.findActiveAccounts();
      
      appLogger.info(`Aggregating daily analytics for ${accounts.length} accounts`);

      for (const account of accounts) {
        try {
          // Check if analytics already exist for yesterday
          const existingAnalytics = await SocialMediaAnalytics.findOne({
            where: {
              accountId: account.id,
              date: yesterday,
              period: 'daily'
            }
          });

          if (existingAnalytics) {
            continue; // Skip if already processed
          }

          // Get posts from yesterday
          const yesterdayPosts = await SocialMediaPost.findAll({
            where: {
              platforms: {
                [SocialMediaPost.sequelize.Sequelize.Op.contains]: [account.platform]
              },
              publishedAt: {
                [SocialMediaPost.sequelize.Sequelize.Op.between]: [
                  yesterday,
                  new Date(yesterday.getTime() + 24 * 60 * 60 * 1000)
                ]
              },
              status: 'published'
            }
          });

          // Aggregate metrics
          let totalLikes = 0;
          let totalComments = 0;
          let totalShares = 0;
          let totalReach = 0;
          let totalImpressions = 0;

          yesterdayPosts.forEach(post => {
            const platformMetrics = post.platformMetrics?.[account.platform];
            if (platformMetrics) {
              totalLikes += platformMetrics.likes || 0;
              totalComments += platformMetrics.comments || 0;
              totalShares += platformMetrics.shares || 0;
              totalReach += platformMetrics.reach || 0;
              totalImpressions += platformMetrics.impressions || 0;
            }
          });

          // Calculate engagement rate
          const engagementRate = totalImpressions > 0 ? 
            ((totalLikes + totalComments + totalShares) / totalImpressions) * 100 : 0;

          // Create analytics record
          await SocialMediaAnalytics.create({
            accountId: account.id,
            date: yesterday,
            period: 'daily',
            followersCount: account.followersCount,
            totalLikes,
            totalComments,
            totalShares,
            totalReach,
            totalImpressions,
            postsCount: yesterdayPosts.length,
            engagementRate
          });

          appLogger.info(`Created daily analytics for ${account.platform} account ${account.accountName}`);
        } catch (error) {
          appLogger.error(`Error aggregating analytics for account ${account.id}:`, error);
        }
      }
    } catch (error) {
      appLogger.error('Error aggregating daily analytics:', error);
    }
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      jobs: Array.from(this.jobs.keys()).map(name => ({
        name,
        running: this.jobs.get(name).running
      }))
    };
  }
}

module.exports = new SocialMediaScheduler();
