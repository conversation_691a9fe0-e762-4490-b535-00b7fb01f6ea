const express = require('express');
const router = express.Router();
const { authenticate, requireAdmin } = require('../middleware/auth');
const adminSocialMediaController = require('../controllers/adminSocialMediaController');

// All routes require admin authentication
router.use(authenticate);
router.use(requireAdmin);

// @route   POST /api/admin/social-media/refresh
// @desc    Refresh social media feeds
// @access  Private (Admin)
router.post('/refresh', adminSocialMediaController.refreshFeeds);

// @route   GET /api/admin/social-media/feeds/status
// @desc    Get social media feed status
// @access  Private (Admin)
router.get('/feeds/status', adminSocialMediaController.getFeedStatus);

// @route   PUT /api/admin/social-media/platforms/:platform/settings
// @desc    Update social media platform settings
// @access  Private (Admin)
router.put('/platforms/:platform/settings', adminSocialMediaController.updatePlatformSettings);

// @route   GET /api/admin/social-media/platforms/:platform/settings
// @desc    Get social media platform settings
// @access  Private (Admin)
router.get('/platforms/:platform/settings', adminSocialMediaController.getPlatformSettings);

// @route   POST /api/admin/social-media/share
// @desc    Share content to social media platforms
// @access  Private (Admin)
router.post('/share', adminSocialMediaController.shareContent);

// @route   GET /api/admin/social-media/analytics
// @desc    Get social media analytics
// @access  Private (Admin)
router.get('/analytics', adminSocialMediaController.getAnalytics);

module.exports = router;
