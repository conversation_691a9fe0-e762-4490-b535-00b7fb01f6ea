#!/bin/bash

# File Permissions Setup Script for Nirvana Organics E-commerce
# Ensures correct ownership and permissions for deployment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/nirvana-backend"
APP_USER="nirvana"
APP_GROUP="nirvana"

echo -e "${CYAN}🔧 Setting up file permissions for Nirvana Organics deployment...${NC}"
echo

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ This script must be run as root (use sudo)${NC}"
    echo -e "${YELLOW}Usage: sudo ./scripts/setup-permissions.sh${NC}"
    exit 1
fi

# Check if application directory exists
if [ ! -d "$APP_DIR" ]; then
    echo -e "${RED}❌ Application directory $APP_DIR does not exist${NC}"
    echo -e "${YELLOW}Please ensure the application files are uploaded first${NC}"
    exit 1
fi

# Check if application user exists
if ! id "$APP_USER" &>/dev/null; then
    echo -e "${YELLOW}⚠️  User $APP_USER does not exist, creating...${NC}"
    useradd -m -s /bin/bash $APP_USER
    usermod -aG sudo $APP_USER
    echo -e "${GREEN}✅ Created user $APP_USER${NC}"
fi

# Set ownership
echo -e "${BLUE}📁 Setting ownership to $APP_USER:$APP_GROUP...${NC}"
chown -R $APP_USER:$APP_GROUP $APP_DIR
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Ownership set successfully${NC}"
else
    echo -e "${RED}❌ Failed to set ownership${NC}"
    exit 1
fi

# Set directory permissions (755 - owner: rwx, group: rx, others: rx)
echo -e "${BLUE}🔒 Setting directory permissions (755)...${NC}"
find $APP_DIR -type d -exec chmod 755 {} \;
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Directory permissions set successfully${NC}"
else
    echo -e "${RED}❌ Failed to set directory permissions${NC}"
    exit 1
fi

# Set file permissions (644 - owner: rw, group: r, others: r)
echo -e "${BLUE}📄 Setting file permissions (644)...${NC}"
find $APP_DIR -type f -exec chmod 644 {} \;
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ File permissions set successfully${NC}"
else
    echo -e "${RED}❌ Failed to set file permissions${NC}"
    exit 1
fi

# Set executable permissions for scripts
echo -e "${BLUE}⚡ Setting executable permissions for scripts...${NC}"
if [ -d "$APP_DIR/scripts" ]; then
    chmod +x $APP_DIR/scripts/*.sh 2>/dev/null
    chmod +x $APP_DIR/scripts/*.js 2>/dev/null
    echo -e "${GREEN}✅ Script permissions set successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Scripts directory not found, skipping...${NC}"
fi

# Secure environment file
echo -e "${BLUE}🔐 Securing environment file...${NC}"
if [ -f "$APP_DIR/.env" ]; then
    chmod 600 $APP_DIR/.env
    chown $APP_USER:$APP_GROUP $APP_DIR/.env
    echo -e "${GREEN}✅ Environment file secured (600 permissions)${NC}"
else
    echo -e "${YELLOW}⚠️  .env file not found, will need to be secured after creation${NC}"
fi

# Create and secure log directory
echo -e "${BLUE}📝 Setting up log directory...${NC}"
mkdir -p $APP_DIR/logs
chown $APP_USER:$APP_GROUP $APP_DIR/logs
chmod 755 $APP_DIR/logs
echo -e "${GREEN}✅ Log directory configured${NC}"

# Create and secure upload directory
echo -e "${BLUE}📤 Setting up upload directory...${NC}"
mkdir -p $APP_DIR/uploads/{products,categories,banners,documents}
chown -R $APP_USER:$APP_GROUP $APP_DIR/uploads
chmod -R 755 $APP_DIR/uploads
echo -e "${GREEN}✅ Upload directory configured${NC}"

# Create and secure backup directory
echo -e "${BLUE}💾 Setting up backup directory...${NC}"
mkdir -p $APP_DIR/backups
chown $APP_USER:$APP_GROUP $APP_DIR/backups
chmod 755 $APP_DIR/backups
echo -e "${GREEN}✅ Backup directory configured${NC}"

# Set special permissions for PM2 and Node.js files
echo -e "${BLUE}🔧 Setting special permissions for PM2 configuration...${NC}"
if [ -f "$APP_DIR/ecosystem.config.production.js" ]; then
    chmod 644 $APP_DIR/ecosystem.config.production.js
    chown $APP_USER:$APP_GROUP $APP_DIR/ecosystem.config.production.js
    echo -e "${GREEN}✅ PM2 configuration secured${NC}"
fi

# Set permissions for package.json and package-lock.json
if [ -f "$APP_DIR/package.json" ]; then
    chmod 644 $APP_DIR/package.json
    chown $APP_USER:$APP_GROUP $APP_DIR/package.json
fi

if [ -f "$APP_DIR/package-lock.json" ]; then
    chmod 644 $APP_DIR/package-lock.json
    chown $APP_USER:$APP_GROUP $APP_DIR/package-lock.json
fi

# Set permissions for Nginx configuration
echo -e "${BLUE}🌐 Setting permissions for Nginx configuration...${NC}"
if [ -f "$APP_DIR/nginx.production.conf" ]; then
    chmod 644 $APP_DIR/nginx.production.conf
    chown root:root $APP_DIR/nginx.production.conf
    echo -e "${GREEN}✅ Nginx configuration permissions set${NC}"
fi

# Verify permissions
echo -e "${CYAN}🔍 Verifying permissions...${NC}"
echo

# Check application directory
APP_DIR_PERMS=$(stat -c "%a" $APP_DIR)
APP_DIR_OWNER=$(stat -c "%U:%G" $APP_DIR)
echo -e "Application directory: ${GREEN}$APP_DIR${NC} (${BLUE}$APP_DIR_PERMS${NC}, ${YELLOW}$APP_DIR_OWNER${NC})"

# Check environment file
if [ -f "$APP_DIR/.env" ]; then
    ENV_PERMS=$(stat -c "%a" $APP_DIR/.env)
    ENV_OWNER=$(stat -c "%U:%G" $APP_DIR/.env)
    echo -e "Environment file: ${GREEN}.env${NC} (${BLUE}$ENV_PERMS${NC}, ${YELLOW}$ENV_OWNER${NC})"
fi

# Check key directories
for dir in "logs" "uploads" "backups" "dist" "server"; do
    if [ -d "$APP_DIR/$dir" ]; then
        DIR_PERMS=$(stat -c "%a" $APP_DIR/$dir)
        DIR_OWNER=$(stat -c "%U:%G" $APP_DIR/$dir)
        echo -e "$dir directory: ${GREEN}$dir/${NC} (${BLUE}$DIR_PERMS${NC}, ${YELLOW}$DIR_OWNER${NC})"
    fi
done

echo
echo -e "${GREEN}✅ File permissions setup complete!${NC}"
echo
echo -e "${CYAN}📋 Summary:${NC}"
echo -e "   - Application directory: ${GREEN}$APP_DIR${NC} (755, $APP_USER:$APP_GROUP)"
echo -e "   - Environment file: ${GREEN}.env${NC} (600, $APP_USER:$APP_GROUP)"
echo -e "   - Log directory: ${GREEN}logs/${NC} (755, $APP_USER:$APP_GROUP)"
echo -e "   - Upload directory: ${GREEN}uploads/${NC} (755, $APP_USER:$APP_GROUP)"
echo -e "   - Backup directory: ${GREEN}backups/${NC} (755, $APP_USER:$APP_GROUP)"
echo
echo -e "${YELLOW}📝 Next steps:${NC}"
echo -e "   1. Switch to application user: ${BLUE}sudo su - $APP_USER${NC}"
echo -e "   2. Navigate to app directory: ${BLUE}cd $APP_DIR${NC}"
echo -e "   3. Install dependencies: ${BLUE}npm install --production${NC}"
echo -e "   4. Start application: ${BLUE}pm2 start ecosystem.config.production.js${NC}"
echo
