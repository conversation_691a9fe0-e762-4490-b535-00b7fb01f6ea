const crypto = require('crypto');
const { User, Role, AuditLog } = require('../models');
const emailService = require('./emailService');

/**
 * Role-Based Access Control Service
 * Handles user role management, invitations, and permissions
 */
class RBACService {
  /**
   * Create a manager invitation
   * @param {Object} invitationData - Invitation details
   * @param {Object} adminUser - Admin user creating the invitation
   */
  static async createManagerInvitation(invitationData, adminUser) {
    const { email, firstName, lastName, phone } = invitationData;

    try {
      // Check if user already exists
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Get manager role
      const managerRole = await Role.getByName('manager');
      if (!managerRole) {
        throw new Error('Manager role not found');
      }

      // Generate temporary password and verification token
      const tempPassword = crypto.randomBytes(12).toString('hex');
      const verificationToken = crypto.randomBytes(32).toString('hex');

      // Create user with manager role
      const newUser = await User.create({
        email,
        firstName,
        lastName,
        phone,
        password: tempPassword,
        roleId: managerRole.id,
        isVerified: false,
        verificationToken,
        isActive: true
      });

      // Send invitation email
      await this.sendManagerInvitationEmail({
        email,
        firstName,
        lastName,
        tempPassword,
        verificationToken,
        invitedBy: adminUser.getFullName()
      });

      // Log the invitation
      await AuditLog.logCreate(
        adminUser.id,
        'USER',
        newUser.id,
        {
          email: newUser.email,
          firstName: newUser.firstName,
          lastName: newUser.lastName,
          role: 'manager'
        },
        {
          severity: 'high',
          metadata: {
            action: 'MANAGER_INVITATION_SENT',
            invitationType: 'manager',
            invitedBy: adminUser.id
          }
        }
      );

      return {
        success: true,
        user: {
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.firstName,
          lastName: newUser.lastName,
          role: 'manager'
        },
        message: 'Manager invitation sent successfully'
      };

    } catch (error) {
      console.error('Manager invitation error:', error);
      throw error;
    }
  }

  /**
   * Send manager invitation email
   */
  static async sendManagerInvitationEmail(invitationData) {
    const { email, firstName, lastName, tempPassword, verificationToken, invitedBy } = invitationData;
    
    const setupUrl = `${process.env.FRONTEND_URL}/setup-account?token=${verificationToken}`;

    const mailOptions = {
      to: email,
      subject: '🎉 Welcome to Nirvana Organics Management Team',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to the Team!</h1>
          </div>

          <div style="padding: 40px; background: #f9fafb;">
            <h2 style="color: #16a34a; margin-bottom: 20px;">Hello ${firstName},</h2>

            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              You've been invited by <strong>${invitedBy}</strong> to join the Nirvana Organics management team. 
              As a manager, you'll have access to order management, customer service functions, and product information.
            </p>

            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #374151; margin-top: 0;">Your Account Details</h3>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Role:</strong> Manager</p>
              <p><strong>Temporary Password:</strong> <code style="background: #f3f4f6; padding: 4px 8px; border-radius: 4px;">${tempPassword}</code></p>
            </div>

            <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <p style="color: #92400e; margin: 0; font-size: 14px;">
                <strong>Important:</strong> Please set up your account within 24 hours. You'll be required to change your password on first login.
              </p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${setupUrl}"
                 style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                Set Up Your Account
              </a>
            </div>

            <h3 style="color: #16a34a; margin-top: 30px;">Your Manager Permissions Include:</h3>
            <ul style="color: #374151; line-height: 1.8;">
              <li>View all product information (read-only)</li>
              <li>Manage customer orders and processing</li>
              <li>Handle customer service inquiries</li>
              <li>Access order management tools</li>
              <li>View customer information</li>
              <li>Generate basic reports</li>
            </ul>

            <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
              If you have any questions, please contact your administrator or reply to this email.
            </p>
          </div>

          <div style="background: #374151; padding: 20px; text-align: center;">
            <p style="color: #9ca3af; margin: 0; font-size: 14px;">
              © 2025 Nirvana Organics. All rights reserved.
            </p>
          </div>
        </div>
      `,
      text: `
        Welcome to Nirvana Organics Management Team!

        Hello ${firstName},

        You've been invited by ${invitedBy} to join the Nirvana Organics management team.

        Your Account Details:
        - Email: ${email}
        - Role: Manager
        - Temporary Password: ${tempPassword}

        Please set up your account: ${setupUrl}

        Important: Please set up your account within 24 hours. You'll be required to change your password on first login.

        Your Manager Permissions Include:
        - View all product information (read-only)
        - Manage customer orders and processing
        - Handle customer service inquiries
        - Access order management tools
        - View customer information
        - Generate basic reports

        If you have any questions, please contact your administrator.

        © 2025 Nirvana Organics. All rights reserved.
      `
    };

    return await emailService.sendEmail(mailOptions, emailService.EMAIL_TYPES.SYSTEM);
  }

  /**
   * Verify and activate manager account
   */
  static async activateManagerAccount(token, newPassword) {
    try {
      const user = await User.findOne({
        where: { verificationToken: token, isVerified: false },
        include: [{ model: Role, as: 'Role' }]
      });

      if (!user) {
        throw new Error('Invalid or expired verification token');
      }

      // Update user with new password and verify account
      await user.update({
        password: newPassword, // Will be hashed by the model hook
        isVerified: true,
        verificationToken: null
      });

      // Log account activation
      await AuditLog.logUserAction(
        user.id,
        'ACCOUNT_ACTIVATED',
        'USER',
        user.id,
        {
          severity: 'medium',
          metadata: {
            activationType: 'manager_invitation',
            role: user.Role?.name
          }
        }
      );

      return {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.Role?.name
        },
        message: 'Account activated successfully'
      };

    } catch (error) {
      console.error('Account activation error:', error);
      throw error;
    }
  }

  /**
   * Get user permissions summary
   */
  static async getUserPermissions(userId) {
    try {
      const user = await User.findByPk(userId, {
        include: [{ model: Role, as: 'Role' }]
      });

      if (!user || !user.Role) {
        throw new Error('User or role not found');
      }

      return {
        userId: user.id,
        email: user.email,
        role: user.Role.name,
        permissions: user.Role.permissions,
        displayName: user.Role.displayName
      };

    } catch (error) {
      console.error('Get permissions error:', error);
      throw error;
    }
  }

  /**
   * Update user role (admin only)
   */
  static async updateUserRole(userId, newRoleName, adminUser) {
    try {
      const user = await User.findByPk(userId, {
        include: [{ model: Role, as: 'Role' }]
      });

      if (!user) {
        throw new Error('User not found');
      }

      const newRole = await Role.getByName(newRoleName);
      if (!newRole) {
        throw new Error('Role not found');
      }

      const oldRole = user.Role;
      
      // Update user role
      await user.update({ roleId: newRole.id });

      // Log role change
      await AuditLog.logUpdate(
        adminUser.id,
        'USER',
        user.id,
        { role: oldRole?.name },
        { role: newRole.name },
        {
          severity: 'high',
          metadata: {
            action: 'ROLE_CHANGE',
            changedBy: adminUser.id,
            oldRoleId: oldRole?.id,
            newRoleId: newRole.id
          }
        }
      );

      return {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          oldRole: oldRole?.name,
          newRole: newRole.name
        },
        message: 'User role updated successfully'
      };

    } catch (error) {
      console.error('Update role error:', error);
      throw error;
    }
  }
}

module.exports = RBACService;
