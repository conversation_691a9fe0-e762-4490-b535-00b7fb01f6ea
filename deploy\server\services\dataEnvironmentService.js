const { DataEnvironment, MockData, User, Product, Order, Category } = require('../models');
const { Op } = require('sequelize');

class DataEnvironmentService {
  /**
   * Get current data mode for a user
   */
  async getCurrentMode(userId, sessionId = null) {
    try {
      return await DataEnvironment.getCurrentMode(userId, sessionId);
    } catch (error) {
      console.error('Error getting current data mode:', error);
      return 'real'; // Default to real data
    }
  }

  /**
   * Set data mode for a user
   */
  async setDataMode(userId, mode, sessionId = null) {
    try {
      if (!['mock', 'real'].includes(mode)) {
        throw new Error('Invalid data mode. Must be "mock" or "real"');
      }

      const environment = await DataEnvironment.setMode(userId, mode, sessionId);
      return {
        success: true,
        mode: environment.mode,
        lastSwitched: environment.lastSwitched
      };
    } catch (error) {
      console.error('Error setting data mode:', error);
      throw error;
    }
  }

  /**
   * Toggle between mock and real data modes
   */
  async toggleDataMode(userId, sessionId = null) {
    try {
      const currentMode = await this.getCurrentMode(userId, sessionId);
      const newMode = currentMode === 'mock' ? 'real' : 'mock';
      return await this.setDataMode(userId, newMode, sessionId);
    } catch (error) {
      console.error('Error toggling data mode:', error);
      throw error;
    }
  }

  /**
   * Get data based on current mode
   */
  async getData(userId, entityType, options = {}) {
    try {
      const mode = await this.getCurrentMode(userId, options.sessionId);
      
      if (mode === 'mock') {
        return await this.getMockData(entityType, options);
      } else {
        return await this.getRealData(entityType, options);
      }
    } catch (error) {
      console.error('Error getting data:', error);
      throw error;
    }
  }

  /**
   * Get mock data for an entity type
   */
  async getMockData(entityType, options = {}) {
    try {
      const mockEntries = await MockData.getByEntityType(entityType, true);
      return mockEntries.map(entry => ({
        ...entry.data,
        _isMockData: true,
        _mockId: entry.mockId
      }));
    } catch (error) {
      console.error('Error getting mock data:', error);
      return [];
    }
  }

  /**
   * Get real data for an entity type
   */
  async getRealData(entityType, options = {}) {
    try {
      let Model;
      switch (entityType.toLowerCase()) {
        case 'product':
          Model = Product;
          break;
        case 'order':
          Model = Order;
          break;
        case 'category':
          Model = Category;
          break;
        case 'user':
          Model = User;
          break;
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }

      const data = await Model.findAll({
        limit: options.limit || 100,
        offset: options.offset || 0,
        order: options.order || [['createdAt', 'DESC']]
      });

      return data.map(item => ({
        ...item.toJSON(),
        _isMockData: false
      }));
    } catch (error) {
      console.error('Error getting real data:', error);
      return [];
    }
  }

  /**
   * Create mock data entry
   */
  async createMockData(entityType, data, createdBy, options = {}) {
    try {
      const mockEntry = await MockData.createMockEntry(entityType, data, createdBy, options);
      return {
        success: true,
        mockId: mockEntry.mockId,
        data: mockEntry.data
      };
    } catch (error) {
      console.error('Error creating mock data:', error);
      throw error;
    }
  }

  /**
   * Update mock data entry
   */
  async updateMockData(mockId, data, options = {}) {
    try {
      const mockEntry = await MockData.findOne({ where: { mockId } });
      if (!mockEntry) {
        throw new Error('Mock data entry not found');
      }

      mockEntry.data = { ...mockEntry.data, ...data };
      if (options.description) mockEntry.description = options.description;
      if (options.tags) mockEntry.tags = options.tags;
      if (options.metadata) mockEntry.metadata = { ...mockEntry.metadata, ...options.metadata };

      await mockEntry.save();
      return {
        success: true,
        mockId: mockEntry.mockId,
        data: mockEntry.data
      };
    } catch (error) {
      console.error('Error updating mock data:', error);
      throw error;
    }
  }

  /**
   * Delete mock data entry
   */
  async deleteMockData(mockId) {
    try {
      const result = await MockData.destroy({ where: { mockId } });
      return {
        success: result > 0,
        deleted: result
      };
    } catch (error) {
      console.error('Error deleting mock data:', error);
      throw error;
    }
  }

  /**
   * Clear all mock data for an entity type
   */
  async clearMockData(entityType, createdBy = null) {
    try {
      const result = await MockData.clearByEntityType(entityType, createdBy);
      return {
        success: true,
        deleted: result
      };
    } catch (error) {
      console.error('Error clearing mock data:', error);
      throw error;
    }
  }

  /**
   * Clear all mock data
   */
  async clearAllMockData(createdBy = null) {
    try {
      const result = await MockData.clearAll(createdBy);
      return {
        success: true,
        deleted: result
      };
    } catch (error) {
      console.error('Error clearing all mock data:', error);
      throw error;
    }
  }

  /**
   * Get mock data statistics
   */
  async getMockDataStats() {
    try {
      const stats = await MockData.getStats();
      return stats;
    } catch (error) {
      console.error('Error getting mock data stats:', error);
      return [];
    }
  }

  /**
   * Generate sample mock data for testing
   */
  async generateSampleMockData(createdBy) {
    try {
      const sampleData = {
        products: [
          {
            name: 'Mock Product 1',
            slug: 'mock-product-1',
            description: 'This is a mock product for testing',
            price: 29.99,
            sku: 'MOCK-001',
            isActive: true,
            stock: 100
          },
          {
            name: 'Mock Product 2',
            slug: 'mock-product-2',
            description: 'Another mock product for testing',
            price: 49.99,
            sku: 'MOCK-002',
            isActive: true,
            stock: 50
          }
        ],
        orders: [
          {
            orderNumber: 'MOCK-ORDER-001',
            status: 'completed',
            total: 79.98,
            items: [
              { productId: 1, quantity: 2, price: 29.99 },
              { productId: 2, quantity: 1, price: 49.99 }
            ]
          }
        ]
      };

      const results = [];
      
      // Create mock products
      for (const product of sampleData.products) {
        const result = await this.createMockData('product', product, createdBy, {
          description: 'Sample mock product data',
          tags: ['sample', 'testing']
        });
        results.push(result);
      }

      // Create mock orders
      for (const order of sampleData.orders) {
        const result = await this.createMockData('order', order, createdBy, {
          description: 'Sample mock order data',
          tags: ['sample', 'testing']
        });
        results.push(result);
      }

      return {
        success: true,
        created: results.length,
        results
      };
    } catch (error) {
      console.error('Error generating sample mock data:', error);
      throw error;
    }
  }
}

module.exports = new DataEnvironmentService();
