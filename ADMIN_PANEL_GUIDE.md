# Nirvana Organics E-commerce - Admin Panel Guide

This guide provides comprehensive instructions for using the Nirvana Organics admin panel to manage your e-commerce platform.

## 🎯 Admin Panel Overview

The admin panel is a powerful web-based interface for managing all aspects of your e-commerce platform, accessible at:
- **Testing**: `https://test.shopnirvanaorganics.com/admin`
- **Production**: `https://shopnirvanaorganics.com/admin`

## 🔐 Access and Authentication

### Initial Admin Setup

After deployment, create the default admin user:
```bash
cd /var/www/nirvana-backend
node scripts/create-default-admin.js
```

This creates an admin user with:
- **Email**: `<EMAIL>`
- **Password**: `Admin123!` (change immediately after first login)

### First Login

1. Navigate to the admin panel URL
2. Enter the default credentials
3. **Immediately change the password** for security
4. Update your profile information

### Password Security

- Use strong passwords (minimum 8 characters)
- Include uppercase, lowercase, numbers, and symbols
- Change passwords regularly
- Never share admin credentials

## 📊 Dashboard Overview

The admin dashboard provides:
- **Sales Overview**: Revenue, orders, and customer metrics
- **Recent Activity**: Latest orders and customer registrations
- **Quick Actions**: Common administrative tasks
- **System Status**: Application health and performance
- **Alerts**: Important notifications and warnings

### Key Metrics Displayed
- Total Revenue (daily, weekly, monthly)
- Order Count and Status
- Customer Registration Count
- Product Inventory Levels
- Payment Processing Status

## 🛍️ Product Management

### Adding New Products

1. **Navigate to Products** → **Add New Product**
2. **Fill Required Information**:
   - Product Name
   - Description
   - Price
   - Category
   - SKU (Stock Keeping Unit)
   - Inventory Quantity

3. **Upload Product Images**:
   - Primary image (required)
   - Additional images (optional)
   - Supported formats: JPG, PNG, GIF
   - Recommended size: 800x800px

4. **Set Product Details**:
   - Weight and dimensions
   - Organic certification status
   - Ingredients/composition
   - Usage instructions

5. **Configure Availability**:
   - Stock status (In Stock/Out of Stock)
   - Inventory tracking
   - Low stock alerts

### Managing Existing Products

#### Editing Products
- Click on any product to edit details
- Update pricing, descriptions, or images
- Modify inventory levels
- Change product status (active/inactive)

#### Bulk Operations
- Select multiple products for bulk actions
- Update categories or prices in bulk
- Export product data to CSV
- Import products from CSV files

#### Inventory Management
- Track stock levels in real-time
- Set low stock alerts
- Manage product variants (size, color, etc.)
- Handle backorders and pre-orders

### Product Categories

#### Creating Categories
1. **Navigate to Products** → **Categories**
2. **Add New Category**:
   - Category Name
   - Description
   - Parent Category (for subcategories)
   - Category Image
   - SEO Settings

#### Managing Category Hierarchy
- Create main categories (e.g., "Supplements", "Skincare")
- Add subcategories (e.g., "Vitamins", "Moisturizers")
- Organize products within categories
- Set category display order

## 👥 Customer Management

### Customer Overview
- View all registered customers
- Search and filter customers
- View customer order history
- Manage customer accounts

### Customer Information
- Personal details (name, email, phone)
- Shipping and billing addresses
- Order history and preferences
- Account status and notes

### Customer Support
- View customer inquiries
- Respond to support tickets
- Track customer satisfaction
- Manage returns and refunds

## 📦 Order Management

### Order Processing Workflow

1. **New Orders**: Automatically appear in the orders list
2. **Order Review**: Verify order details and payment
3. **Processing**: Prepare items for shipment
4. **Shipped**: Update with tracking information
5. **Delivered**: Mark as completed

### Order Details
- Customer information
- Ordered items and quantities
- Payment status and method
- Shipping address and method
- Order notes and special instructions

### Order Status Management
- **Pending**: Awaiting payment confirmation
- **Processing**: Payment confirmed, preparing shipment
- **Shipped**: Order dispatched with tracking
- **Delivered**: Order received by customer
- **Cancelled**: Order cancelled (with reason)
- **Refunded**: Payment returned to customer

### Shipping Management
- Print shipping labels
- Update tracking information
- Manage shipping methods and rates
- Handle shipping exceptions

## 💳 Payment Management

### Payment Overview
- View all payment transactions
- Monitor payment status
- Handle payment disputes
- Process refunds

### Square Integration
- Real-time payment processing
- Automatic payment confirmation
- Secure payment handling
- Transaction reporting

### Refund Processing
1. Navigate to the order requiring refund
2. Select refund amount (full or partial)
3. Provide refund reason
4. Process refund through Square
5. Update order status

## 📈 Analytics and Reporting

### Sales Reports
- Daily, weekly, monthly revenue
- Product performance analysis
- Customer acquisition metrics
- Payment method breakdown

### Inventory Reports
- Stock levels and movements
- Low stock alerts
- Product performance
- Category analysis

### Customer Reports
- Customer demographics
- Purchase behavior analysis
- Customer lifetime value
- Retention metrics

### Export Options
- Export reports to CSV/Excel
- Schedule automated reports
- Custom date range selection
- Filter by various criteria

## ⚙️ Settings and Configuration

### General Settings
- Store information and branding
- Contact details and addresses
- Business hours and policies
- Currency and tax settings

### Email Configuration
- Email templates customization
- SMTP settings verification
- Automated email triggers
- Email delivery monitoring

### Payment Settings
- Square payment configuration
- Payment method availability
- Tax calculation settings
- Shipping rate configuration

### Security Settings
- Admin user management
- Password policies
- Session timeout settings
- Access logging

## 🔧 System Administration

### User Management
- Create additional admin users
- Assign roles and permissions
- Manage user access levels
- Monitor user activity

### System Monitoring
- Application performance metrics
- Database status and health
- Error logs and debugging
- System resource usage

### Backup and Maintenance
- Database backup scheduling
- System update notifications
- Maintenance mode activation
- Data export and import

## 🚨 Troubleshooting Common Issues

### Login Issues
- **Forgot Password**: Use password reset feature
- **Account Locked**: Contact system administrator
- **Session Expired**: Clear browser cache and retry

### Product Upload Issues
- **Image Upload Fails**: Check file size and format
- **Data Not Saving**: Verify required fields are filled
- **Category Not Showing**: Refresh page or check category status

### Order Processing Issues
- **Payment Not Confirmed**: Check Square dashboard
- **Shipping Label Error**: Verify shipping address
- **Inventory Mismatch**: Reconcile stock levels

### Performance Issues
- **Slow Loading**: Clear browser cache
- **Timeout Errors**: Check internet connection
- **Data Not Updating**: Refresh page or logout/login

## 📱 Mobile Access

The admin panel is responsive and works on mobile devices:
- Access from smartphones and tablets
- Touch-friendly interface
- Essential functions available on mobile
- Optimized for various screen sizes

### Mobile Limitations
- Complex data entry better on desktop
- File uploads may be slower
- Some advanced features desktop-only
- Printing functions may not work

## 🔒 Security Best Practices

### Admin Account Security
- Use strong, unique passwords
- Enable two-factor authentication (if available)
- Log out when finished
- Don't share admin credentials

### Data Protection
- Regular password changes
- Monitor access logs
- Report suspicious activity
- Keep software updated

### Safe Practices
- Always verify customer information
- Double-check refund amounts
- Backup important data regularly
- Test changes in staging environment first

## 📞 Support and Help

### Getting Help
- Check this guide for common tasks
- Review error messages carefully
- Check system logs for technical issues
- Contact technical support if needed

### Training Resources
- Video tutorials (if available)
- User documentation
- Best practices guides
- Community forums

### Reporting Issues
- Describe the problem clearly
- Include error messages
- Note when the issue started
- Provide steps to reproduce

---

**Admin Panel Version**: 1.0.0
**Last Updated**: ${new Date().toISOString().split('T')[0]}
**Support**: Nirvana Organics Development Team
