#!/usr/bin/env node

/**
 * Google OAuth Setup Guide
 * This script provides instructions for configuring Google OAuth correctly
 */

require('dotenv').config();

console.log('🔧 Google OAuth Configuration Guide\n');

const clientId = process.env.GOOGLE_CLIENT_ID;
const callbackUrl = process.env.GOOGLE_OAUTH_CALLBACK_URL || `${process.env.BACKEND_URL || 'http://localhost:5000'}/api/auth/google/callback`;

console.log('📋 Current Configuration:');
console.log(`   Client ID: ${clientId}`);
console.log(`   Callback URL: ${callbackUrl}\n`);

console.log('🔧 To fix the "redirect_uri_mismatch" error, follow these steps:\n');

console.log('1️⃣ Go to Google Cloud Console:');
console.log('   https://console.cloud.google.com/\n');

console.log('2️⃣ Navigate to APIs & Services > Credentials\n');

console.log('3️⃣ Find your OAuth 2.0 Client ID:');
console.log(`   ${clientId}\n`);

console.log('4️⃣ Click "Edit" on your OAuth client\n');

console.log('5️⃣ In "Authorized redirect URIs", add these URLs:');
console.log('   📍 Development:');
console.log('      http://localhost:5000/api/auth/google/callback');
console.log('      http://localhost:5001/auth/google/callback');
console.log('   📍 Production:');
console.log('      https://shopnirvanaorganics.com/api/auth/google/callback');
console.log('      https://www.shopnirvanaorganics.com/api/auth/google/callback\n');

console.log('6️⃣ Click "Save"\n');

console.log('7️⃣ Wait a few minutes for changes to propagate\n');

console.log('8️⃣ Test the OAuth flow again\n');

console.log('💡 Alternative: If you want to use a different callback URL,');
console.log('   update the GOOGLE_OAUTH_CALLBACK_URL in your .env file\n');

console.log('🔍 Current redirect URIs that should be configured:');
const redirectUris = [
  'http://localhost:5000/api/auth/google/callback',
  'http://localhost:5001/auth/google/callback',
  'https://shopnirvanaorganics.com/api/auth/google/callback',
  'https://www.shopnirvanaorganics.com/api/auth/google/callback'
];

redirectUris.forEach((uri, index) => {
  console.log(`   ${index + 1}. ${uri}`);
});

console.log('\n✅ After configuration, test with:');
console.log(`   http://localhost:5000/api/auth/google/login`);
console.log(`   http://localhost:5001/auth/google/login (test server)`);

console.log('\n🚨 Common Issues:');
console.log('   • Make sure URLs match exactly (including http/https)');
console.log('   • No trailing slashes in redirect URIs');
console.log('   • Changes can take up to 5 minutes to propagate');
console.log('   • Clear browser cache if issues persist');

console.log('\n📞 Need help? Check the Google OAuth documentation:');
console.log('   https://developers.google.com/identity/protocols/oauth2/web-server');
