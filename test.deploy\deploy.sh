#!/bin/bash

# Deployment Script for Testing Environment
# test.shopnirvanaorganics.com
# Version 2.0 - Updated with Social Media Integration and Complete Application Build

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="nirvana-test"
PROJECT_PATH="/var/www/nirvana-test"
NGINX_CONFIG="/etc/nginx/sites-available/test.shopnirvanaorganics.com"
NGINX_ENABLED="/etc/nginx/sites-enabled/test.shopnirvanaorganics.com"
DOMAIN="test.shopnirvanaorganics.com"
NODE_VERSION="18"

echo -e "${BLUE}🚀 Deploying Nirvana Organics Testing Environment${NC}"
echo "=================================================="

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# Install system dependencies
install_dependencies() {
    log_info "Installing system dependencies..."
    
    # Update package list
    apt update
    
    # Install required packages
    apt install -y curl wget git nginx mysql-server redis-server build-essential \
        python3-dev python3-pip certbot python3-certbot-nginx fail2ban \
        htop iotop nethogs unzip
    
    # Install Node.js
    if ! command -v node &> /dev/null || [[ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt $NODE_VERSION ]]; then
        log_info "Installing Node.js $NODE_VERSION..."
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
        apt install -y nodejs
    fi
    
    # Install PM2 globally
    npm install -g pm2
    
    log_info "System dependencies installed"
}

# Create project user and directories
setup_project_structure() {
    log_info "Setting up project structure..."
    
    # Create nirvana user if it doesn't exist
    if ! id "nirvana" &>/dev/null; then
        useradd -m -s /bin/bash nirvana
        usermod -aG www-data nirvana
    fi
    
    # Create project directories
    mkdir -p $PROJECT_PATH/{server,dist,logs,uploads,backups,scripts}
    mkdir -p $PROJECT_PATH/server/{models,routes,services,middleware,utils,migrations}
    
    # Set ownership and permissions
    chown -R nirvana:www-data $PROJECT_PATH
    chmod -R 755 $PROJECT_PATH
    chmod -R 775 $PROJECT_PATH/{logs,uploads,backups}
    
    log_info "Project structure created"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    # Secure MySQL installation (basic setup)
    mysql_secure_installation --use-default
    
    # Run database setup script
    if [ -f "./database-setup.sql" ]; then
        mysql -u root -p < ./database-setup.sql
        log_info "Database setup completed"
    else
        log_warning "Database setup script not found. Please run manually."
    fi
}

# Clone or update repository
setup_repository() {
    log_info "Setting up repository..."
    
    if [ -d "$PROJECT_PATH/.git" ]; then
        log_info "Repository exists, pulling latest changes..."
        cd $PROJECT_PATH
        sudo -u nirvana git pull origin testing
    else
        log_info "Cloning repository..."
        sudo -u nirvana git clone -b testing https://github.com/your-username/nirvana-organics.git $PROJECT_PATH
        cd $PROJECT_PATH
    fi
    
    # Set proper ownership
    chown -R nirvana:www-data $PROJECT_PATH
    
    log_info "Repository setup completed"
}

# Install application dependencies
install_app_dependencies() {
    log_info "Installing application dependencies..."
    
    cd $PROJECT_PATH
    
    # Install backend dependencies
    sudo -u nirvana npm install --production
    
    # Install frontend dependencies and build
    sudo -u nirvana npm run build:test
    
    log_info "Application dependencies installed"
}

# Setup environment configuration
setup_environment() {
    log_info "Setting up environment configuration..."
    
    # Copy environment file
    if [ -f "./test.deploy/.env.testing" ]; then
        cp ./test.deploy/.env.testing $PROJECT_PATH/.env
        chown nirvana:www-data $PROJECT_PATH/.env
        chmod 600 $PROJECT_PATH/.env
        log_info "Environment file copied"
    else
        log_error "Environment file not found!"
        exit 1
    fi
    
    # Copy PM2 ecosystem file
    if [ -f "./test.deploy/ecosystem.config.js" ]; then
        cp ./test.deploy/ecosystem.config.js $PROJECT_PATH/
        chown nirvana:www-data $PROJECT_PATH/ecosystem.config.js
        log_info "PM2 ecosystem file copied"
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    cd $PROJECT_PATH
    sudo -u nirvana npm run migrate:test
    
    log_info "Database migrations completed"
}

# Setup Nginx
setup_nginx() {
    log_info "Setting up Nginx configuration..."
    
    # Copy Nginx configuration
    if [ -f "./test.deploy/nginx.conf" ]; then
        cp ./test.deploy/nginx.conf $NGINX_CONFIG
        
        # Create symbolic link to enable site
        ln -sf $NGINX_CONFIG $NGINX_ENABLED
        
        # Test Nginx configuration
        nginx -t
        
        if [ $? -eq 0 ]; then
            log_info "Nginx configuration is valid"
        else
            log_error "Nginx configuration is invalid"
            exit 1
        fi
    else
        log_error "Nginx configuration file not found!"
        exit 1
    fi
}

# Setup SSL certificates
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    if [ -f "./test.deploy/ssl-setup.sh" ]; then
        chmod +x ./test.deploy/ssl-setup.sh
        ./test.deploy/ssl-setup.sh
    else
        log_warning "SSL setup script not found. Please run manually."
    fi
}

# Start services
start_services() {
    log_info "Starting services..."
    
    cd $PROJECT_PATH
    
    # Start PM2 processes
    sudo -u nirvana pm2 start ecosystem.config.js --env testing
    
    # Save PM2 configuration
    sudo -u nirvana pm2 save
    
    # Setup PM2 startup script
    pm2 startup systemd -u nirvana --hp /home/<USER>
    
    # Reload Nginx
    systemctl reload nginx
    
    # Enable services to start on boot
    systemctl enable nginx
    systemctl enable mysql
    
    log_info "Services started successfully"
}

# Setup monitoring and logging
setup_monitoring() {
    log_info "Setting up monitoring and logging..."
    
    # Setup log rotation
    cat > /etc/logrotate.d/nirvana-test << 'EOF'
/var/www/nirvana-test/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 nirvana www-data
    postrotate
        pm2 reload all
    endscript
}
EOF
    
    # Setup system monitoring script
    cat > /usr/local/bin/monitor-nirvana-test.sh << 'EOF'
#!/bin/bash

# Check if PM2 processes are running
pm2 list | grep -q "online" || {
    echo "PM2 processes not running, restarting..."
    cd /var/www/nirvana-test
    sudo -u nirvana pm2 restart all
}

# Check disk space
DISK_USAGE=$(df /var/www/nirvana-test | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Warning: Disk usage is ${DISK_USAGE}%"
fi

# Check log file sizes
find /var/www/nirvana-test/logs -name "*.log" -size +100M -exec echo "Large log file: {}" \;
EOF
    
    chmod +x /usr/local/bin/monitor-nirvana-test.sh
    
    # Add monitoring to cron
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/monitor-nirvana-test.sh >> /var/log/nirvana-monitor.log 2>&1") | crontab -
    
    log_info "Monitoring setup completed"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check if services are running
    if systemctl is-active --quiet nginx; then
        log_info "✅ Nginx is running"
    else
        log_error "❌ Nginx is not running"
    fi
    
    if systemctl is-active --quiet mysql; then
        log_info "✅ MySQL is running"
    else
        log_error "❌ MySQL is not running"
    fi
    
    # Check PM2 processes
    cd $PROJECT_PATH
    if sudo -u nirvana pm2 list | grep -q "online"; then
        log_info "✅ PM2 processes are running"
    else
        log_error "❌ PM2 processes are not running"
    fi
    
    # Test API endpoint
    sleep 5
    if curl -f -s http://localhost:5001/api/health > /dev/null; then
        log_info "✅ API is responding"
    else
        log_warning "⚠️ API is not responding (may need time to start)"
    fi
    
    log_info "Deployment verification completed"
}

# Main deployment function
main() {
    log_info "Starting deployment process..."
    
    check_root
    install_dependencies
    setup_project_structure
    setup_database
    setup_repository
    install_app_dependencies
    setup_environment
    run_migrations
    setup_nginx
    setup_ssl
    start_services
    setup_monitoring
    verify_deployment
    
    echo ""
    echo -e "${GREEN}🎉 Deployment Completed Successfully!${NC}"
    echo "=================================================="
    echo -e "Testing URL: ${BLUE}https://$DOMAIN${NC}"
    echo -e "Admin Panel: ${BLUE}https://$DOMAIN/admin${NC}"
    echo -e "API Health: ${BLUE}https://$DOMAIN/api/health${NC}"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Update DNS records to point $DOMAIN to this server"
    echo "2. Test all functionality thoroughly"
    echo "3. Monitor logs: tail -f $PROJECT_PATH/logs/combined.log"
    echo "4. Check PM2 status: pm2 status"
    echo ""
}

# Run main function
main "$@"
