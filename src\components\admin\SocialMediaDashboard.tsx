import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { 
  PlusIcon, 
  ChartBarIcon, 
  CalendarIcon, 
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import SocialMediaAccounts from './SocialMediaAccounts';
import SocialMediaPosts from './SocialMediaPosts';
import SocialMediaAnalytics from './SocialMediaAnalytics';
import SocialMediaScheduler from './SocialMediaScheduler';
import LoadingSpinner from '../common/LoadingSpinner';

interface SocialMediaAccount {
  id: string;
  platform: string;
  accountName: string;
  accountHandle: string;
  profilePicture: string;
  followersCount: number;
  isActive: boolean;
  needsRefresh: boolean;
  isTokenExpired: boolean;
  recentMetrics?: {
    engagementRate: number;
    followersGrowth: number;
    lastUpdated: string;
  };
}

interface DashboardStats {
  totalAccounts: number;
  activeAccounts: number;
  totalFollowers: number;
  scheduledPosts: number;
  publishedToday: number;
  avgEngagementRate: number;
}

const SocialMediaDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [accounts, setAccounts] = useState<SocialMediaAccount[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'accounts', name: 'Accounts', icon: CogIcon },
    { id: 'posts', name: 'Posts', icon: CalendarIcon },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon },
    { id: 'scheduler', name: 'Scheduler', icon: ClockIcon }
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch accounts
      const accountsResponse = await fetch('/api/admin/social-media/accounts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!accountsResponse.ok) {
        throw new Error('Failed to fetch social media accounts');
      }

      const accountsData = await accountsResponse.json();
      setAccounts(accountsData.data || []);

      // Calculate stats
      const totalAccounts = accountsData.data?.length || 0;
      const activeAccounts = accountsData.data?.filter((acc: SocialMediaAccount) => acc.isActive).length || 0;
      const totalFollowers = accountsData.data?.reduce((sum: number, acc: SocialMediaAccount) => sum + acc.followersCount, 0) || 0;

      // Fetch additional stats
      const postsResponse = await fetch('/api/admin/social-media/posts?status=scheduled&limit=1', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const postsData = await postsResponse.json();
      const scheduledPosts = postsData.data?.pagination?.total || 0;

      // Fetch today's published posts
      const today = new Date().toISOString().split('T')[0];
      const todayPostsResponse = await fetch(`/api/admin/social-media/posts?status=published&startDate=${today}T00:00:00Z&endDate=${today}T23:59:59Z&limit=1`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const todayPostsData = await todayPostsResponse.json();
      const publishedToday = todayPostsData.data?.pagination?.total || 0;

      // Calculate average engagement rate
      const avgEngagementRate = accountsData.data?.reduce((sum: number, acc: SocialMediaAccount) => {
        return sum + (acc.recentMetrics?.engagementRate || 0);
      }, 0) / Math.max(activeAccounts, 1) || 0;

      setStats({
        totalAccounts,
        activeAccounts,
        totalFollowers,
        scheduledPosts,
        publishedToday,
        avgEngagementRate
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data');
      toast.error('Failed to load social media dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleAccountUpdate = () => {
    fetchDashboardData();
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CogIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Connected Accounts</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.activeAccounts || 0} / {stats?.totalAccounts || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Followers</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.totalFollowers?.toLocaleString() || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CalendarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Scheduled Posts</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.scheduledPosts || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-8 w-8 text-emerald-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Published Today</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.publishedToday || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Avg Engagement</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.avgEngagementRate?.toFixed(1) || 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Account Status */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Account Status</h3>
        </div>
        <div className="p-6">
          {accounts.length === 0 ? (
            <div className="text-center py-8">
              <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No social media accounts</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by connecting your first social media account.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setActiveTab('accounts')}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                >
                  <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                  Connect Account
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {accounts.map((account) => (
                <div key={account.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <img
                      src={account.profilePicture || '/images/default-avatar.png'}
                      alt={account.accountName}
                      className="h-10 w-10 rounded-full"
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {account.accountName}
                      </p>
                      <p className="text-sm text-gray-500">
                        {account.platform} • {account.followersCount.toLocaleString()} followers
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {account.isTokenExpired ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                        Token Expired
                      </span>
                    ) : account.needsRefresh ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <ClockIcon className="w-4 h-4 mr-1" />
                        Needs Refresh
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircleIcon className="w-4 h-4 mr-1" />
                        Active
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'accounts':
        return <SocialMediaAccounts onAccountUpdate={handleAccountUpdate} />;
      case 'posts':
        return <SocialMediaPosts />;
      case 'analytics':
        return <SocialMediaAnalytics />;
      case 'scheduler':
        return <SocialMediaScheduler />;
      default:
        return renderOverview();
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Dashboard</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
            <div className="mt-4">
              <button
                onClick={fetchDashboardData}
                className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-2xl font-bold text-gray-900">Social Media Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your social media accounts, posts, and analytics from one dashboard.
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white shadow">
        <div className="px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center py-4 px-1 border-b-2 font-medium text-sm
                    ${activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-4 sm:px-6 lg:px-8">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default SocialMediaDashboard;
