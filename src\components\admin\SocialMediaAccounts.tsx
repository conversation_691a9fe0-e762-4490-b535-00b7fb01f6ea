import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  PlusIcon,
  TrashIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../common/LoadingSpinner';
import Modal from '../common/Modal';

interface SocialMediaAccount {
  id: string;
  platform: string;
  accountName: string;
  accountHandle: string;
  profilePicture: string;
  followersCount: number;
  followingCount: number;
  postsCount: number;
  isActive: boolean;
  needsRefresh: boolean;
  isTokenExpired: boolean;
  lastSyncAt: string;
  recentMetrics?: {
    engagementRate: number;
    followersGrowth: number;
    lastUpdated: string;
  };
}

interface SocialMediaAccountsProps {
  onAccountUpdate: () => void;
}

const SocialMediaAccounts: React.FC<SocialMediaAccountsProps> = ({ onAccountUpdate }) => {
  const [accounts, setAccounts] = useState<SocialMediaAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null);
  const [refreshingAccount, setRefreshingAccount] = useState<string | null>(null);
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [showDisconnectModal, setShowDisconnectModal] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<SocialMediaAccount | null>(null);

  const platforms = [
    { id: 'facebook', name: 'Facebook', color: 'bg-blue-600', icon: '📘' },
    { id: 'twitter', name: 'Twitter/X', color: 'bg-blue-400', icon: '🐦' },
    { id: 'instagram', name: 'Instagram', color: 'bg-pink-600', icon: '📷' },
    { id: 'linkedin', name: 'LinkedIn', color: 'bg-blue-700', icon: '💼' },
    { id: 'youtube', name: 'YouTube', color: 'bg-red-600', icon: '📺' },
    { id: 'pinterest', name: 'Pinterest', color: 'bg-red-500', icon: '📌' },
    { id: 'tiktok', name: 'TikTok', color: 'bg-black', icon: '🎵' }
  ];

  useEffect(() => {
    fetchAccounts();
  }, []);

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/social-media/accounts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch accounts');
      }

      const data = await response.json();
      setAccounts(data.data || []);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      toast.error('Failed to load social media accounts');
    } finally {
      setLoading(false);
    }
  };

  const handleConnectAccount = async (platform: string) => {
    try {
      setConnectingPlatform(platform);
      
      const response = await fetch(`/api/admin/social-media/accounts/${platform}/connect`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to initiate connection');
      }

      const data = await response.json();
      
      // Open OAuth popup
      const popup = window.open(
        data.data.authUrl,
        'social-media-auth',
        'width=600,height=600,scrollbars=yes,resizable=yes'
      );

      // Listen for popup completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          setConnectingPlatform(null);
          // Refresh accounts after connection
          setTimeout(() => {
            fetchAccounts();
            onAccountUpdate();
          }, 1000);
        }
      }, 1000);

    } catch (error) {
      console.error('Error connecting account:', error);
      toast.error(`Failed to connect ${platform} account`);
      setConnectingPlatform(null);
    }
  };

  const handleRefreshAccount = async (account: SocialMediaAccount) => {
    try {
      setRefreshingAccount(account.id);
      
      const response = await fetch(`/api/admin/social-media/accounts/${account.id}/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to refresh account');
      }

      toast.success(`${account.platform} account refreshed successfully`);
      fetchAccounts();
      onAccountUpdate();
    } catch (error) {
      console.error('Error refreshing account:', error);
      toast.error(`Failed to refresh ${account.platform} account`);
    } finally {
      setRefreshingAccount(null);
    }
  };

  const handleDisconnectAccount = async () => {
    if (!selectedAccount) return;

    try {
      const response = await fetch(`/api/admin/social-media/accounts/${selectedAccount.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to disconnect account');
      }

      toast.success(`${selectedAccount.platform} account disconnected successfully`);
      setShowDisconnectModal(false);
      setSelectedAccount(null);
      fetchAccounts();
      onAccountUpdate();
    } catch (error) {
      console.error('Error disconnecting account:', error);
      toast.error(`Failed to disconnect ${selectedAccount.platform} account`);
    }
  };

  const getConnectedPlatforms = () => {
    return accounts.map(account => account.platform);
  };

  const getAvailablePlatforms = () => {
    const connectedPlatforms = getConnectedPlatforms();
    return platforms.filter(platform => !connectedPlatforms.includes(platform.id));
  };

  const getPlatformInfo = (platformId: string) => {
    return platforms.find(p => p.id === platformId) || { name: platformId, color: 'bg-gray-500', icon: '🔗' };
  };

  const getAccountStatus = (account: SocialMediaAccount) => {
    if (account.isTokenExpired) {
      return {
        label: 'Token Expired',
        color: 'bg-red-100 text-red-800',
        icon: ExclamationTriangleIcon
      };
    }
    if (account.needsRefresh) {
      return {
        label: 'Needs Refresh',
        color: 'bg-yellow-100 text-yellow-800',
        icon: ClockIcon
      };
    }
    return {
      label: 'Active',
      color: 'bg-green-100 text-green-800',
      icon: CheckCircleIcon
    };
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Connected Accounts</h2>
          <p className="text-sm text-gray-500">
            Manage your social media account connections and permissions.
          </p>
        </div>
        <button
          onClick={() => setShowConnectModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
        >
          <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
          Connect Account
        </button>
      </div>

      {/* Connected Accounts */}
      {accounts.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <div className="mx-auto h-12 w-12 text-gray-400 text-4xl">🔗</div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No connected accounts</h3>
          <p className="mt-1 text-sm text-gray-500">
            Connect your social media accounts to start managing your posts and analytics.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowConnectModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Connect Your First Account
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {accounts.map((account) => {
            const platformInfo = getPlatformInfo(account.platform);
            const status = getAccountStatus(account);
            const StatusIcon = status.icon;

            return (
              <div key={account.id} className="bg-white rounded-lg shadow border border-gray-200">
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-full ${platformInfo.color} flex items-center justify-center text-white text-lg`}>
                        {platformInfo.icon}
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">
                          {account.accountName}
                        </h3>
                        <p className="text-sm text-gray-500">
                          @{account.accountHandle || account.accountName}
                        </p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.color}`}>
                      <StatusIcon className="w-3 h-3 mr-1" />
                      {status.label}
                    </span>
                  </div>

                  <div className="mt-4 grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-lg font-semibold text-gray-900">
                        {account.followersCount.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">Followers</p>
                    </div>
                    <div>
                      <p className="text-lg font-semibold text-gray-900">
                        {account.followingCount.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">Following</p>
                    </div>
                    <div>
                      <p className="text-lg font-semibold text-gray-900">
                        {account.postsCount.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">Posts</p>
                    </div>
                  </div>

                  {account.recentMetrics && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-md">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Engagement Rate</span>
                        <span className="text-sm font-medium text-gray-900">
                          {account.recentMetrics.engagementRate.toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-sm text-gray-600">Growth</span>
                        <span className={`text-sm font-medium ${
                          account.recentMetrics.followersGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {account.recentMetrics.followersGrowth >= 0 ? '+' : ''}
                          {account.recentMetrics.followersGrowth}
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="mt-4 flex space-x-2">
                    <button
                      onClick={() => handleRefreshAccount(account)}
                      disabled={refreshingAccount === account.id}
                      className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      {refreshingAccount === account.id ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <ArrowPathIcon className="h-4 w-4 mr-1" />
                      )}
                      Refresh
                    </button>
                    <button
                      onClick={() => {
                        setSelectedAccount(account);
                        setShowDisconnectModal(true);
                      }}
                      className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>

                  <div className="mt-2 text-xs text-gray-500">
                    Last synced: {new Date(account.lastSyncAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Connect Account Modal */}
      <Modal
        isOpen={showConnectModal}
        onClose={() => setShowConnectModal(false)}
        title="Connect Social Media Account"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Choose a platform to connect your social media account.
          </p>
          
          <div className="grid grid-cols-1 gap-3">
            {getAvailablePlatforms().map((platform) => (
              <button
                key={platform.id}
                onClick={() => {
                  setShowConnectModal(false);
                  handleConnectAccount(platform.id);
                }}
                disabled={connectingPlatform === platform.id}
                className={`
                  flex items-center justify-between p-4 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50
                  ${connectingPlatform === platform.id ? 'bg-gray-50' : 'bg-white'}
                `}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full ${platform.color} flex items-center justify-center text-white`}>
                    {platform.icon}
                  </div>
                  <span className="font-medium text-gray-900">{platform.name}</span>
                </div>
                {connectingPlatform === platform.id ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <PlusIcon className="h-5 w-5 text-gray-400" />
                )}
              </button>
            ))}
          </div>

          {getAvailablePlatforms().length === 0 && (
            <p className="text-sm text-gray-500 text-center py-4">
              All supported platforms are already connected.
            </p>
          )}
        </div>
      </Modal>

      {/* Disconnect Account Modal */}
      <Modal
        isOpen={showDisconnectModal}
        onClose={() => {
          setShowDisconnectModal(false);
          setSelectedAccount(null);
        }}
        title="Disconnect Account"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Are you sure you want to disconnect your {selectedAccount?.platform} account? 
            This will remove access to post and collect analytics from this account.
          </p>
          
          <div className="flex space-x-3 justify-end">
            <button
              onClick={() => {
                setShowDisconnectModal(false);
                setSelectedAccount(null);
              }}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleDisconnectAccount}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
            >
              Disconnect
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SocialMediaAccounts;
