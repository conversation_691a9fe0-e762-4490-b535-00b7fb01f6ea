const nodemailer = require('nodemailer');
require('dotenv').config();

// Import performance monitor for email tracking
const performanceMonitor = require('../utils/performanceMonitor');

// Gmail/Google Workspace SMTP Configuration
const createGmailTransporter = () => {
  // For test environment, use a mock transporter
  if (process.env.NODE_ENV === 'test') {
    return nodemailer.createTransport({
      streamTransport: true,
      newline: 'unix',
      buffer: true
    });
  }

  const config = {
    service: 'gmail', // Use Gmail service for automatic configuration
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // Use STARTTLS
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS // App Password or OAuth2 token
    },
    // Enhanced settings for Gmail
    connectionTimeout: parseInt(process.env.EMAIL_CONNECTION_TIMEOUT) || 60000,
    greetingTimeout: 30000,
    socketTimeout: parseInt(process.env.EMAIL_SOCKET_TIMEOUT) || 60000,
    pool: process.env.EMAIL_POOL === 'true' || true,
    maxConnections: parseInt(process.env.EMAIL_MAX_CONNECTIONS) || 5,
    maxMessages: parseInt(process.env.EMAIL_MAX_MESSAGES) || 100,
    // TLS options optimized for Gmail
    tls: {
      rejectUnauthorized: true,
      minVersion: 'TLSv1.2'
    },
    // Debug logging in development
    debug: process.env.NODE_ENV === 'development',
    logger: process.env.NODE_ENV === 'development'
  };

  // OAuth2 configuration (if using OAuth2 instead of App Password)
  if (process.env.EMAIL_OAUTH2_CLIENT_ID && process.env.EMAIL_OAUTH2_CLIENT_SECRET) {
    config.auth = {
      type: 'OAuth2',
      user: process.env.EMAIL_USER,
      clientId: process.env.EMAIL_OAUTH2_CLIENT_ID,
      clientSecret: process.env.EMAIL_OAUTH2_CLIENT_SECRET,
      refreshToken: process.env.EMAIL_OAUTH2_REFRESH_TOKEN,
      accessToken: process.env.EMAIL_OAUTH2_ACCESS_TOKEN
    };
  }

  return nodemailer.createTransport(config);
};

// Create support email transporter
const createSupportTransporter = () => {
  // For test environment, use a mock transporter
  if (process.env.NODE_ENV === 'test') {
    return nodemailer.createTransport({
      streamTransport: true,
      newline: 'unix',
      buffer: true
    });
  }

  const config = {
    service: 'gmail',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_SUPPORT_USER || '<EMAIL>',
      pass: process.env.EMAIL_SUPPORT_PASS
    },
    pool: true,
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 1000,
    rateLimit: 5
  };

  return nodemailer.createTransport(config);
};

// Create orders email transporter
const createOrdersTransporter = () => {
  // For test environment, use a mock transporter
  if (process.env.NODE_ENV === 'test') {
    return nodemailer.createTransport({
      streamTransport: true,
      newline: 'unix',
      buffer: true
    });
  }

  const config = {
    service: 'gmail',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_ORDERS_USER || '<EMAIL>',
      pass: process.env.EMAIL_ORDERS_PASS
    },
    pool: true,
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 1000,
    rateLimit: 5
  };

  return nodemailer.createTransport(config);
};

// Create transporters
const transporter = createGmailTransporter();
const supportTransporter = createSupportTransporter();
const ordersTransporter = createOrdersTransporter();

// Verify Gmail transporter configuration with retry logic
const verifyGmailTransporter = async (retries = 3) => {
  // Skip verification in test environment
  if (process.env.NODE_ENV === 'test') {
    console.log('✅ Mock email transporter ready for testing');
    return true;
  }

  for (let i = 0; i < retries; i++) {
    try {
      await transporter.verify();
      console.log('✅ Gmail SMTP server is ready to send messages');
      console.log(`📧 Using email address: ${process.env.EMAIL_USER}`);
      return true;
    } catch (error) {
      console.error(`❌ Gmail SMTP verification failed (attempt ${i + 1}/${retries}):`, error.message);

      // Provide specific error guidance for Gmail
      if (error.message.includes('Invalid login')) {
        console.error('🔐 Gmail authentication failed. Please check:');
        console.error('   1. Email address is correct');
        console.error('   2. App Password is correct (not your regular Gmail password)');
        console.error('   3. 2-Factor Authentication is enabled on your Gmail account');
        console.error('   4. Less secure app access is disabled (use App Passwords instead)');
      } else if (error.message.includes('ECONNREFUSED')) {
        console.error('🌐 Connection refused. Please check your internet connection.');
      }

      if (i === retries - 1) {
        console.error('🚨 Gmail email service is not available. Please check your configuration.');
        return false;
      }
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

// Email type constants for routing
const EMAIL_TYPES = {
  ORDER: 'order',
  CUSTOMER_SERVICE: 'customer_service',
  SUPPORT: 'support',
  GENERAL: 'general',
  MARKETING: 'marketing',
  SYSTEM: 'system'
};

// Get appropriate sender email based on email type
const getSenderEmail = (emailType = EMAIL_TYPES.GENERAL) => {
  const fromName = process.env.EMAIL_FROM_NAME || 'Nirvana Organics';

  switch (emailType) {
    case EMAIL_TYPES.ORDER:
      return `"${fromName}" <${process.env.EMAIL_ORDERS || '<EMAIL>'}>`;
    case EMAIL_TYPES.CUSTOMER_SERVICE:
      return `"${fromName}" <${process.env.EMAIL_CUSTOMER_SERVICE || '<EMAIL>'}>`;
    case EMAIL_TYPES.SUPPORT:
      return `"${fromName}" <${process.env.EMAIL_SUPPORT || '<EMAIL>'}>`;
    case EMAIL_TYPES.MARKETING:
    case EMAIL_TYPES.GENERAL:
    case EMAIL_TYPES.SYSTEM:
    default:
      return `"${fromName}" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`;
  }
};

// Get appropriate recipient email for support communications
const getSupportRecipientEmail = () => {
  // Primary support email (new)
  const supportEmail = process.env.EMAIL_SUPPORT || '<EMAIL>';

  // Fallback to customer service email for backward compatibility
  const customerServiceEmail = process.env.EMAIL_CUSTOMER_SERVICE || '<EMAIL>';

  // Return primary support email, but include both for critical communications
  return {
    primary: supportEmail,
    fallback: customerServiceEmail,
    // For single recipient scenarios, use primary
    single: supportEmail,
    // For multiple recipients (like admin notifications), use both
    multiple: [supportEmail, customerServiceEmail]
  };
};

// Enhanced email sending function with Gmail-specific features and email type routing
const sendEmail = async (mailOptions, emailType = EMAIL_TYPES.GENERAL) => {
  try {
    // Set appropriate from address based on email type if not provided
    if (!mailOptions.from) {
      mailOptions.from = getSenderEmail(emailType);
    }

    // Add Gmail-specific headers for better deliverability
    mailOptions.headers = {
      ...mailOptions.headers,
      'X-Mailer': 'Nirvana Organics E-commerce Platform',
      'X-Priority': '3',
      'X-MSMail-Priority': 'Normal'
    };

    // Choose appropriate transporter based on email type
    let selectedTransporter;
    switch (emailType) {
      case EMAIL_TYPES.SUPPORT:
        selectedTransporter = supportTransporter;
        break;
      case EMAIL_TYPES.ORDER:
        selectedTransporter = ordersTransporter;
        break;
      default:
        selectedTransporter = transporter;
        break;
    }

    const info = await selectedTransporter.sendMail(mailOptions);

    console.log('✅ Email sent successfully:', {
      messageId: info.messageId,
      to: mailOptions.to,
      subject: mailOptions.subject,
      response: info.response
    });

    return {
      success: true,
      messageId: info.messageId,
      response: info.response
    };
  } catch (error) {
    console.error('❌ Email sending failed:', {
      error: error.message,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    // Gmail-specific error handling
    if (error.message.includes('Daily sending quota exceeded')) {
      console.error('📊 Gmail daily sending limit reached. Consider upgrading to Google Workspace.');
    } else if (error.message.includes('Invalid recipients')) {
      console.error('📧 Invalid recipient email address.');
    }

    throw error;
  }
};

// Verify support transporter configuration
const verifySupportTransporter = async (retries = 3) => {
  // Skip verification in test environment
  if (process.env.NODE_ENV === 'test') {
    console.log('✅ Mock support email transporter ready for testing');
    return true;
  }

  for (let i = 0; i < retries; i++) {
    try {
      await supportTransporter.verify();
      console.log('✅ Support Gmail SMTP server is ready to send messages');
      console.log(`📧 Using support email address: ${process.env.EMAIL_SUPPORT_USER}`);
      return true;
    } catch (error) {
      console.error(`❌ Support Gmail SMTP verification failed (attempt ${i + 1}/${retries}):`, error.message);

      // Provide specific error guidance for Gmail
      if (error.message.includes('Invalid login')) {
        console.error('🔐 Support Gmail authentication failed. Please check:');
        console.error('   1. Support email address is correct');
        console.error('   2. Support App Password is valid (16 characters)');
        console.error('   3. 2-Factor Authentication is enabled on support account');
        console.error('   4. Account is not suspended or locked');
      }

      if (i === retries - 1) {
        console.error('❌ Support Gmail SMTP verification failed after all retries');
        return false;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

// Verify orders transporter configuration
const verifyOrdersTransporter = async (retries = 3) => {
  // Skip verification in test environment
  if (process.env.NODE_ENV === 'test') {
    console.log('✅ Mock orders email transporter ready for testing');
    return true;
  }

  for (let i = 0; i < retries; i++) {
    try {
      await ordersTransporter.verify();
      console.log('✅ Orders Gmail SMTP server is ready to send messages');
      console.log(`📧 Using orders email address: ${process.env.EMAIL_ORDERS_USER}`);
      return true;
    } catch (error) {
      console.error(`❌ Orders Gmail SMTP verification failed (attempt ${i + 1}/${retries}):`, error.message);

      // Provide specific error guidance for Gmail
      if (error.message.includes('Invalid login')) {
        console.error('🔐 Orders Gmail authentication failed. Please check:');
        console.error('   1. Orders email address is correct');
        console.error('   2. Orders App Password is valid (16 characters)');
        console.error('   3. 2-Factor Authentication is enabled on orders account');
        console.error('   4. Account is not suspended or locked');
      }

      if (i === retries - 1) {
        console.error('❌ Orders Gmail SMTP verification failed after all retries');
        return false;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

// Initialize verification
verifyGmailTransporter();
verifySupportTransporter();
verifyOrdersTransporter();

// Send welcome email
const sendWelcomeEmail = async (email) => {
  const mailOptions = {
    to: email,
    subject: 'Welcome to Nirvana Organics Newsletter!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
          <div style="background: white; display: inline-block; padding: 15px 25px; border-radius: 8px; margin-bottom: 10px;">
            <img src="${process.env.BACKEND_URL || 'https://api.shopnirvanaorganics.com'}/assets/Nirvana_logo.png"
                 alt="Nirvana Organics Logo"
                 style="height: 40px; width: auto; display: block; margin: 0 auto;" />
          </div>
          <div style="margin-bottom: 20px;">
            <p style="color: white; margin: 0; font-size: 18px; font-weight: 500;">Shop Nirvana Organics</p>
          </div>
          <h2 style="color: white; margin: 0; font-size: 28px;">Welcome to Nirvana Organics!</h2>
        </div>
        
        <div style="padding: 40px; background: #f9fafb;">
          <h2 style="color: #16a34a; margin-bottom: 20px;">Thank you for subscribing!</h2>
          
          <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
            We're excited to have you join our community of health-conscious individuals who value premium organic products.
          </p>
          
          <p style="color: #374151; line-height: 1.6; margin-bottom: 30px;">
            You'll receive updates about:
          </p>
          
          <ul style="color: #374151; line-height: 1.8; margin-bottom: 30px;">
            <li>New organic product launches</li>
            <li>Exclusive subscriber discounts</li>
            <li>Health and wellness tips</li>
            <li>Special promotions and offers</li>
          </ul>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/shop" 
               style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
              Start Shopping
            </a>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; text-align: center; margin-top: 40px;">
            You can unsubscribe at any time by clicking 
            <a href="${process.env.FRONTEND_URL}/unsubscribe?email=${encodeURIComponent(email)}" style="color: #16a34a;">here</a>
          </p>
        </div>
        
        <div style="background: #374151; padding: 20px; text-align: center;">
          <p style="color: #9ca3af; margin: 0; font-size: 14px;">
            © 2024 Nirvana Organics. All rights reserved.
          </p>
        </div>
      </div>
    `,
    text: `
      Welcome to Nirvana Organics!
      
      Thank you for subscribing to our newsletter. We're excited to have you join our community of health-conscious individuals who value premium organic products.
      
      You'll receive updates about:
      - New organic product launches
      - Exclusive subscriber discounts
      - Health and wellness tips
      - Special promotions and offers
      
      Start shopping: ${process.env.FRONTEND_URL}/shop
      
      You can unsubscribe at any time: ${process.env.FRONTEND_URL}/unsubscribe?email=${encodeURIComponent(email)}
      
      © 2024 Nirvana Organics. All rights reserved.
    `
  };

  return await sendEmail(mailOptions, EMAIL_TYPES.CUSTOMER_SERVICE);
};

// Send promotional email
const sendPromotionalEmail = async ({ to, subject, htmlContent, textContent, campaignId }) => {
  const mailOptions = {
    to,
    subject,
    html: htmlContent,
    text: textContent,
    headers: {
      'X-Campaign-ID': campaignId
    }
  };

  return await sendEmail(mailOptions, EMAIL_TYPES.MARKETING);
};

// Send order confirmation email
const sendOrderConfirmationEmail = async (order, customer) => {
  const mailOptions = {
    to: customer.email,
    subject: `📦 Order Confirmation - ${order.orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #16a34a; padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Order Confirmed!</h1>
        </div>
        
        <div style="padding: 30px; background: #f9fafb;">
          <h2 style="color: #16a34a;">Thank you for your order, ${customer.firstName}!</h2>
          
          <p style="color: #374151; margin-bottom: 20px;">
            Your order <strong>${order.orderNumber}</strong> has been confirmed and is being processed.
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #374151; margin-top: 0;">Order Details</h3>
            <p><strong>Order Number:</strong> ${order.orderNumber}</p>
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
          </div>
          
          <p style="color: #374151;">
            We'll send you another email when your order ships with tracking information.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/orders/${order.id}" 
               style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
              View Order Details
            </a>
          </div>
        </div>
      </div>
    `,
    text: `
      Order Confirmed!
      
      Thank you for your order, ${customer.firstName}!
      
      Your order ${order.orderNumber} has been confirmed and is being processed.
      
      Order Details:
      - Order Number: ${order.orderNumber}
      - Order Date: ${new Date(order.createdAt).toLocaleDateString()}
      - Total: $${order.total.toFixed(2)}
      
      We'll send you another email when your order ships with tracking information.
      
      View order details: ${process.env.FRONTEND_URL}/orders/${order.id}
    `
  };

  return await sendEmail(mailOptions, EMAIL_TYPES.ORDER);
};

// Send password reset email with enhanced routing and professional templates
const sendPasswordResetEmail = async (email, resetToken, userType = 'customer') => {
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

  // Determine email type based on user type
  const emailType = userType === 'admin' || userType === 'manager' ? EMAIL_TYPES.SUPPORT : EMAIL_TYPES.GENERAL;

  // Get appropriate sender based on user type
  const fromEmail = emailType === EMAIL_TYPES.SUPPORT ?
    getSenderEmail(EMAIL_TYPES.SUPPORT) :
    getSenderEmail(EMAIL_TYPES.GENERAL);

  const mailOptions = {
    to: email,
    from: fromEmail,
    subject: 'Password Reset Request - Shop Nirvana Organics',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
        <!-- Header with Logo -->
        <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
          <div style="background: white; display: inline-block; padding: 15px 25px; border-radius: 8px; margin-bottom: 10px;">
            <img src="${process.env.BACKEND_URL || 'https://api.shopnirvanaorganics.com'}/assets/Nirvana_logo.png"
                 alt="Nirvana Organics Logo"
                 style="height: 40px; width: auto; display: block; margin: 0 auto;" />
          </div>
          <div style="margin-bottom: 20px;">
            <p style="color: white; margin: 0; font-size: 18px; font-weight: 500;">Shop Nirvana Organics</p>
          </div>
          <h2 style="color: white; margin: 0; font-size: 28px;">Password Reset Request</h2>
          <p style="color: #dcfce7; margin: 10px 0 0 0; font-size: 16px;">Secure your account with a new password</p>
        </div>

        <!-- Main Content -->
        <div style="padding: 40px; background: #f9fafb;">
          <div style="background: white; padding: 30px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="color: #374151; margin-top: 0; font-size: 20px;">Hello,</h3>

            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              We received a request to reset the password for your ${userType === 'admin' || userType === 'manager' ? 'admin' : 'customer'} account associated with this email address.
            </p>

            <p style="color: #374151; line-height: 1.6; margin-bottom: 30px;">
              If you made this request, click the button below to reset your password. If you didn't request this, you can safely ignore this email.
            </p>

            <!-- Reset Button -->
            <div style="text-align: center; margin: 40px 0;">
              <a href="${resetUrl}"
                 style="background: linear-gradient(135deg, #16a34a, #22c55e); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px; box-shadow: 0 4px 6px rgba(22, 163, 74, 0.3);">
                🔐 Reset My Password
              </a>
            </div>

            <!-- Security Information -->
            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 30px 0;">
              <h4 style="color: #92400e; margin-top: 0; font-size: 16px;">🔒 Security Information</h4>
              <ul style="color: #92400e; margin: 10px 0; padding-left: 20px; line-height: 1.6;">
                <li><strong>This link expires in 1 hour</strong> for your security</li>
                <li>The link can only be used once</li>
                <li>If you didn't request this reset, please ignore this email</li>
                <li>Your current password remains unchanged until you complete the reset</li>
              </ul>
            </div>

            <!-- Alternative Link -->
            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="color: #6b7280; font-size: 14px; margin: 0;">
                <strong>Can't click the button?</strong> Copy and paste this link into your browser:
              </p>
              <p style="color: #16a34a; font-size: 14px; word-break: break-all; margin: 10px 0 0 0;">
                ${resetUrl}
              </p>
            </div>

            <!-- Support Contact -->
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
              <p style="color: #6b7280; font-size: 14px; margin: 0;">
                Need help? Contact our support team at
                <a href="mailto:${process.env.EMAIL_SUPPORT || '<EMAIL>'}" style="color: #16a34a; text-decoration: none;">
                  ${process.env.EMAIL_SUPPORT || '<EMAIL>'}
                </a>
              </p>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div style="background: #374151; color: white; padding: 30px; text-align: center;">
          <div style="margin-bottom: 20px;">
            <a href="https://shopnirvanaorganics.com" style="color: #22c55e; text-decoration: none; margin: 0 15px;">🏠 Home</a>
            <a href="https://shopnirvanaorganics.com/products" style="color: #22c55e; text-decoration: none; margin: 0 15px;">🛍️ Shop</a>
            <a href="https://shopnirvanaorganics.com/contact" style="color: #22c55e; text-decoration: none; margin: 0 15px;">📞 Contact</a>
            <a href="https://shopnirvanaorganics.com/account" style="color: #22c55e; text-decoration: none; margin: 0 15px;">👤 Account</a>
          </div>
          <p style="margin: 0; font-size: 14px; color: #9ca3af;">
            © 2025 Shop Nirvana Organics. All rights reserved.<br>
            This email was sent to ${email} regarding your password reset request.
          </p>
        </div>
      </div>
    `,
    text: `
      SHOP NIRVANA ORGANICS - Password Reset Request

      Hello,

      We received a request to reset the password for your ${userType === 'admin' || userType === 'manager' ? 'admin' : 'customer'} account associated with this email address.

      If you made this request, use the link below to reset your password:
      ${resetUrl}

      SECURITY INFORMATION:
      - This link expires in 1 hour for your security
      - The link can only be used once
      - If you didn't request this reset, please ignore this email
      - Your current password remains unchanged until you complete the reset

      Need help? Contact our support team at ${process.env.EMAIL_SUPPORT || '<EMAIL>'}

      © 2025 Shop Nirvana Organics. All rights reserved.
      This email was sent to ${email} regarding your password reset request.
    `
  };

  return await sendEmail(mailOptions, emailType);
};

// Send email verification email
const sendEmailVerificationEmail = async (email, verificationToken) => {
  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;

  const mailOptions = {
    to: email,
    subject: 'Verify Your Email Address - Nirvana Organics',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
          <div style="background: white; display: inline-block; padding: 15px 25px; border-radius: 8px; margin-bottom: 10px;">
            <img src="${process.env.BACKEND_URL || 'https://api.shopnirvanaorganics.com'}/assets/Nirvana_logo.png"
                 alt="Nirvana Organics Logo"
                 style="height: 40px; width: auto; display: block; margin: 0 auto;" />
          </div>
          <div style="margin-bottom: 20px;">
            <p style="color: white; margin: 0; font-size: 18px; font-weight: 500;">Shop Nirvana Organics</p>
          </div>
          <h2 style="color: white; margin: 0; font-size: 28px;">Welcome to Nirvana Organics!</h2>
        </div>

        <div style="padding: 40px; background: #f9fafb;">
          <h2 style="color: #16a34a; margin-bottom: 20px;">Please verify your email address</h2>

          <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
            Thank you for creating an account with Nirvana Organics. To complete your registration and start shopping for premium organic products, please verify your email address.
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}"
               style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
              Verify Email Address
            </a>
          </div>

          <p style="color: #6b7280; font-size: 14px; margin-bottom: 20px;">
            This verification link will expire in 24 hours for security reasons.
          </p>

          <p style="color: #6b7280; font-size: 14px;">
            If you didn't create this account, please ignore this email.
          </p>
        </div>

        <div style="background: #374151; padding: 20px; text-align: center;">
          <p style="color: #9ca3af; margin: 0; font-size: 12px;">
            © 2025 Nirvana Organics. All rights reserved.
          </p>
        </div>
      </div>
    `,
    text: `
      Welcome to Nirvana Organics!

      Please verify your email address by clicking the link below:
      ${verificationUrl}

      This verification link will expire in 24 hours for security reasons.

      If you didn't create this account, please ignore this email.
    `
  };

  return await sendEmail(mailOptions, EMAIL_TYPES.CUSTOMER_SERVICE);
};

// Send order status update email
const sendOrderStatusUpdateEmail = async (order, customer, newStatus) => {
  const statusMessages = {
    processing: 'Your order is being processed',
    shipped: 'Your order has been shipped',
    delivered: 'Your order has been delivered',
    cancelled: 'Your order has been cancelled'
  };

  const statusColors = {
    processing: '#3b82f6',
    shipped: '#8b5cf6',
    delivered: '#10b981',
    cancelled: '#ef4444'
  };

  const mailOptions = {
    to: customer.email,
    subject: `📋 Order Update - ${order.orderNumber} - ${statusMessages[newStatus]}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: ${statusColors[newStatus]}; padding: 40px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">${statusMessages[newStatus]}</h1>
        </div>

        <div style="padding: 40px; background: #f9fafb;">
          <h2 style="color: #16a34a; margin-bottom: 20px;">Order #${order.orderNumber}</h2>

          <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
            <p><strong>Status:</strong> ${statusMessages[newStatus]}</p>
            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
            ${order.trackingNumber ? `<p><strong>Tracking Number:</strong> ${order.trackingNumber}</p>` : ''}
          </div>

          ${newStatus === 'shipped' ? `
            <p style="color: #374151; margin-bottom: 20px;">
              Your order is on its way! You can track your package using the tracking number above.
            </p>
          ` : ''}

          ${newStatus === 'delivered' ? `
            <p style="color: #374151; margin-bottom: 20px;">
              Your order has been delivered! We hope you enjoy your premium organic products.
            </p>
          ` : ''}

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/orders/${order.id}"
               style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
              View Order Details
            </a>
          </div>
        </div>

        <div style="background: #374151; padding: 20px; text-align: center;">
          <p style="color: #9ca3af; margin: 0; font-size: 12px;">
            © 2025 Nirvana Organics. All rights reserved.
          </p>
        </div>
      </div>
    `,
    text: `
      ${statusMessages[newStatus]}

      Order Details:
      - Order Number: ${order.orderNumber}
      - Order Date: ${new Date(order.createdAt).toLocaleDateString()}
      - Status: ${statusMessages[newStatus]}
      - Total: $${order.total.toFixed(2)}
      ${order.trackingNumber ? `- Tracking Number: ${order.trackingNumber}` : ''}

      View order details: ${process.env.FRONTEND_URL}/orders/${order.id}
    `
  };

  return await sendEmail(mailOptions, EMAIL_TYPES.ORDER);
};

// Test email function for Gmail configuration
const sendTestEmail = async (testEmail = '<EMAIL>') => {
  const mailOptions = {
    to: testEmail,
    subject: 'Gmail Configuration Test - Nirvana Organics',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
          <div style="background: white; display: inline-block; padding: 15px 25px; border-radius: 8px; margin-bottom: 10px;">
            <img src="${process.env.BACKEND_URL || 'https://api.shopnirvanaorganics.com'}/assets/Nirvana_logo.png"
                 alt="Nirvana Organics Logo"
                 style="height: 40px; width: auto; display: block; margin: 0 auto;" />
          </div>
          <div style="margin-bottom: 20px;">
            <p style="color: white; margin: 0; font-size: 18px; font-weight: 500;">Shop Nirvana Organics</p>
          </div>
          <h2 style="color: white; margin: 0; font-size: 28px;">Gmail Test Successful!</h2>
        </div>

        <div style="padding: 40px; background: #f9fafb;">
          <h2 style="color: #16a34a; margin-bottom: 20px;">Email Configuration Test</h2>

          <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
            Congratulations! Your Gmail SMTP configuration is working correctly.
          </p>

          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #2c5530; margin: 0 0 10px 0;">Configuration Details:</h3>
            <ul style="color: #495057; line-height: 1.6; margin: 0; padding-left: 20px;">
              <li>SMTP Server: Gmail (smtp.gmail.com)</li>
              <li>From Address: <EMAIL></li>
              <li>Authentication: ${process.env.EMAIL_OAUTH2_CLIENT_ID ? 'OAuth2' : 'App Password'}</li>
              <li>Test Time: ${new Date().toLocaleString()}</li>
            </ul>
          </div>

          <p style="color: #374151; line-height: 1.6;">
            Your Nirvana Organics e-commerce application is now ready to send transactional emails through Gmail!
          </p>
        </div>

        <div style="background: #374151; padding: 20px; text-align: center;">
          <p style="color: #9ca3af; margin: 0; font-size: 14px;">
            © ${new Date().getFullYear()} Nirvana Organics. All rights reserved.
          </p>
        </div>
      </div>
    `,
    text: `
      Gmail Configuration Test - Nirvana Organics

      Congratulations! Your Gmail SMTP configuration is working correctly.

      Configuration Details:
      - SMTP Server: Gmail (smtp.gmail.com)
      - From Address: <EMAIL>
      - Authentication: ${process.env.EMAIL_OAUTH2_CLIENT_ID ? 'OAuth2' : 'App Password'}
      - Test Time: ${new Date().toLocaleString()}

      Your Nirvana Organics e-commerce application is now ready to send transactional emails through Gmail!

      © ${new Date().getFullYear()} Nirvana Organics. All rights reserved.
    `
  };

  return await sendEmail(mailOptions, EMAIL_TYPES.SYSTEM);
};

// Send contact form email
const sendContactFormEmail = async ({ name, email, phone, subject, message, inquiryType }) => {
  const inquiryTypeLabels = {
    general: 'General Inquiry',
    product: 'Product Question',
    order: 'Order Support',
    shipping: 'Shipping Question',
    return: 'Return/Exchange',
    wholesale: 'Wholesale Inquiry',
    partnership: 'Partnership Opportunity'
  };

  // Get support recipient email (primary: support@, fallback: on-reply@)
  const supportRecipient = getSupportRecipientEmail();

  const mailOptions = {
    to: supportRecipient.single,
    subject: `Contact Form: ${subject} [${inquiryTypeLabels[inquiryType] || 'General'}]`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
          <div style="background: white; display: inline-block; padding: 15px 25px; border-radius: 8px; margin-bottom: 10px;">
            <img src="${process.env.BACKEND_URL || 'https://api.shopnirvanaorganics.com'}/assets/Nirvana_logo.png"
                 alt="Nirvana Organics Logo"
                 style="height: 40px; width: auto; display: block; margin: 0 auto;" />
          </div>
          <div style="margin-bottom: 20px;">
            <p style="color: white; margin: 0; font-size: 18px; font-weight: 500;">Shop Nirvana Organics</p>
          </div>
          <h2 style="color: white; margin: 0; font-size: 28px;">New Contact Form Submission</h2>
        </div>

        <div style="padding: 30px; background: #f9fafb;">
          <h2 style="color: #16a34a; margin-bottom: 20px;">Contact Details</h2>

          <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
            <p><strong>Inquiry Type:</strong> ${inquiryTypeLabels[inquiryType] || 'General'}</p>
            <p><strong>Subject:</strong> ${subject}</p>
          </div>

          <div style="background: white; padding: 20px; border-radius: 8px;">
            <h3 style="color: #374151; margin-top: 0;">Message</h3>
            <p style="color: #374151; line-height: 1.6; white-space: pre-wrap;">${message}</p>
          </div>

          <div style="margin-top: 20px; padding: 15px; background: #fef3c7; border-radius: 8px;">
            <p style="color: #92400e; margin: 0; font-size: 14px;">
              <strong>Action Required:</strong> Please respond to this inquiry within 24 hours.
              Reply directly to this email to respond to the customer.
            </p>
          </div>
        </div>

        <div style="background: #374151; padding: 20px; text-align: center;">
          <p style="color: #9ca3af; margin: 0; font-size: 14px;">
            © ${new Date().getFullYear()} Nirvana Organics. Customer Service System.
          </p>
        </div>
      </div>
    `,
    text: `
      New Contact Form Submission - Nirvana Organics

      Contact Details:
      - Name: ${name}
      - Email: ${email}
      ${phone ? `- Phone: ${phone}` : ''}
      - Inquiry Type: ${inquiryTypeLabels[inquiryType] || 'General'}
      - Subject: ${subject}

      Message:
      ${message}

      Action Required: Please respond to this inquiry within 24 hours.
      Reply directly to this email to respond to the customer.
    `,
    replyTo: email // Allow direct reply to customer
  };

  return await sendEmail(mailOptions, EMAIL_TYPES.SUPPORT);
};

module.exports = {
  sendEmail,
  sendWelcomeEmail,
  sendPromotionalEmail,
  sendOrderConfirmationEmail,
  sendPasswordResetEmail,
  sendEmailVerificationEmail,
  sendOrderStatusUpdateEmail,
  sendTestEmail,
  sendContactFormEmail,
  verifyGmailTransporter,
  verifySupportTransporter,
  verifyOrdersTransporter,
  EMAIL_TYPES,
  getSenderEmail,
  getSupportRecipientEmail
};
