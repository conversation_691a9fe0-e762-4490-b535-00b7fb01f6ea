#!/usr/bin/env node

/**
 * Complete Deployment Package Validation Script
 * 
 * This script validates that the complete production deployment package
 * contains all required files and configurations for successful deployment.
 */

const fs = require('fs');
const path = require('path');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(70));
    console.log(`🔍 ${msg}`);
    console.log('='.repeat(70));
  }
};

function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    const size = (stats.size / 1024).toFixed(2);
    log.success(`${description}: ${filePath} (${size} KB)`);
    return true;
  } else {
    log.error(`${description} not found: ${filePath}`);
    return false;
  }
}

function checkDirectoryExists(dirPath, description) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    const files = fs.readdirSync(dirPath);
    log.success(`${description}: ${dirPath} (${files.length} items)`);
    return true;
  } else {
    log.error(`${description} not found: ${dirPath}`);
    return false;
  }
}

async function validateCompleteDeployment() {
  try {
    log.header('Validating Complete Production Deployment Package');
    
    const deploymentDir = 'deployment';
    
    if (!fs.existsSync(deploymentDir)) {
      log.error('Deployment directory not found. Run "npm run build:complete-deployment" first.');
      process.exit(1);
    }
    
    let allValid = true;
    
    // 1. Validate directory structure
    log.header('Directory Structure Validation');
    const requiredDirs = [
      { path: 'deployment', desc: 'Main deployment directory' },
      { path: 'deployment/frontend', desc: 'Unified frontend build' },
      { path: 'deployment/frontend/admin', desc: 'Admin panel' },
      { path: 'deployment/server', desc: 'Backend server' },
      { path: 'deployment/config', desc: 'Configuration files' },
      { path: 'deployment/scripts', desc: 'Deployment scripts' },
      { path: 'deployment/nginx', desc: 'Nginx configuration' },
      { path: 'deployment/systemd', desc: 'Systemd service files' }
    ];
    
    requiredDirs.forEach(dir => {
      if (!checkDirectoryExists(dir.path, dir.desc)) {
        allValid = false;
      }
    });
    
    // 2. Validate core application files
    log.header('Core Application Files Validation');
    const coreFiles = [
      { path: 'deployment/frontend/index.html', desc: 'Main frontend entry point' },
      { path: 'deployment/frontend/admin/admin.html', desc: 'Admin panel entry point' },
      { path: 'deployment/server/index.js', desc: 'Backend server entry point' },
      { path: 'deployment/package.json', desc: 'Deployment package configuration' },
      { path: 'deployment/ecosystem.config.js', desc: 'PM2 ecosystem configuration' },
      { path: 'deployment/deploy.sh', desc: 'Main deployment script' }
    ];
    
    coreFiles.forEach(file => {
      if (!checkFileExists(file.path, file.desc)) {
        allValid = false;
      }
    });
    
    // 3. Validate configuration files
    log.header('Configuration Files Validation');
    const configFiles = [
      { path: 'deployment/config/.env.production.template', desc: 'Environment configuration template' },
      { path: 'deployment/config/database.js', desc: 'Database configuration' },
      { path: 'deployment/nginx/nirvana-organics.conf', desc: 'Nginx configuration' },
      { path: 'deployment/systemd/nirvana-backend.service', desc: 'Systemd service configuration' }
    ];
    
    configFiles.forEach(file => {
      if (!checkFileExists(file.path, file.desc)) {
        allValid = false;
      }
    });
    
    // 4. Validate deployment scripts
    log.header('Deployment Scripts Validation');
    const scriptFiles = [
      { path: 'deployment/scripts/setup-database.js', desc: 'Database setup script' },
      { path: 'deployment/scripts/install-dependencies.sh', desc: 'Dependencies installation script' },
      { path: 'deployment/scripts/configure-nginx.sh', desc: 'Nginx configuration script' },
      { path: 'deployment/scripts/setup-pm2.sh', desc: 'PM2 setup script' },
      { path: 'deployment/scripts/pm2-manager.sh', desc: 'PM2 management script' },
      { path: 'deployment/scripts/test-environment.sh', desc: 'Test environment verification script' },
      { path: 'deployment/scripts/enhanced-square-inventory.js', desc: 'Square inventory integration' }
    ];
    
    scriptFiles.forEach(file => {
      if (!checkFileExists(file.path, file.desc)) {
        allValid = false;
      }
    });
    
    // 5. Validate frontend assets
    log.header('Frontend Assets Validation');
    const frontendAssets = [
      { path: 'deployment/frontend/assets', desc: 'Main frontend assets directory' },
      { path: 'deployment/frontend/admin/assets', desc: 'Admin panel assets directory' },
      { path: 'deployment/frontend/routing-config.json', desc: 'Routing configuration' }
    ];
    
    frontendAssets.forEach(asset => {
      if (asset.path.endsWith('.json')) {
        if (!checkFileExists(asset.path, asset.desc)) {
          allValid = false;
        }
      } else {
        if (!checkDirectoryExists(asset.path, asset.desc)) {
          allValid = false;
        }
      }
    });
    
    // 6. Validate backend components
    log.header('Backend Components Validation');
    const backendComponents = [
      { path: 'deployment/server/controllers', desc: 'API controllers' },
      { path: 'deployment/server/models', desc: 'Database models' },
      { path: 'deployment/server/routes', desc: 'API routes' },
      { path: 'deployment/server/services', desc: 'Business logic services' },
      { path: 'deployment/server/middleware', desc: 'Express middleware' }
    ];
    
    backendComponents.forEach(component => {
      if (!checkDirectoryExists(component.path, component.desc)) {
        allValid = false;
      }
    });
    
    // 7. Check for Square integration
    log.header('Square Integration Validation');
    const squareFiles = [
      { path: 'deployment/server/services/squareService.js', desc: 'Square service integration' },
      { path: 'deployment/scripts/enhanced-square-inventory.js', desc: 'Enhanced Square inventory management' }
    ];
    
    squareFiles.forEach(file => {
      if (!checkFileExists(file.path, file.desc)) {
        allValid = false;
      }
    });
    
    // 8. Validate documentation
    log.header('Documentation Validation');
    if (!checkFileExists('deployment/README.md', 'Deployment documentation')) {
      allValid = false;
    }
    
    // 9. Calculate package size and provide summary
    log.header('Package Summary');
    const packageSize = calculatePackageSize('deployment');
    log.info(`Total package size: ${packageSize}`);
    
    // Count files
    const fileCount = countFiles('deployment');
    log.info(`Total files: ${fileCount}`);
    
    // 10. Validate file permissions (if on Unix-like system)
    log.header('File Permissions Check');
    const executableFiles = [
      'deployment/deploy.sh',
      'deployment/scripts/install-dependencies.sh',
      'deployment/scripts/configure-nginx.sh'
    ];
    
    executableFiles.forEach(file => {
      if (fs.existsSync(file)) {
        try {
          const stats = fs.statSync(file);
          // Check if file has execute permissions (Unix-like systems)
          if (process.platform !== 'win32') {
            const mode = stats.mode;
            const hasExecute = (mode & parseInt('111', 8)) !== 0;
            if (hasExecute) {
              log.success(`Executable permissions set: ${file}`);
            } else {
              log.warning(`Missing execute permissions: ${file}`);
            }
          } else {
            log.info(`File exists (Windows): ${file}`);
          }
        } catch (error) {
          log.warning(`Could not check permissions for ${file}: ${error.message}`);
        }
      }
    });
    
    // Final validation result
    log.header('Validation Results');
    
    if (allValid) {
      log.success('🎉 Complete deployment package validation passed!');
      log.info('');
      log.info('✅ All required files and directories are present');
      log.info('✅ Unified frontend build with main app and admin panel');
      log.info('✅ Complete backend server with all components');
      log.info('✅ Nginx configuration for unified deployment');
      log.info('✅ Systemd service configuration');
      log.info('✅ Automated deployment scripts');
      log.info('✅ Square payment integration');
      log.info('✅ Enhanced Square inventory management');
      log.info('✅ Comprehensive documentation');
      log.info('');
      log.info('📦 Package Details:');
      log.info(`   • Size: ${packageSize}`);
      log.info(`   • Files: ${fileCount}`);
      log.info(`   • Location: ${path.resolve('deployment')}`);
      log.info('');
      log.info('🚀 Ready for production deployment!');
      log.info('');
      log.info('Next steps:');
      log.info('1. Upload the deployment package to your VPS server');
      log.info('2. Extract: tar -xzf deployment.tar.gz');
      log.info('3. Deploy: cd deployment && chmod +x deploy.sh && sudo ./deploy.sh');
      
    } else {
      log.error('❌ Deployment package validation failed');
      log.error('Some required files or directories are missing');
      log.error('Please run "npm run build:complete-deployment" to regenerate the package');
      process.exit(1);
    }
    
  } catch (error) {
    log.error(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

function calculatePackageSize(dirPath) {
  let size = 0;
  
  function getDirSize(dir) {
    const files = fs.readdirSync(dir);
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        getDirSize(filePath);
      } else {
        size += stats.size;
      }
    }
  }
  
  getDirSize(dirPath);
  return `${(size / 1024 / 1024).toFixed(2)} MB`;
}

function countFiles(dirPath) {
  let count = 0;
  
  function countInDir(dir) {
    const files = fs.readdirSync(dir);
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        countInDir(filePath);
      } else {
        count++;
      }
    }
  }
  
  countInDir(dirPath);
  return count;
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateCompleteDeployment();
}

module.exports = { validateCompleteDeployment };
