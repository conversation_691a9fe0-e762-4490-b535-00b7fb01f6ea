# Nirvana Organics E-commerce - Testing Environment Nginx Configuration
# Target Domain: test.shopnirvanaorganics.com

# Rate limiting zones - RELAXED FOR TESTING
limit_req_zone $binary_remote_addr zone=api:10m rate=200r/m;
limit_req_zone $binary_remote_addr zone=auth:10m rate=60r/m;
limit_req_zone $binary_remote_addr zone=admin:10m rate=120r/m;
limit_req_zone $binary_remote_addr zone=upload:10m rate=20r/m;

# Upstream backend servers
upstream nirvana_backend {
    least_conn;
    server 127.0.0.1:5000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Main server block
server {
    listen 80;
    listen [::]:80;
    server_name test.shopnirvanaorganics.com;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "noindex, nofollow" always;
    add_header X-Environment "testing" always;

    # Hide Nginx version
    server_tokens off;

    # Root directory
    root /var/www/nirvana-backend/dist;
    index index.html;

    # Logging (verbose for testing)
    access_log /var/log/nginx/nirvana-testing-access.log combined;
    error_log /var/log/nginx/nirvana-testing-error.log debug;

    # Client settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;

    # API routes with relaxed rate limiting
    location /api/ {
        limit_req zone=api burst=40 nodelay;
        limit_req_status 429;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        proxy_cache_bypass $http_upgrade;
        proxy_hide_header X-Powered-By;
    }

    # Authentication routes
    location /api/auth/ {
        limit_req zone=auth burst=10 nodelay;
        limit_req_status 429;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Admin API routes (more permissive for testing)
    location /api/admin/ {
        limit_req zone=admin burst=30 nodelay;
        limit_req_status 429;

        add_header X-Admin-API "true" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Environment "testing" always;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Admin-Request "true";
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 30s;
        proxy_send_timeout 45s;
        proxy_read_timeout 45s;

        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Admin panel static files
    location /admin/ {
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header X-Robots-Tag "noindex, nofollow" always;
        add_header X-Environment "testing" always;

        limit_req zone=admin burst=20 nodelay;
        limit_req_status 429;

        try_files $uri $uri/ /admin/admin.html;

        location ~* ^/admin/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
            add_header Vary "Accept-Encoding";
            add_header X-Content-Type-Options "nosniff";
            add_header X-Frame-Options "DENY";
            add_header X-Environment "testing";
        }

        location ~* ^/admin/.*\.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header X-Frame-Options "DENY";
            add_header X-XSS-Protection "1; mode=block";
            add_header X-Content-Type-Options "nosniff";
            add_header X-Robots-Tag "noindex, nofollow";
            add_header X-Environment "testing";
        }
    }

    # Static file serving
    location /uploads/ {
        alias /var/www/nirvana-backend/uploads/;

        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        add_header X-Content-Type-Options "nosniff";
        add_header X-Environment "testing";

        location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }

        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Range";
    }

    # Static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        add_header X-Environment "testing";

        gzip_static on;

        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header X-Content-Type-Options "nosniff";

        try_files $uri $uri/ =404;
    }

    # Frontend SPA routing
    location / {
        try_files $uri $uri/ /index.html;

        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
            add_header X-Environment "testing";
        }

        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
        add_header X-Environment "testing";
    }

    # Health check endpoint
    location /api/health {
        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;

        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        add_header X-Environment "testing";
    }

    # Robots.txt for testing
    location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\nDisallow: /\n";
    }

    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|log|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~* \.(php|asp|aspx|jsp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}