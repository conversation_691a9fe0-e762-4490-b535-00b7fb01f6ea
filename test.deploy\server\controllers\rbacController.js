const { User, Role, AuditLog } = require('../models');
const RBACService = require('../services/rbacService');
const { validationResult } = require('express-validator');

/**
 * Role-Based Access Control Controller
 * Handles user role management, invitations, and permissions
 */
class RBACController {
  /**
   * Create manager invitation
   * @route POST /api/admin/rbac/invite-manager
   * @access Private (Admin only)
   */
  static async inviteManager(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, firstName, lastName, phone } = req.body;

      const result = await RBACService.createManagerInvitation(
        { email, firstName, lastName, phone },
        req.user
      );

      res.status(201).json({
        success: true,
        data: result,
        message: 'Manager invitation sent successfully'
      });

    } catch (error) {
      console.error('Invite manager error:', error);
      
      if (error.message === 'User with this email already exists') {
        return res.status(409).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to send manager invitation',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Activate manager account
   * @route POST /api/auth/activate-manager
   * @access Public (with token)
   */
  static async activateManagerAccount(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { token, password } = req.body;

      const result = await RBACService.activateManagerAccount(token, password);

      res.json({
        success: true,
        data: result,
        message: 'Manager account activated successfully'
      });

    } catch (error) {
      console.error('Activate manager error:', error);
      
      if (error.message === 'Invalid or expired verification token') {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to activate manager account',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get user permissions
   * @route GET /api/admin/rbac/permissions/:userId
   * @access Private (Admin/Manager)
   */
  static async getUserPermissions(req, res) {
    try {
      const { userId } = req.params;

      const permissions = await RBACService.getUserPermissions(userId);

      res.json({
        success: true,
        data: permissions
      });

    } catch (error) {
      console.error('Get permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user permissions',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Update user role
   * @route PUT /api/admin/rbac/users/:userId/role
   * @access Private (Admin only)
   */
  static async updateUserRole(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId } = req.params;
      const { role } = req.body;

      const result = await RBACService.updateUserRole(userId, role, req.user);

      res.json({
        success: true,
        data: result,
        message: 'User role updated successfully'
      });

    } catch (error) {
      console.error('Update role error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update user role',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get all roles and their permissions
   * @route GET /api/admin/rbac/roles
   * @access Private (Admin only)
   */
  static async getRoles(req, res) {
    try {
      const roles = await Role.findAll({
        where: { isActive: true },
        order: [['name', 'ASC']]
      });

      res.json({
        success: true,
        data: roles.map(role => ({
          id: role.id,
          name: role.name,
          displayName: role.displayName,
          description: role.description,
          permissions: role.permissions,
          isActive: role.isActive
        }))
      });

    } catch (error) {
      console.error('Get roles error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get roles',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get audit logs for admin actions
   * @route GET /api/admin/rbac/audit-logs
   * @access Private (Admin only)
   */
  static async getAuditLogs(req, res) {
    try {
      const {
        page = 1,
        limit = 50,
        userId,
        action,
        entityType,
        severity,
        startDate,
        endDate
      } = req.query;

      const logs = await AuditLog.getRecentActivity({
        page: parseInt(page),
        limit: parseInt(limit),
        userId: userId ? parseInt(userId) : undefined,
        action,
        entityType,
        severity,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined
      });

      res.json({
        success: true,
        data: logs
      });

    } catch (error) {
      console.error('Get audit logs error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get audit logs',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get audit statistics
   * @route GET /api/admin/rbac/audit-stats
   * @access Private (Admin only)
   */
  static async getAuditStatistics(req, res) {
    try {
      const { startDate, endDate, userId } = req.query;

      const stats = await AuditLog.getStatistics({
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        userId: userId ? parseInt(userId) : undefined
      });

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      console.error('Get audit stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get audit statistics',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get current user's permissions
   * @route GET /api/auth/my-permissions
   * @access Private
   */
  static async getMyPermissions(req, res) {
    try {
      const permissions = await RBACService.getUserPermissions(req.user.id);

      res.json({
        success: true,
        data: permissions
      });

    } catch (error) {
      console.error('Get my permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get permissions',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get users by role
   * @route GET /api/admin/rbac/users-by-role/:roleName
   * @access Private (Admin/Manager)
   */
  static async getUsersByRole(req, res) {
    try {
      const { roleName } = req.params;
      const { page = 1, limit = 20 } = req.query;

      const role = await Role.getByName(roleName);
      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role not found'
        });
      }

      const { count, rows: users } = await User.findAndCountAll({
        where: { roleId: role.id, isActive: true },
        include: [{ model: Role, as: 'Role' }],
        attributes: ['id', 'email', 'firstName', 'lastName', 'lastLogin', 'createdAt'],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit)
      });

      res.json({
        success: true,
        data: {
          users: users.map(user => ({
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: user.getFullName(),
            role: user.Role?.name,
            lastLogin: user.lastLogin,
            createdAt: user.createdAt
          })),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / parseInt(limit))
          }
        }
      });

    } catch (error) {
      console.error('Get users by role error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get users by role',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = RBACController;
