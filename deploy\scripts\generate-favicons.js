#!/usr/bin/env node

/**
 * Favicon Generation Script for Nirvana Organics
 * Generates multiple favicon sizes and formats from the main logo
 */

import sharp from 'sharp';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`)
};

// Favicon configurations
const faviconSizes = [
  // Standard favicon sizes
  { size: 16, name: 'favicon-16x16.png', format: 'png' },
  { size: 32, name: 'favicon-32x32.png', format: 'png' },
  { size: 48, name: 'favicon-48x48.png', format: 'png' },
  
  // Apple touch icons
  { size: 57, name: 'apple-touch-icon-57x57.png', format: 'png' },
  { size: 60, name: 'apple-touch-icon-60x60.png', format: 'png' },
  { size: 72, name: 'apple-touch-icon-72x72.png', format: 'png' },
  { size: 76, name: 'apple-touch-icon-76x76.png', format: 'png' },
  { size: 114, name: 'apple-touch-icon-114x114.png', format: 'png' },
  { size: 120, name: 'apple-touch-icon-120x120.png', format: 'png' },
  { size: 144, name: 'apple-touch-icon-144x144.png', format: 'png' },
  { size: 152, name: 'apple-touch-icon-152x152.png', format: 'png' },
  { size: 180, name: 'apple-touch-icon-180x180.png', format: 'png' },
  { size: 180, name: 'apple-touch-icon.png', format: 'png' }, // Default Apple touch icon
  
  // Android/PWA icons
  { size: 36, name: 'android-chrome-36x36.png', format: 'png' },
  { size: 48, name: 'android-chrome-48x48.png', format: 'png' },
  { size: 72, name: 'android-chrome-72x72.png', format: 'png' },
  { size: 96, name: 'android-chrome-96x96.png', format: 'png' },
  { size: 144, name: 'android-chrome-144x144.png', format: 'png' },
  { size: 192, name: 'android-chrome-192x192.png', format: 'png' },
  { size: 256, name: 'android-chrome-256x256.png', format: 'png' },
  { size: 384, name: 'android-chrome-384x384.png', format: 'png' },
  { size: 512, name: 'android-chrome-512x512.png', format: 'png' },
  
  // Microsoft tiles
  { size: 70, name: 'mstile-70x70.png', format: 'png' },
  { size: 144, name: 'mstile-144x144.png', format: 'png' },
  { size: 150, name: 'mstile-150x150.png', format: 'png' },
  { size: 310, name: 'mstile-310x150.png', format: 'png', width: 310, height: 150 },
  { size: 310, name: 'mstile-310x310.png', format: 'png' },
  
  // Additional common sizes
  { size: 128, name: 'icon-128x128.png', format: 'png' },
  { size: 196, name: 'icon-196x196.png', format: 'png' }
];

/**
 * Ensure directory exists
 */
async function ensureDirectory(dirPath) {
  try {
    await fs.access(dirPath);
  } catch (error) {
    await fs.mkdir(dirPath, { recursive: true });
    log.info(`Created directory: ${dirPath}`);
  }
}

/**
 * Generate a single favicon size
 */
async function generateFavicon(inputPath, outputPath, config) {
  try {
    const { size, width, height, format } = config;
    const finalWidth = width || size;
    const finalHeight = height || size;
    
    await sharp(inputPath)
      .resize(finalWidth, finalHeight, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 } // Transparent background
      })
      .png({ quality: 100, compressionLevel: 9 })
      .toFile(outputPath);
    
    log.success(`Generated ${config.name} (${finalWidth}x${finalHeight})`);
    return true;
  } catch (error) {
    log.error(`Failed to generate ${config.name}: ${error.message}`);
    return false;
  }
}

/**
 * Generate ICO file from PNG files
 */
async function generateIcoFile(pngFiles, outputPath) {
  try {
    // For ICO generation, we'll create a simple script that uses the 32x32 PNG
    // Since sharp doesn't support ICO output, we'll copy the 32x32 PNG as favicon.ico
    const favicon32Path = pngFiles.find(file => file.includes('favicon-32x32.png'));
    if (favicon32Path) {
      await fs.copyFile(favicon32Path, outputPath.replace('.ico', '.png'));
      // Create a simple ICO by renaming (browsers will accept PNG with .ico extension)
      await fs.copyFile(favicon32Path, outputPath);
      log.success(`Generated favicon.ico`);
      return true;
    }
    return false;
  } catch (error) {
    log.error(`Failed to generate ICO file: ${error.message}`);
    return false;
  }
}

/**
 * Generate browserconfig.xml for Microsoft tiles
 */
async function generateBrowserConfig(outputDir) {
  const browserConfigContent = `<?xml version="1.0" encoding="utf-8"?>
<browserconfig>
    <msapplication>
        <tile>
            <square70x70logo src="/mstile-70x70.png"/>
            <square150x150logo src="/mstile-150x150.png"/>
            <wide310x150logo src="/mstile-310x150.png"/>
            <square310x310logo src="/mstile-310x310.png"/>
            <TileColor>#16a34a</TileColor>
        </tile>
    </msapplication>
</browserconfig>`;

  const configPath = path.join(outputDir, 'browserconfig.xml');
  await fs.writeFile(configPath, browserConfigContent);
  log.success('Generated browserconfig.xml');
}

/**
 * Main favicon generation function
 */
async function generateFavicons() {
  log.info('Starting Nirvana Organics Favicon Generation...');
  log.info('================================================');

  const projectRoot = path.join(__dirname, '..');
  const inputLogo = path.join(projectRoot, 'public', 'Nirvana_logo.png');
  const outputDir = path.join(projectRoot, 'public');

  // Check if input logo exists
  try {
    await fs.access(inputLogo);
    log.info(`Using logo: ${inputLogo}`);
  } catch (error) {
    log.error(`Logo file not found: ${inputLogo}`);
    process.exit(1);
  }

  // Ensure output directory exists
  await ensureDirectory(outputDir);

  // Generate all favicon sizes
  const generatedFiles = [];
  let successCount = 0;
  let failCount = 0;

  for (const config of faviconSizes) {
    const outputPath = path.join(outputDir, config.name);
    const success = await generateFavicon(inputLogo, outputPath, config);
    
    if (success) {
      generatedFiles.push(outputPath);
      successCount++;
    } else {
      failCount++;
    }
  }

  // Generate ICO file
  await generateIcoFile(generatedFiles, path.join(outputDir, 'favicon.ico'));

  // Generate browserconfig.xml
  await generateBrowserConfig(outputDir);

  // Summary
  log.info('================================================');
  log.info(`Favicon generation completed!`);
  log.info(`Successfully generated: ${successCount} files`);
  if (failCount > 0) {
    log.warning(`Failed to generate: ${failCount} files`);
  }
  log.info('================================================');
  
  log.info('Next steps:');
  log.info('1. Update HTML head tags with new favicon references');
  log.info('2. Update site.webmanifest with new icon sizes');
  log.info('3. Test favicons in different browsers and devices');
  log.info('4. Deploy updated files to Hostinger hosting');
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateFavicons().catch(error => {
    log.error(`Favicon generation failed: ${error.message}`);
    process.exit(1);
  });
}

export { generateFavicons };
