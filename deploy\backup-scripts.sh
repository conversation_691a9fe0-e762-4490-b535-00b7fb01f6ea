#!/bin/bash

# Comprehensive Backup Scripts for Production Environment
# shopnirvanaorganics.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_PATH="/var/www/nirvana-backend"
BACKUP_PATH="/var/www/nirvana-backend/backups"
DB_NAME="nirvana_organics_production"
DB_USER="nirvana_backup_prod"
DB_PASSWORD="your-super-secure-backup-password"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directories
create_backup_dirs() {
    log_info "Creating backup directories..."
    
    mkdir -p "$BACKUP_PATH"/{database,files,logs,config}
    chown -R nirvana:www-data "$BACKUP_PATH"
    chmod -R 755 "$BACKUP_PATH"
    
    log_info "Backup directories created"
}

# Database backup function
backup_database() {
    log_info "Starting database backup..."
    
    local backup_file="$BACKUP_PATH/database/db_backup_$DATE.sql"
    local compressed_file="$backup_file.gz"
    
    # Create database dump
    mysqldump \
        --user="$DB_USER" \
        --password="$DB_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --opt \
        --lock-tables=false \
        "$DB_NAME" > "$backup_file"
    
    if [ $? -eq 0 ]; then
        # Compress the backup
        gzip "$backup_file"
        
        # Set permissions
        chown nirvana:www-data "$compressed_file"
        chmod 640 "$compressed_file"
        
        local file_size=$(du -h "$compressed_file" | cut -f1)
        log_info "Database backup completed: $compressed_file ($file_size)"
    else
        log_error "Database backup failed"
        return 1
    fi
}

# Files backup function
backup_files() {
    log_info "Starting files backup..."
    
    local backup_file="$BACKUP_PATH/files/files_backup_$DATE.tar.gz"
    
    # Directories to backup
    local dirs_to_backup=(
        "$PROJECT_PATH/uploads"
        "$PROJECT_PATH/dist"
        "$PROJECT_PATH/.env"
        "$PROJECT_PATH/ecosystem.config.js"
    )
    
    # Create tar archive
    tar -czf "$backup_file" \
        --exclude="*.tmp" \
        --exclude="*.log" \
        --exclude="node_modules" \
        "${dirs_to_backup[@]}" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        # Set permissions
        chown nirvana:www-data "$backup_file"
        chmod 640 "$backup_file"
        
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_info "Files backup completed: $backup_file ($file_size)"
    else
        log_error "Files backup failed"
        return 1
    fi
}

# Configuration backup function
backup_config() {
    log_info "Starting configuration backup..."
    
    local backup_file="$BACKUP_PATH/config/config_backup_$DATE.tar.gz"
    
    # Configuration files to backup
    local config_files=(
        "/etc/nginx/sites-available/shopnirvanaorganics.com"
        "/etc/ssl/certs/shopnirvanaorganics.com.crt"
        "/usr/local/bin/renew-ssl-production.sh"
        "/usr/local/bin/monitor-ssl.sh"
        "/etc/logrotate.d/nirvana-backend"
    )
    
    # Create tar archive of existing files only
    local existing_files=()
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            existing_files+=("$file")
        fi
    done
    
    if [ ${#existing_files[@]} -gt 0 ]; then
        tar -czf "$backup_file" "${existing_files[@]}" 2>/dev/null
        
        # Set permissions
        chown nirvana:www-data "$backup_file"
        chmod 640 "$backup_file"
        
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_info "Configuration backup completed: $backup_file ($file_size)"
    else
        log_warning "No configuration files found to backup"
    fi
}

# Logs backup function
backup_logs() {
    log_info "Starting logs backup..."
    
    local backup_file="$BACKUP_PATH/logs/logs_backup_$DATE.tar.gz"
    
    # Log directories to backup
    local log_dirs=(
        "$PROJECT_PATH/logs"
        "/var/log/nginx"
        "/var/log/ssl-renewal.log"
        "/var/log/ssl-monitor.log"
    )
    
    # Create tar archive of existing directories only
    local existing_dirs=()
    for dir in "${log_dirs[@]}"; do
        if [ -d "$dir" ] || [ -f "$dir" ]; then
            existing_dirs+=("$dir")
        fi
    done
    
    if [ ${#existing_dirs[@]} -gt 0 ]; then
        tar -czf "$backup_file" \
            --exclude="*.tmp" \
            "${existing_dirs[@]}" 2>/dev/null
        
        # Set permissions
        chown nirvana:www-data "$backup_file"
        chmod 640 "$backup_file"
        
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_info "Logs backup completed: $backup_file ($file_size)"
    else
        log_warning "No log files found to backup"
    fi
}

# Clean up old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    local deleted_count=0
    local deleted_size=0
    
    # Find and delete old backup files
    while IFS= read -r -d '' file; do
        local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        rm "$file"
        deleted_count=$((deleted_count + 1))
        deleted_size=$((deleted_size + file_size))
    done < <(find "$BACKUP_PATH" -name "*.sql.gz" -o -name "*.tar.gz" -mtime +$RETENTION_DAYS -print0 2>/dev/null)
    
    if [ $deleted_count -gt 0 ]; then
        local deleted_size_mb=$((deleted_size / 1024 / 1024))
        log_info "Deleted $deleted_count old backup files (${deleted_size_mb}MB freed)"
    else
        log_info "No old backup files to delete"
    fi
}

# Verify backup integrity
verify_backups() {
    log_info "Verifying backup integrity..."
    
    local verification_failed=0
    
    # Verify database backup
    local latest_db_backup=$(find "$BACKUP_PATH/database" -name "db_backup_$DATE.sql.gz" -type f 2>/dev/null | head -1)
    if [ -n "$latest_db_backup" ]; then
        if gzip -t "$latest_db_backup" 2>/dev/null; then
            log_info "✅ Database backup integrity verified"
        else
            log_error "❌ Database backup integrity check failed"
            verification_failed=1
        fi
    fi
    
    # Verify files backup
    local latest_files_backup=$(find "$BACKUP_PATH/files" -name "files_backup_$DATE.tar.gz" -type f 2>/dev/null | head -1)
    if [ -n "$latest_files_backup" ]; then
        if tar -tzf "$latest_files_backup" >/dev/null 2>&1; then
            log_info "✅ Files backup integrity verified"
        else
            log_error "❌ Files backup integrity check failed"
            verification_failed=1
        fi
    fi
    
    return $verification_failed
}

# Generate backup report
generate_backup_report() {
    log_info "Generating backup report..."
    
    local report_file="$BACKUP_PATH/backup_report_$DATE.txt"
    
    cat > "$report_file" << EOF
Nirvana Organics Production Backup Report
==========================================
Date: $(date)
Server: $(hostname)
Backup Location: $BACKUP_PATH

Database Backup:
$(find "$BACKUP_PATH/database" -name "db_backup_$DATE.sql.gz" -exec ls -lh {} \; 2>/dev/null || echo "No database backup found")

Files Backup:
$(find "$BACKUP_PATH/files" -name "files_backup_$DATE.tar.gz" -exec ls -lh {} \; 2>/dev/null || echo "No files backup found")

Configuration Backup:
$(find "$BACKUP_PATH/config" -name "config_backup_$DATE.tar.gz" -exec ls -lh {} \; 2>/dev/null || echo "No config backup found")

Logs Backup:
$(find "$BACKUP_PATH/logs" -name "logs_backup_$DATE.tar.gz" -exec ls -lh {} \; 2>/dev/null || echo "No logs backup found")

Disk Usage:
$(df -h "$BACKUP_PATH")

Total Backup Size:
$(du -sh "$BACKUP_PATH")

Backup Files Count:
Database: $(find "$BACKUP_PATH/database" -name "*.sql.gz" | wc -l)
Files: $(find "$BACKUP_PATH/files" -name "*.tar.gz" | wc -l)
Config: $(find "$BACKUP_PATH/config" -name "*.tar.gz" | wc -l)
Logs: $(find "$BACKUP_PATH/logs" -name "*.tar.gz" | wc -l)
EOF
    
    chown nirvana:www-data "$report_file"
    chmod 644 "$report_file"
    
    log_info "Backup report generated: $report_file"
}

# Send backup notification (optional - configure email settings)
send_notification() {
    local status=$1
    local message=$2
    
    # Uncomment and configure if you want email notifications
    # echo "$message" | mail -s "Nirvana Organics Backup $status" <EMAIL>
    
    log_info "Backup notification: $status - $message"
}

# Main backup function
run_full_backup() {
    log_info "Starting full backup process..."
    
    local start_time=$(date +%s)
    local backup_success=true
    
    create_backup_dirs
    
    # Run all backup functions
    if ! backup_database; then
        backup_success=false
    fi
    
    if ! backup_files; then
        backup_success=false
    fi
    
    backup_config
    backup_logs
    cleanup_old_backups
    
    if ! verify_backups; then
        backup_success=false
    fi
    
    generate_backup_report
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ "$backup_success" = true ]; then
        local message="Full backup completed successfully in ${duration}s"
        log_info "$message"
        send_notification "SUCCESS" "$message"
    else
        local message="Backup completed with errors in ${duration}s"
        log_error "$message"
        send_notification "WARNING" "$message"
        return 1
    fi
}

# Quick database-only backup
run_quick_backup() {
    log_info "Starting quick database backup..."
    
    create_backup_dirs
    
    if backup_database; then
        log_info "Quick database backup completed successfully"
        send_notification "SUCCESS" "Quick database backup completed"
    else
        log_error "Quick database backup failed"
        send_notification "ERROR" "Quick database backup failed"
        return 1
    fi
}

# Restore function (use with caution)
restore_database() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        log_error "Please specify backup file to restore"
        return 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup file not found: $backup_file"
        return 1
    fi
    
    log_warning "This will restore the database from: $backup_file"
    read -p "Are you sure? This will overwrite the current database (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Restoring database from $backup_file..."
        
        if [[ "$backup_file" == *.gz ]]; then
            gunzip -c "$backup_file" | mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"
        else
            mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$backup_file"
        fi
        
        if [ $? -eq 0 ]; then
            log_info "Database restore completed successfully"
        else
            log_error "Database restore failed"
            return 1
        fi
    else
        log_info "Database restore cancelled"
    fi
}

# Main script logic
case "${1:-full}" in
    "full")
        run_full_backup
        ;;
    "quick")
        run_quick_backup
        ;;
    "database")
        create_backup_dirs
        backup_database
        ;;
    "files")
        create_backup_dirs
        backup_files
        ;;
    "restore")
        restore_database "$2"
        ;;
    *)
        echo "Usage: $0 {full|quick|database|files|restore <backup_file>}"
        echo ""
        echo "Commands:"
        echo "  full     - Complete backup (database, files, config, logs)"
        echo "  quick    - Quick database backup only"
        echo "  database - Database backup only"
        echo "  files    - Files backup only"
        echo "  restore  - Restore database from backup file"
        exit 1
        ;;
esac

# Make script executable
chmod +x "$0"
