# Nirvana Organics E-commerce - Production Nginx Configuration
# Place this file at: /etc/nginx/sites-available/nirvana-organics

# Rate limiting zones - FIXED FOR ADMIN ACCESS
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=auth:10m rate=30r/m;  # Increased for admin login
limit_req_zone $binary_remote_addr zone=admin:10m rate=60r/m;  # Dedicated admin zone
limit_req_zone $binary_remote_addr zone=upload:10m rate=10r/m;

# Upstream backend servers
upstream nirvana_backend {
    least_conn;
    server 127.0.0.1:5000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream nirvana_admin {
    server 127.0.0.1:3001 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

# Main server block
server {
    listen 80;
    listen [::]:80;
    server_name test.shopnirvanaorganics.com;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "noindex, nofollow" always; # Remove in production
    
    # Hide Nginx version
    server_tokens off;
    
    # Root directory
    root /var/www/nirvana-backend/dist;
    index index.html;
    
    # Logging
    access_log /var/log/nginx/nirvana-access.log combined;
    error_log /var/log/nginx/nirvana-error.log warn;
    
    # Client settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;
    
    # API routes with rate limiting
    location /api/ {
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
        
        # Proxy settings
        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # Cache bypass
        proxy_cache_bypass $http_upgrade;
        
        # Security
        proxy_hide_header X-Powered-By;
    }
    
    # Authentication routes with stricter rate limiting
    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;
        limit_req_status 429;
        
        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # File upload routes with upload rate limiting
    location /api/upload/ {
        limit_req zone=upload burst=3 nodelay;
        limit_req_status 429;

        client_max_body_size 10M;
        client_body_timeout 120s;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 60s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Disable buffering for uploads
        proxy_request_buffering off;
    }

    # Admin API routes (stricter security and rate limiting)
    location /api/admin/ {
        # Security: IP whitelist for admin API access (uncomment and configure as needed)
        # allow ***********/24;  # Your office network
        # allow 10.0.0.0/8;      # Your VPN network
        # deny all;

        # Admin API rate limiting (more permissive for admin operations)
        limit_req zone=admin burst=15 nodelay;
        limit_req_status 429;

        # Admin API specific headers
        add_header X-Admin-API "true" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Admin-Request "true";

        # Shorter timeouts for admin operations
        proxy_connect_timeout 30s;
        proxy_send_timeout 45s;
        proxy_read_timeout 45s;

        # No caching for admin API responses
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Admin panel static files (served directly from filesystem)
    location /admin/ {
        # Security: IP whitelist for admin access (uncomment and configure as needed)
        # allow ***********/24;  # Your office network
        # allow 10.0.0.0/8;      # Your VPN network
        # deny all;

        # Additional security headers for admin panel
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header X-Robots-Tag "noindex, nofollow" always;

        # Admin-specific rate limiting (more permissive for admin access)
        limit_req zone=admin burst=10 nodelay;
        limit_req_status 429;

        # Try to serve admin files, fallback to admin.html for SPA routing
        try_files $uri $uri/ /admin/admin.html;

        # Admin panel specific caching (shorter cache times for admin assets)
        location ~* ^/admin/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
            add_header Vary "Accept-Encoding";

            # Security headers for admin assets
            add_header X-Content-Type-Options "nosniff";
            add_header X-Frame-Options "DENY";
        }

        # Admin HTML files (no caching for admin interface)
        location ~* ^/admin/.*\.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";

            # Security headers for admin HTML
            add_header X-Frame-Options "DENY";
            add_header X-XSS-Protection "1; mode=block";
            add_header X-Content-Type-Options "nosniff";
            add_header X-Robots-Tag "noindex, nofollow";
        }
    }
    
    # Static file serving with caching
    location /uploads/ {
        alias /var/www/nirvana-backend/uploads/;
        
        # Cache settings
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Security
        add_header X-Content-Type-Options "nosniff";
        
        # Prevent execution of scripts
        location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
        
        # CORS for assets
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Range";
    }
    
    # Static assets with long-term caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Enable gzip_static if available
        gzip_static on;
        
        # CORS headers for fonts and assets
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        
        # Security headers
        add_header X-Content-Type-Options "nosniff";
        
        # Try files with fallback
        try_files $uri $uri/ =404;
    }
    
    # Frontend SPA routing
    location / {
        try_files $uri $uri/ /index.html;
        
        # Cache HTML files for shorter time
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
        
        # Security headers for HTML
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
    }
    
    # Health check endpoint (no rate limiting)
    location /api/health {
        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Quick timeouts for health checks
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
        
        # No caching
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Robots.txt
    location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\nDisallow: /admin/\nDisallow: /api/\n";
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Block common exploit attempts
    location ~* \.(php|asp|aspx|jsp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# SSL configuration (will be added by Certbot)
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name test.shopnirvanaorganics.com;
#     
#     # SSL certificates (managed by Certbot)
#     ssl_certificate /etc/letsencrypt/live/test.shopnirvanaorganics.com/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/test.shopnirvanaorganics.com/privkey.pem;
#     
#     # SSL configuration
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
#     
#     # Include the same location blocks as above
# }
