'use strict';

/**
 * Migration: Add Square OAuth authentication support
 * Adds squareId field to Users table for Square OAuth integration
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add Square authentication column to Users table
      await queryInterface.addColumn('Users', 'square_id', {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true
      });

      // Update authProvider enum to include 'square'
      await queryInterface.changeColumn('Users', 'auth_provider', {
        type: Sequelize.ENUM('local', 'google', 'facebook', 'square', 'both'),
        allowNull: true,
        defaultValue: 'local'
      });

      console.log('✅ Successfully added Square OAuth authentication support');
    } catch (error) {
      console.error('❌ Error adding Square OAuth authentication support:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Remove Square authentication column
      await queryInterface.removeColumn('Users', 'square_id');

      // Revert authProvider enum to original values
      await queryInterface.changeColumn('Users', 'auth_provider', {
        type: Sequelize.ENUM('local', 'google', 'facebook', 'both'),
        allowNull: true,
        defaultValue: 'local'
      });

      console.log('✅ Successfully removed Square OAuth authentication support');
    } catch (error) {
      console.error('❌ Error removing Square OAuth authentication support:', error);
      throw error;
    }
  }
};
