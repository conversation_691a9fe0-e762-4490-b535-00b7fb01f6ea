# Nirvana Organics E-commerce - Complete Deployment Guide

This comprehensive guide covers the deployment of the Nirvana Organics E-commerce platform to Hostinger VPS with dual environment setup (testing and production).

## 🎯 Overview

The Nirvana Organics E-commerce platform consists of:
- **Frontend**: React.js customer-facing e-commerce store
- **Admin Panel**: React.js admin interface accessible at `/admin`
- **Backend**: Node.js REST API with MySQL database
- **Payment Integration**: Square payment processing
- **Email System**: SMTP-based notifications

## 📦 Deployment Packages

Two complete deployment packages have been created:

### 1. Testing Environment (`/test.deploy/`)
- **Target Domain**: `test.shopnirvanaorganics.com`
- **Purpose**: Pre-production testing and validation
- **Configuration**: Debug logging, relaxed rate limits
- **Features**: Sandbox payments, verbose logging, testing headers

### 2. Production Environment (`/deploy/`)
- **Target Domains**: `shopnirvanaorganics.com`, `www.shopnirvanaorganics.com`
- **Purpose**: Live e-commerce platform
- **Configuration**: Optimized logging, secure rate limits
- **Features**: Production payments, enhanced security, performance optimization

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Nginx Web Server                        │
│  ┌─────────────────┐  ┌─────────────────┐                 │
│  │   Frontend      │  │   Admin Panel   │                 │
│  │   (React SPA)   │  │   (/admin)      │                 │
│  └─────────────────┘  └─────────────────┘                 │
│                           │                                │
│                           ▼                                │
│  ┌─────────────────────────────────────────────────────────┤
│  │              API Proxy (/api/*)                        │
│  └─────────────────────────────────────────────────────────┤
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                 Node.js Backend Server                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Express   │  │   Square    │  │   Email     │        │
│  │   API       │  │   Payments  │  │   Service   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                    MySQL Database                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Products  │  │    Users    │  │   Orders    │        │
│  │   Categories│  │   Admins    │  │   Payments  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Deployment Steps

### For Testing Environment

1. **Upload Package**:
   ```bash
   # Upload test.deploy.tar.gz to your VPS
   scp test.deploy.tar.gz user@your-vps-ip:/tmp/
   ```

2. **Extract and Deploy**:
   ```bash
   ssh user@your-vps-ip
   cd /tmp
   tar -xzf test.deploy.tar.gz
   cd test.deploy
   chmod +x deploy-testing.sh
   sudo ./deploy-testing.sh
   ```

3. **Configure Environment**:
   ```bash
   sudo nano /var/www/nirvana-backend/.env
   # Update with your testing values
   ```

4. **Setup SSL**:
   ```bash
   sudo certbot --nginx -d test.shopnirvanaorganics.com
   ```

### For Production Environment

1. **Upload Package**:
   ```bash
   # Upload deploy.tar.gz to your VPS
   scp deploy.tar.gz user@your-vps-ip:/tmp/
   ```

2. **Extract and Deploy**:
   ```bash
   ssh user@your-vps-ip
   cd /tmp
   tar -xzf deploy.tar.gz
   cd deploy
   chmod +x deploy-production.sh
   sudo ./deploy-production.sh
   ```

3. **Configure Environment**:
   ```bash
   sudo nano /var/www/nirvana-backend/.env
   # Update with your production values
   ```

4. **Setup SSL**:
   ```bash
   sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com
   ```

## 📋 Prerequisites

### VPS Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **RAM**: Minimum 1GB (Testing), 2GB+ (Production)
- **Storage**: Minimum 10GB (Testing), 20GB+ (Production)
- **Access**: Root or sudo privileges

### Domain Requirements
- **Testing**: `test.shopnirvanaorganics.com`
- **Production**: `shopnirvanaorganics.com`, `www.shopnirvanaorganics.com`
- **DNS**: A records pointing to your VPS IP

### External Services
- **Database**: MySQL 8.0+
- **Email**: Gmail SMTP or equivalent
- **Payments**: Square account (sandbox for testing, production for live)

## ⚙️ Configuration Details

### Environment Variables

Both packages include environment templates that must be configured:

#### Critical Variables (Must Change)
```bash
# Database
DB_PASSWORD=your-secure-database-password

# JWT Secrets (64+ characters for production)
JWT_SECRET=your-very-secure-jwt-secret-key
JWT_REFRESH_SECRET=your-very-secure-refresh-secret-key

# Email
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password

# Square Payments
SQUARE_ACCESS_TOKEN=your-square-access-token
SQUARE_APPLICATION_ID=your-square-application-id

# Security
SESSION_SECRET=your-secure-session-secret
```

#### Environment-Specific Variables
```bash
# Testing Environment
NODE_ENV=testing
APP_URL=https://test.shopnirvanaorganics.com
SQUARE_ENVIRONMENT=sandbox
LOG_LEVEL=debug
RATE_LIMIT_MAX_REQUESTS=200

# Production Environment
NODE_ENV=production
APP_URL=https://shopnirvanaorganics.com
SQUARE_ENVIRONMENT=production
LOG_LEVEL=info
RATE_LIMIT_MAX_REQUESTS=100
```

### Admin Panel Access

The admin panel is accessible at `/admin` on both environments:
- **Testing**: `https://test.shopnirvanaorganics.com/admin`
- **Production**: `https://shopnirvanaorganics.com/admin`

#### Default Admin Creation
```bash
cd /var/www/nirvana-backend
node scripts/create-default-admin.js
```

### SSL Configuration

Both environments require SSL certificates:

#### Let's Encrypt (Recommended)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Testing Environment
sudo certbot --nginx -d test.shopnirvanaorganics.com

# Production Environment
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com
```

## 🔧 Management Commands

### Application Management
```bash
# Check status
sudo pm2 status

# View logs
sudo pm2 logs nirvana-testing    # Testing
sudo pm2 logs nirvana-production # Production

# Restart application
sudo pm2 restart nirvana-testing    # Testing
sudo pm2 restart nirvana-production # Production

# Monitor resources
sudo pm2 monit
```

### Database Management
```bash
cd /var/www/nirvana-backend

# Test connection
node scripts/test-database-connection.js

# Create backup
node scripts/backup-database.js

# Run migrations
node scripts/run-migrations.js

# Create admin user
node scripts/create-default-admin.js
```

### System Monitoring
```bash
# System status
cd /var/www/nirvana-backend
node scripts/system-status.js

# API health check
curl https://shopnirvanaorganics.com/api/health

# Nginx logs
sudo tail -f /var/log/nginx/nirvana-production-access.log
sudo tail -f /var/log/nginx/nirvana-testing-access.log
```

## 🔒 Security Features

### Production Security
- **Enhanced Rate Limiting**: Strict API protection
- **Security Headers**: Comprehensive HTTP security
- **HTTPS Enforcement**: Automatic redirects
- **Admin Protection**: Enhanced admin panel security
- **Firewall**: UFW and fail2ban configuration
- **File Upload Security**: Restricted types and sizes

### Testing Security
- **Relaxed Rate Limits**: Higher limits for testing
- **Debug Headers**: Testing environment identification
- **Robots.txt**: Search engine blocking
- **Verbose Logging**: Detailed troubleshooting logs

## 📊 Performance Features

### Production Optimizations
- **PM2 Clustering**: Multi-core utilization
- **Redis Caching**: Database query optimization
- **Nginx Compression**: Gzip for static assets
- **Asset Caching**: Long-term browser caching
- **CDN Ready**: Optimized for CDN integration

### Monitoring
- **Health Checks**: API endpoint monitoring
- **Log Aggregation**: Centralized logging
- **Resource Monitoring**: PM2 monitoring
- **Error Tracking**: Comprehensive error logs

## 🆘 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check PM2 logs
sudo pm2 logs

# Verify environment
cat /var/www/nirvana-backend/.env

# Test database
cd /var/www/nirvana-backend
node scripts/test-database-connection.js
```

#### Admin Panel Not Accessible
```bash
# Check if admin build exists
ls -la /var/www/nirvana-backend/dist/admin/

# Verify nginx configuration
sudo nginx -t

# Check admin routes in logs
sudo tail -f /var/log/nginx/nirvana-*-access.log | grep admin
```

#### SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew

# Test SSL configuration
sudo nginx -t
```

#### Database Connection Issues
```bash
# Check MySQL status
sudo systemctl status mysql

# Test connection
mysql -u root -p

# Check database exists
mysql -u root -p -e "SHOW DATABASES;"
```

## 🔄 Updates and Maintenance

### Application Updates
1. **Backup Current Installation**
2. **Stop Application**: `sudo pm2 stop nirvana-*`
3. **Upload New Package**
4. **Extract and Replace Files**
5. **Run Migrations**: `node scripts/run-migrations.js`
6. **Start Application**: `sudo pm2 start ecosystem.config.js`

### Security Updates
```bash
# System packages
sudo apt update && sudo apt upgrade

# Node.js dependencies
cd /var/www/nirvana-backend
npm audit fix

# PM2 updates
sudo npm update -g pm2
```

### Backup Strategy
- **Database**: Daily automated backups
- **Files**: Weekly application backups
- **Configuration**: Version control for configs

## 📞 Support

For deployment support and troubleshooting:
- **Documentation**: Check package README files
- **Logs**: Review application and system logs
- **Validation**: Run validation scripts
- **Testing**: Use health check endpoints

---

**Last Updated**: ${new Date().toISOString().split('T')[0]}
**Version**: 1.0.0
**Environment**: Dual (Testing + Production)
