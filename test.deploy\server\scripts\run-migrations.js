#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize');

// Load environment variables
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  dialect: 'mysql',
  logging: console.log,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
};

console.log(`Running migrations for ${process.env.NODE_ENV || 'testing'} environment...`);
console.log(`Database: ${dbConfig.database} on ${dbConfig.host}:${dbConfig.port}`);

// Initialize Sequelize
const sequelize = new Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
  host: dbConfig.host,
  port: dbConfig.port,
  dialect: dbConfig.dialect,
  logging: dbConfig.logging,
  pool: dbConfig.pool
});

// Migration tracking table
const MIGRATIONS_TABLE = 'sequelize_migrations';

async function createMigrationsTable() {
  await sequelize.query(`
    CREATE TABLE IF NOT EXISTS ${MIGRATIONS_TABLE} (
      name VARCHAR(255) NOT NULL PRIMARY KEY,
      executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
}

async function getExecutedMigrations() {
  try {
    const [results] = await sequelize.query(`SELECT name FROM ${MIGRATIONS_TABLE} ORDER BY name`);
    return results.map(row => row.name);
  } catch (error) {
    console.log('Migrations table does not exist yet, creating...');
    await createMigrationsTable();
    return [];
  }
}

async function markMigrationAsExecuted(migrationName) {
  await sequelize.query(`INSERT INTO ${MIGRATIONS_TABLE} (name) VALUES (?)`, {
    replacements: [migrationName]
  });
}

async function runMigrations() {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Ensure migrations table exists
    await createMigrationsTable();

    // Get list of executed migrations
    const executedMigrations = await getExecutedMigrations();
    console.log(`📋 Found ${executedMigrations.length} previously executed migrations.`);

    // Get list of migration files
    const migrationsDir = path.join(__dirname, '..', 'migrations');
    
    if (!fs.existsSync(migrationsDir)) {
      console.log('📁 Creating migrations directory...');
      fs.mkdirSync(migrationsDir, { recursive: true });
    }

    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js'))
      .sort();

    console.log(`📂 Found ${migrationFiles.length} migration files.`);

    // Run pending migrations
    let executedCount = 0;
    
    for (const file of migrationFiles) {
      const migrationName = path.basename(file, '.js');
      
      if (executedMigrations.includes(migrationName)) {
        console.log(`⏭️  Skipping ${migrationName} (already executed)`);
        continue;
      }

      console.log(`🔄 Running migration: ${migrationName}`);
      
      try {
        const migrationPath = path.join(migrationsDir, file);
        const migration = require(migrationPath);
        
        if (typeof migration.up !== 'function') {
          throw new Error(`Migration ${migrationName} does not export an 'up' function`);
        }

        // Run the migration
        await migration.up(sequelize.getQueryInterface(), Sequelize);
        
        // Mark as executed
        await markMigrationAsExecuted(migrationName);
        
        console.log(`✅ Migration ${migrationName} completed successfully`);
        executedCount++;
        
      } catch (error) {
        console.error(`❌ Migration ${migrationName} failed:`, error.message);
        console.error(error.stack);
        process.exit(1);
      }
    }

    if (executedCount === 0) {
      console.log('✨ No pending migrations to run. Database is up to date!');
    } else {
      console.log(`🎉 Successfully executed ${executedCount} migrations!`);
    }

  } catch (error) {
    console.error('❌ Migration process failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Migration process interrupted');
  await sequelize.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Migration process terminated');
  await sequelize.close();
  process.exit(0);
});

// Run migrations
if (require.main === module) {
  runMigrations().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { runMigrations };
