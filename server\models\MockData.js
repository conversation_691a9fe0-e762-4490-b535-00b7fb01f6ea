const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const MockData = sequelize.define('MockData', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  entityType: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'entity_type',
    comment: 'Type of entity (product, order, user, etc.)'
  },
  entityId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'entity_id',
    comment: 'Original entity ID if this is a copy of real data'
  },
  mockId: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    field: 'mock_id',
    comment: 'Unique identifier for the mock data entry'
  },
  data: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: 'The actual mock data content'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'created_by',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Admin user who created this mock data'
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: 'Tags for categorizing mock data'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Description of the mock data entry'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional metadata about the mock data'
  }
}, {
  tableName: 'mock_data',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['entity_type']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['entity_type', 'is_active']
    }
  ]
});

// Instance methods
MockData.prototype.activate = async function() {
  this.isActive = true;
  await this.save();
  return this;
};

MockData.prototype.deactivate = async function() {
  this.isActive = false;
  await this.save();
  return this;
};

MockData.prototype.addTag = async function(tag) {
  if (!this.tags.includes(tag)) {
    this.tags.push(tag);
    await this.save();
  }
  return this;
};

MockData.prototype.removeTag = async function(tag) {
  this.tags = this.tags.filter(t => t !== tag);
  await this.save();
  return this;
};

// Static methods
MockData.getByEntityType = async function(entityType, activeOnly = true) {
  const where = { entityType };
  if (activeOnly) {
    where.isActive = true;
  }
  
  return await this.findAll({
    where,
    order: [['createdAt', 'DESC']]
  });
};

MockData.createMockEntry = async function(entityType, data, createdBy, options = {}) {
  const mockId = `mock_${entityType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  return await this.create({
    entityType,
    mockId,
    data,
    createdBy,
    description: options.description || null,
    tags: options.tags || [],
    metadata: options.metadata || {}
  });
};

MockData.clearByEntityType = async function(entityType, createdBy = null) {
  const where = { entityType };
  if (createdBy) {
    where.createdBy = createdBy;
  }
  
  const result = await this.destroy({ where });
  return result;
};

MockData.clearAll = async function(createdBy = null) {
  const where = {};
  if (createdBy) {
    where.createdBy = createdBy;
  }
  
  const result = await this.destroy({ where });
  return result;
};

MockData.getStats = async function() {
  const stats = await this.findAll({
    attributes: [
      'entityType',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('COUNT', sequelize.literal('CASE WHEN is_active = true THEN 1 END')), 'activeCount']
    ],
    group: ['entityType'],
    raw: true
  });
  
  return stats;
};

module.exports = MockData;
