# Nirvana Organics E-commerce - Production Environment

This package contains the production deployment for the Nirvana Organics E-commerce platform.

## 🎯 Production Environment Details

- **Target Domains**: shopnirvanaorganics.com, www.shopnirvanaorganics.com
- **Environment**: Production
- **Purpose**: Live e-commerce platform
- **Configuration**: Optimized logging, secure rate limits, enhanced security

## 🚀 Quick Deployment

1. **Upload this package to your VPS server**
2. **Extract and run the deployment script:**
   ```bash
   tar -xzf nirvana-production-deployment.tar.gz
   cd deploy
   chmod +x deploy-production.sh
   sudo ./deploy-production.sh
   ```
3. **Configure SSL certificates**
4. **Update DNS records**
5. **Test thoroughly before going live**

## 📋 Prerequisites

- Ubuntu 20.04+ or CentOS 8+ VPS server
- Root or sudo access
- Minimum 2GB RAM, 20GB storage
- Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com

## 🏗️ What's Included

### Frontend Applications
- **Main E-commerce Store**: Accessible at `/` (root path)
- **Admin Panel**: Accessible at `/admin` path with enhanced security
- **Unified Build**: Both applications served from single domain

### Backend Services
- **Node.js API Server**: Complete REST API with authentication
- **Database Integration**: MySQL with automated setup
- **Square Payment Integration**: Production environment
- **Email System**: SMTP configuration for notifications
- **Redis Caching**: Performance optimization

### Production-Specific Features
- **Enhanced Security**: Strict rate limits, security headers
- **SSL/HTTPS**: Full SSL configuration
- **Firewall Configuration**: UFW and fail2ban setup
- **Performance Optimization**: Clustering, caching, compression
- **Monitoring**: Health checks and logging

## ⚙️ Configuration

### Environment Variables
Edit `/var/www/nirvana-backend/.env` with your production values:

- **Database**: MySQL connection for production database
- **JWT Secrets**: Secure authentication tokens (64+ characters)
- **Email**: SMTP configuration for production notifications
- **Square**: Production payment processing credentials
- **Domains**: shopnirvanaorganics.com, www.shopnirvanaorganics.com
- **Security**: Session secrets, CORS origins

### SSL Certificates
For Let's Encrypt:
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com
```

## 🔧 Management Commands

### Application Management
```bash
# Check application status
sudo pm2 status

# View application logs
sudo pm2 logs nirvana-production

# Restart application
sudo pm2 restart nirvana-production

# Stop application
sudo pm2 stop nirvana-production

# Monitor application
sudo pm2 monit
```

### Database Management
```bash
# Create database backup
cd /var/www/nirvana-backend
node scripts/backup-database.js

# Run database migrations
node scripts/run-migrations.js

# Create admin user
node scripts/create-default-admin.js

# Check database status
node scripts/test-database-connection.js
```

### System Monitoring
```bash
# Check system status
cd /var/www/nirvana-backend
node scripts/system-status.js

# Check API health
curl https://shopnirvanaorganics.com/api/health

# Monitor logs
sudo tail -f /var/log/nginx/nirvana-production-access.log
sudo pm2 logs nirvana-production --lines 100
```

## 🔒 Security Features

- **Enhanced Rate Limiting**: Production-level API protection
- **Security Headers**: Comprehensive HTTP security headers
- **HTTPS Enforcement**: Automatic HTTP to HTTPS redirects
- **Admin Protection**: Enhanced security for admin panel
- **Firewall Configuration**: UFW and fail2ban protection
- **File Upload Security**: Restricted file types and sizes
- **IP Whitelisting**: Optional admin access restrictions

## 📊 Performance Features

- **PM2 Clustering**: Multi-core utilization
- **Redis Caching**: Database query optimization
- **Nginx Compression**: Gzip compression for assets
- **Static Asset Caching**: Long-term browser caching
- **CDN Ready**: Optimized for CDN integration

## 🆘 Troubleshooting

### Common Issues

**Application won't start:**
```bash
# Check PM2 logs
sudo pm2 logs nirvana-production

# Verify environment file
cat /var/www/nirvana-backend/.env

# Test database connection
cd /var/www/nirvana-backend
node scripts/test-database-connection.js
```

**SSL certificate issues:**
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew

# Test SSL configuration
sudo nginx -t
```

**Performance issues:**
```bash
# Monitor system resources
htop
sudo pm2 monit

# Check Redis status
redis-cli ping

# Analyze logs
sudo tail -f /var/log/nginx/nirvana-production-error.log
```

## 🔄 Updates and Maintenance

### Application Updates
1. Upload new deployment package
2. Stop the application: `sudo pm2 stop nirvana-production`
3. Backup current installation and database
4. Replace application files
5. Run migrations if needed
6. Start the application: `sudo pm2 start nirvana-production`

### Security Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js dependencies
cd /var/www/nirvana-backend
npm audit fix

# Update PM2
sudo npm update -g pm2
```

### Backup Strategy
- **Database**: Daily automated backups
- **Files**: Weekly file system backups
- **Configuration**: Version control for configurations

---

**Environment**: Production
**Target Domains**: shopnirvanaorganics.com, www.shopnirvanaorganics.com
**Last Updated**: 2025-07-30
**Support**: Nirvana Organics Development Team