# Nirvana Organics E-commerce - Testing Environment Configuration
# Target Domain: test.shopnirvanaorganics.com

# Application Configuration
NODE_ENV=testing
PORT=5000
APP_NAME="Nirvana Organics E-commerce (Testing)"
APP_URL=https://test.shopnirvanaorganics.com

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_testing
DB_USER=nirvana_test_user
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# JWT Configuration
JWT_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_REFRESH_SECRET=CHANGE_THIS_TO_ANOTHER_VERY_SECURE_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
EMAIL_FROM="Nirvana Organics Testing <<EMAIL>>"

# Square Payment Integration (Sandbox)
SQUARE_ACCESS_TOKEN=your-square-sandbox-access-token
SQUARE_APPLICATION_ID=your-square-sandbox-application-id
SQUARE_ENVIRONMENT=sandbox
SQUARE_WEBHOOK_SIGNATURE_KEY=your-webhook-signature-key

# File Upload Configuration
UPLOAD_PATH=/var/www/nirvana-backend/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Security Configuration
BCRYPT_ROUNDS=10
SESSION_SECRET=CHANGE_THIS_TO_A_SECURE_SESSION_SECRET
CORS_ORIGIN=https://test.shopnirvanaorganics.com

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/test.shopnirvanaorganics.com.crt
SSL_KEY_PATH=/etc/ssl/private/test.shopnirvanaorganics.com.key

# Backup Configuration
BACKUP_PATH=/var/www/nirvana-backend/backups
BACKUP_RETENTION_DAYS=7

# Logging Configuration (Debug level for testing)
LOG_LEVEL=debug
LOG_PATH=/var/www/nirvana-backend/logs

# Rate Limiting (Relaxed for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200

# Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_TTL=1800