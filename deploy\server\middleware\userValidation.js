const { body, param, query } = require('express-validator');

// Validation for creating a new user
const validateCreateUser = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),
    
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),
    
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
    
  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
    
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date of birth')
    .custom((value) => {
      const birthDate = new Date(value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13) {
        throw new Error('User must be at least 13 years old');
      }
      
      return true;
    }),
    
  body('role')
    .optional()
    .isIn(['customer', 'admin', 'manager'])
    .withMessage('Role must be customer, admin, or manager'),
    
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value'),
    
  body('isEmailVerified')
    .optional()
    .isBoolean()
    .withMessage('isEmailVerified must be a boolean value')
];

// Validation for updating a user
const validateUpdateUser = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer'),
    
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),
    
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),
    
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
    
  body('password')
    .optional()
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
    
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date of birth')
    .custom((value) => {
      if (value) {
        const birthDate = new Date(value);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        
        if (age < 13) {
          throw new Error('User must be at least 13 years old');
        }
      }
      
      return true;
    }),
    
  body('role')
    .optional()
    .isIn(['customer', 'admin', 'manager'])
    .withMessage('Role must be customer, admin, or manager'),
    
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value'),
    
  body('isEmailVerified')
    .optional()
    .isBoolean()
    .withMessage('isEmailVerified must be a boolean value')
];

// Validation for user ID parameter
const validateUserId = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer')
];

// Validation for bulk operations
const validateBulkOperation = [
  body('userIds')
    .isArray({ min: 1 })
    .withMessage('User IDs must be a non-empty array')
    .custom((userIds) => {
      if (!userIds.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('All user IDs must be positive integers');
      }
      return true;
    }),
    
  body('action')
    .isIn(['activate', 'deactivate', 'change_role'])
    .withMessage('Action must be activate, deactivate, or change_role'),
    
  body('data.role')
    .if(body('action').equals('change_role'))
    .isIn(['customer', 'admin', 'manager'])
    .withMessage('Role must be customer, admin, or manager')
];

// Validation for password reset
const validatePasswordReset = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer'),
    
  body('newPassword')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

// Validation for user listing query parameters
const validateUserQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
    
  query('role')
    .optional()
    .isIn(['all', 'customer', 'admin', 'manager'])
    .withMessage('Role filter must be all, customer, admin, or manager'),
    
  query('status')
    .optional()
    .isIn(['all', 'active', 'inactive'])
    .withMessage('Status filter must be all, active, or inactive'),
    
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'firstName', 'lastName', 'email', 'lastLoginAt'])
    .withMessage('Sort field must be createdAt, firstName, lastName, email, or lastLoginAt'),
    
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
    
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid ISO 8601 date'),
    
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid ISO 8601 date'),
    
  query('includeStats')
    .optional()
    .isBoolean()
    .withMessage('Include stats must be a boolean value')
];

// Role-based access control middleware
const requireSuperAdmin = (req, res, next) => {
  // Check if user has Role association loaded
  const userRole = req.user.Role ? req.user.Role.name : req.user.role;
  if (userRole !== 'super_admin') {
    return res.status(403).json({
      success: false,
      message: 'Super admin access required'
    });
  }
  next();
};

const requireAdminOrManager = (req, res, next) => {
  // Check if user has Role association loaded
  if (req.user.Role && req.user.Role.name) {
    if (!['admin', 'manager', 'super_admin'].includes(req.user.Role.name)) {
      return res.status(403).json({
        success: false,
        message: 'Admin or manager access required'
      });
    }
  } else {
    // Fallback to role field if Role association not loaded
    if (!['admin', 'manager', 'super_admin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Admin or manager access required'
      });
    }
  }
  next();
};

// Prevent self-modification for certain operations
const preventSelfModification = (req, res, next) => {
  const { userId } = req.params;
  const { userIds } = req.body;
  
  if (userId && parseInt(userId) === req.user.id) {
    return res.status(400).json({
      success: false,
      message: 'Cannot perform this operation on your own account'
    });
  }
  
  if (userIds && userIds.includes(req.user.id)) {
    return res.status(400).json({
      success: false,
      message: 'Cannot perform bulk operations on your own account'
    });
  }
  
  next();
};

module.exports = {
  validateCreateUser,
  validateUpdateUser,
  validateUserId,
  validateBulkOperation,
  validatePasswordReset,
  validateUserQuery,
  requireSuperAdmin,
  requireAdminOrManager,
  preventSelfModification
};
