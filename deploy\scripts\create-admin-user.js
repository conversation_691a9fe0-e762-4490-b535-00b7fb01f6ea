#!/usr/bin/env node

/**
 * Create Production Admin User Script
 * Creates a secure admin user account for production
 */

require('dotenv').config();
const bcrypt = require('bcrypt');
const readline = require('readline');
const { sequelize } = require('../server/models/database');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🔄${colors.reset} ${msg}`)
};

/**
 * Create readline interface
 */
function createReadlineInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

/**
 * Prompt user for input
 */
function prompt(question, hideInput = false) {
  const rl = createReadlineInterface();
  
  return new Promise((resolve) => {
    if (hideInput) {
      // Hide password input
      rl.question(question, (answer) => {
        rl.close();
        resolve(answer.trim());
      });
      rl._writeToOutput = function _writeToOutput(stringToWrite) {
        if (stringToWrite.charCodeAt(0) === 13) {
          rl.output.write('\n');
        } else {
          rl.output.write('*');
        }
      };
    } else {
      rl.question(question, (answer) => {
        rl.close();
        resolve(answer.trim());
      });
    }
  });
}

/**
 * Validate email format
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
function validatePassword(password) {
  const minLength = 12;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const errors = [];
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  if (!hasUpperCase) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!hasLowerCase) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!hasNumbers) {
    errors.push('Password must contain at least one number');
  }
  
  if (!hasSpecialChar) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * Create admin role if it doesn't exist
 */
async function ensureAdminRole() {
  try {
    const { Role } = require('../server/models');
    
    let adminRole = await Role.findOne({ where: { name: 'admin' } });
    
    if (!adminRole) {
      adminRole = await Role.create({
        name: 'admin',
        description: 'Administrator role with full access to the system',
        permissions: JSON.stringify(['*']),
        isActive: true
      });
      
      log.success('Admin role created successfully');
    } else {
      log.info('Admin role already exists');
    }
    
    return adminRole;
    
  } catch (error) {
    log.error(`Failed to create admin role: ${error.message}`);
    throw error;
  }
}

/**
 * Create admin user
 */
async function createAdminUser() {
  try {
    console.log(`${colors.cyan}${colors.bright}👤 Create Production Admin User${colors.reset}`);
    console.log(`${colors.cyan}${colors.bright}================================${colors.reset}\n`);
    
    // Test database connection
    log.step('Testing database connection...');
    await sequelize.authenticate();
    log.success('Database connection established');
    
    // Ensure admin role exists
    log.step('Ensuring admin role exists...');
    const adminRole = await ensureAdminRole();
    
    // Get admin user details
    log.step('Collecting admin user information...');
    
    let email;
    while (true) {
      email = await prompt('Admin Email Address: ');
      
      if (!email) {
        log.error('Email address is required');
        continue;
      }
      
      if (!isValidEmail(email)) {
        log.error('Please enter a valid email address');
        continue;
      }
      
      // Check if user already exists
      const { User } = require('../server/models');
      const existingUser = await User.findOne({ where: { email } });
      
      if (existingUser) {
        log.error('A user with this email already exists');
        const overwrite = await prompt('Do you want to update the existing user? (y/N): ');
        if (overwrite.toLowerCase() === 'y' || overwrite.toLowerCase() === 'yes') {
          break;
        }
        continue;
      }
      
      break;
    }
    
    const firstName = await prompt('First Name: ') || 'Admin';
    const lastName = await prompt('Last Name: ') || 'User';
    const phone = await prompt('Phone Number (optional): ') || null;
    
    let password;
    while (true) {
      password = await prompt('Admin Password: ', true);
      console.log(''); // New line after hidden input
      
      if (!password) {
        log.error('Password is required');
        continue;
      }
      
      const validation = validatePassword(password);
      if (!validation.isValid) {
        log.error('Password does not meet security requirements:');
        validation.errors.forEach(error => log.error(`  - ${error}`));
        continue;
      }
      
      const confirmPassword = await prompt('Confirm Password: ', true);
      console.log(''); // New line after hidden input
      
      if (password !== confirmPassword) {
        log.error('Passwords do not match');
        continue;
      }
      
      break;
    }
    
    // Hash password
    log.step('Hashing password...');
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create or update admin user
    const { User } = require('../server/models');
    const existingUser = await User.findOne({ where: { email } });
    
    let adminUser;
    if (existingUser) {
      // Update existing user
      await existingUser.update({
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        isEmailVerified: true,
        isActive: true,
        roleId: adminRole.id
      });
      adminUser = existingUser;
      log.success('Admin user updated successfully');
    } else {
      // Create new user
      adminUser = await User.create({
        email,
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        dateOfBirth: new Date('1980-01-01'), // Default date
        isEmailVerified: true,
        isActive: true,
        roleId: adminRole.id
      });
      log.success('Admin user created successfully');
    }
    
    // Display summary
    console.log(`\n${colors.cyan}${colors.bright}Admin User Summary:${colors.reset}`);
    console.log(`Email: ${colors.green}${adminUser.email}${colors.reset}`);
    console.log(`Name: ${colors.green}${adminUser.firstName} ${adminUser.lastName}${colors.reset}`);
    console.log(`Phone: ${colors.green}${adminUser.phone || 'Not provided'}${colors.reset}`);
    console.log(`Role: ${colors.green}Administrator${colors.reset}`);
    console.log(`Status: ${colors.green}Active${colors.reset}`);
    console.log(`Email Verified: ${colors.green}Yes${colors.reset}`);
    
    log.warning('\n🔐 IMPORTANT SECURITY NOTES:');
    log.warning('1. Store the admin password securely');
    log.warning('2. Change the password after first login');
    log.warning('3. Enable two-factor authentication if available');
    log.warning('4. Use a password manager for secure storage');
    log.warning('5. Never share admin credentials');
    
    log.success('\n🎉 Admin user setup completed successfully!');
    
  } catch (error) {
    log.error(`Failed to create admin user: ${error.message}`);
    throw error;
  } finally {
    await sequelize.close();
  }
}

/**
 * Test admin login
 */
async function testAdminLogin() {
  try {
    log.step('Testing admin login...');
    
    const email = await prompt('Enter admin email to test: ');
    const password = await prompt('Enter admin password: ', true);
    console.log(''); // New line after hidden input
    
    const { User } = require('../server/models');
    const adminUser = await User.findOne({ 
      where: { email },
      include: [{ model: require('../server/models').Role, as: 'Role' }]
    });
    
    if (!adminUser) {
      log.error('Admin user not found');
      return false;
    }
    
    const isValidPassword = await bcrypt.compare(password, adminUser.password);
    
    if (!isValidPassword) {
      log.error('Invalid password');
      return false;
    }
    
    if (!adminUser.isActive) {
      log.error('Admin user is not active');
      return false;
    }
    
    if (!adminUser.Role || adminUser.Role.name !== 'admin') {
      log.error('User does not have admin role');
      return false;
    }
    
    log.success('Admin login test successful!');
    log.info(`Welcome, ${adminUser.firstName} ${adminUser.lastName}`);
    
    return true;
    
  } catch (error) {
    log.error(`Login test failed: ${error.message}`);
    return false;
  } finally {
    await sequelize.close();
  }
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'create':
        await createAdminUser();
        break;
        
      case 'test':
        await testAdminLogin();
        break;
        
      default:
        console.log('Usage:');
        console.log('  node create-admin-user.js create  - Create admin user');
        console.log('  node create-admin-user.js test    - Test admin login');
        break;
    }
  } catch (error) {
    log.error(`Operation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { createAdminUser, testAdminLogin };
