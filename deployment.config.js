// ============================================================================
// Nirvana Organics E-commerce - Deployment Configuration
// ============================================================================

module.exports = {
  // Application Information
  app: {
    name: 'nirvana-organics-backend',
    version: '1.0.0',
    description: 'Nirvana Organics E-commerce Backend API',
    author: 'Nirvana Organics Team'
  },

  // Git Configuration
  git: {
    repository: 'https://github.com/your-username/nirvana-organics-backend.git',
    branch: 'main',
    deployBranch: 'production'
  },

  // Server Configuration
  server: {
    host: 'your-hostinger-server.com',
    username: 'root',
    port: 22,
    deployPath: '/var/www/nirvana-backend',
    backupPath: '/var/www/backups/nirvana-backend',
    logPath: '/var/www/nirvana-backend/logs'
  },

  // Build Configuration
  build: {
    commands: [
      'npm ci --production',
      'npm run build:prod',
      'npm run migrate:prod'
    ],
    excludeFiles: [
      '.git',
      'node_modules',
      '.env*',
      'tests',
      'docs',
      '*.md',
      '.gitignore',
      'deployment.config.js'
    ]
  },

  // Database Configuration
  database: {
    backup: true,
    migrate: true,
    seed: false, // Set to true only for initial deployment
    backupRetention: 7 // days
  },

  // File Upload Configuration
  uploads: {
    backup: true,
    syncDirectories: [
      'uploads/products',
      'uploads/categories',
      'uploads/banners',
      'uploads/documents'
    ]
  },

  // SSL Configuration
  ssl: {
    enabled: true,
    autoRenew: true,
    certPath: '/etc/ssl/certs',
    keyPath: '/etc/ssl/private'
  },

  // Monitoring Configuration
  monitoring: {
    healthCheck: true,
    logRotation: true,
    alerting: {
      email: '<EMAIL>',
      webhook: null
    }
  },

  // Backup Configuration
  backup: {
    enabled: true,
    schedule: '0 2 * * *', // Daily at 2 AM
    retention: 30, // days
    compress: true,
    include: [
      'database',
      'uploads',
      'logs',
      'config'
    ]
  },

  // Environment Specific Settings
  environments: {
    development: {
      branch: 'develop',
      deployPath: '/var/www/nirvana-backend-dev',
      database: {
        backup: false,
        migrate: true,
        seed: true
      }
    },
    staging: {
      branch: 'staging',
      deployPath: '/var/www/nirvana-backend-staging',
      database: {
        backup: true,
        migrate: true,
        seed: false
      }
    },
    production: {
      branch: 'main',
      deployPath: '/var/www/nirvana-backend',
      database: {
        backup: true,
        migrate: true,
        seed: false
      },
      ssl: {
        enabled: true,
        autoRenew: true
      }
    }
  },

  // Deployment Hooks
  hooks: {
    beforeDeploy: [
      'npm run test',
      'npm run lint'
    ],
    afterDeploy: [
      'npm run setup:security',
      'npm run status',
      'systemctl restart nirvana-backend'
    ],
    onFailure: [
      'npm run rollback',
      'systemctl restart nirvana-backend'
    ]
  },

  // Rollback Configuration
  rollback: {
    enabled: true,
    keepReleases: 5,
    autoRollback: false
  },

  // Notification Configuration
  notifications: {
    slack: {
      enabled: false,
      webhook: null,
      channel: '#deployments'
    },
    email: {
      enabled: true,
      recipients: ['<EMAIL>'],
      onSuccess: true,
      onFailure: true
    }
  }
};
