import React, { useState, useEffect } from 'react';
import { useDataEnvironment } from '../../contexts/DataEnvironmentContext';
import { 
  TrashIcon, 
  PlusIcon, 
  BeakerIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface MockDataStats {
  entityType: string;
  count: number;
  activeCount: number;
}

interface MockDataManagerProps {
  className?: string;
}

const MockDataManager: React.FC<MockDataManagerProps> = ({ className = '' }) => {
  const { isMockMode } = useDataEnvironment();
  const [stats, setStats] = useState<MockDataStats[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // API call helper
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const token = localStorage.getItem('token');
    const sessionId = sessionStorage.getItem('data-environment-session-id');
    
    const response = await fetch(`${import.meta.env.VITE_API_URL || '/api'}/admin/data-environment${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'X-Session-Id': sessionId || '',
        ...options.headers
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(errorData.message || 'API request failed');
    }

    return response.json();
  };

  // Load mock data statistics
  const loadStats = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiCall('/stats');
      setStats(response.data.stats || []);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load statistics');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate sample mock data
  const generateSampleData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await apiCall('/generate-sample', { method: 'POST' });
      setSuccess('Sample mock data generated successfully');
      await loadStats();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate sample data');
    } finally {
      setIsLoading(false);
    }
  };

  // Clear mock data for specific entity type
  const clearEntityData = async (entityType: string) => {
    if (!confirm(`Are you sure you want to clear all mock data for ${entityType}?`)) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      await apiCall(`/clear/${entityType}`, { method: 'DELETE' });
      setSuccess(`Mock data cleared for ${entityType}`);
      await loadStats();
    } catch (error) {
      setError(error instanceof Error ? error.message : `Failed to clear ${entityType} data`);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all mock data
  const clearAllData = async () => {
    if (!confirm('Are you sure you want to clear ALL mock data? This action cannot be undone.')) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      await apiCall('/clear-all', { method: 'DELETE' });
      setSuccess('All mock data cleared successfully');
      await loadStats();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to clear all mock data');
    } finally {
      setIsLoading(false);
    }
  };

  // Clear messages after timeout
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 10000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // Load stats on component mount
  useEffect(() => {
    loadStats();
  }, []);

  const totalEntries = stats.reduce((sum, stat) => sum + parseInt(stat.count.toString()), 0);
  const totalActive = stats.reduce((sum, stat) => sum + parseInt(stat.activeCount.toString()), 0);

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <BeakerIcon className="h-6 w-6 text-indigo-600" />
            <h3 className="text-lg font-medium text-gray-900">Mock Data Management</h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadStats}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Status Messages */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
              <span className="text-sm text-red-800">{error}</span>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
              <span className="text-sm text-green-800">{success}</span>
            </div>
          </div>
        )}

        {/* Current Mode Warning */}
        {!isMockMode && (
          <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-2" />
              <span className="text-sm text-yellow-800">
                You are currently in Real Data mode. Switch to Mock Data mode to test with mock data.
              </span>
            </div>
          </div>
        )}

        {/* Statistics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-gray-900">{totalEntries}</div>
            <div className="text-sm text-gray-600">Total Mock Entries</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">{totalActive}</div>
            <div className="text-sm text-gray-600">Active Entries</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-indigo-600">{stats.length}</div>
            <div className="text-sm text-gray-600">Entity Types</div>
          </div>
        </div>

        {/* Entity Statistics */}
        {stats.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Mock Data by Entity Type</h4>
            <div className="space-y-3">
              {stats.map((stat) => (
                <div key={stat.entityType} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="text-sm font-medium text-gray-900 capitalize">
                      {stat.entityType}
                    </div>
                    <div className="text-sm text-gray-600">
                      {stat.activeCount} active / {stat.count} total
                    </div>
                  </div>
                  <button
                    onClick={() => clearEntityData(stat.entityType)}
                    disabled={isLoading}
                    className="inline-flex items-center px-2 py-1 border border-red-300 rounded text-xs font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    <TrashIcon className="h-3 w-3 mr-1" />
                    Clear
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <button
            onClick={generateSampleData}
            disabled={isLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Generate Sample Data
          </button>

          {totalEntries > 0 && (
            <button
              onClick={clearAllData}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Clear All Mock Data
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MockDataManager;
