const express = require('express');
const router = express.Router();
const reviewManagementController = require('../controllers/reviewManagementController');
const { authenticate, requireAdmin } = require('../middleware/auth');
const {
  validateReviewId,
  validateProductId,
  validateReviewStatus,
  validateAdminResponse,
  validateBulkReviewOperation,
  validateReviewQuery,
  validateProductReviewQuery,
  validateDateRange,
  requireReviewManagementAccess,
  checkReviewExists,
  checkProductExists,
  sanitizeReviewContent
} = require('../middleware/reviewValidation');

// All routes require authentication and admin privileges
router.use(authenticate);
router.use(requireAdmin);

// @route   GET /api/admin/reviews
// @desc    Get all reviews with filtering and pagination
// @access  Private (Admin/Manager)
router.get('/', 
  requireReviewManagementAccess,
  validateReviewQuery,
  validateDateRange,
  reviewManagementController.getReviews
);

// @route   GET /api/admin/reviews/statistics
// @desc    Get review statistics
// @access  Private (Admin/Manager)
router.get('/statistics',
  requireReviewManagementAccess,
  reviewManagementController.getReviewStatistics
);

// @route   GET /api/admin/reviews/product/:productId
// @desc    Get reviews for a specific product
// @access  Private (Admin/Manager)
router.get('/product/:productId',
  requireReviewManagementAccess,
  validateProductReviewQuery,
  checkProductExists,
  reviewManagementController.getProductReviews
);

// @route   GET /api/admin/reviews/:reviewId
// @desc    Get single review by ID
// @access  Private (Admin/Manager)
router.get('/:reviewId',
  requireReviewManagementAccess,
  validateReviewId,
  checkReviewExists,
  reviewManagementController.getReviewById
);

// @route   PUT /api/admin/reviews/:reviewId/status
// @desc    Update review status (approve/reject/pending)
// @access  Private (Admin/Manager)
router.put('/:reviewId/status',
  requireReviewManagementAccess,
  validateReviewStatus,
  sanitizeReviewContent,
  checkReviewExists,
  reviewManagementController.updateReviewStatus
);

// @route   POST /api/admin/reviews/:reviewId/response
// @desc    Add admin response to review
// @access  Private (Admin/Manager)
router.post('/:reviewId/response',
  requireReviewManagementAccess,
  validateAdminResponse,
  sanitizeReviewContent,
  checkReviewExists,
  reviewManagementController.addAdminResponse
);

// @route   DELETE /api/admin/reviews/:reviewId
// @desc    Delete review
// @access  Private (Admin/Manager)
router.delete('/:reviewId',
  requireReviewManagementAccess,
  validateReviewId,
  checkReviewExists,
  reviewManagementController.deleteReview
);

// @route   POST /api/admin/reviews/bulk
// @desc    Bulk operations on reviews (approve/reject/delete)
// @access  Private (Admin/Manager)
router.post('/bulk',
  requireReviewManagementAccess,
  validateBulkReviewOperation,
  reviewManagementController.bulkUpdateReviews
);

module.exports = router;
