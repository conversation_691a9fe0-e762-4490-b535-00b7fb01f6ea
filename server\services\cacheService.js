const redis = require('redis');

/**
 * Redis Cache Service
 * Provides caching functionality with Redis backend
 */
class CacheService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.defaultTTL = parseInt(process.env.CACHE_TTL) || 3600; // 1 hour default
    this.isEnabled = process.env.REDIS_HOST && process.env.REDIS_HOST !== '';
    
    if (this.isEnabled) {
      this.connect();
    } else {
      console.log('⚠️ Redis caching disabled - REDIS_HOST not configured');
    }
  }

  /**
   * Connect to Redis server
   */
  async connect() {
    try {
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
      };

      // Add password if configured
      if (process.env.REDIS_PASSWORD) {
        redisConfig.password = process.env.REDIS_PASSWORD;
      }

      this.client = redis.createClient(redisConfig);

      // Handle connection events
      this.client.on('connect', () => {
        console.log('🔗 Redis client connected');
      });

      this.client.on('ready', () => {
        this.isConnected = true;
        console.log('✅ Redis client ready');
      });

      this.client.on('error', (err) => {
        console.error('❌ Redis client error:', err);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        console.log('🔌 Redis client disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error);
      this.isConnected = false;
    }
  }

  /**
   * Check if cache is available
   */
  isAvailable() {
    return this.isEnabled && this.isConnected && this.client;
  }

  /**
   * Get value from cache
   */
  async get(key) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const value = await this.client.get(key);
      if (value) {
        return JSON.parse(value);
      }
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(key, value, ttl = null) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const serializedValue = JSON.stringify(value);
      const expiration = ttl || this.defaultTTL;
      
      await this.client.setEx(key, expiration, serializedValue);
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete value from cache
   */
  async del(key) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Delete multiple keys from cache
   */
  async delPattern(pattern) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
      return true;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return false;
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  /**
   * Get TTL for a key
   */
  async ttl(key) {
    if (!this.isAvailable()) {
      return -1;
    }

    try {
      return await this.client.ttl(key);
    } catch (error) {
      console.error('Cache TTL error:', error);
      return -1;
    }
  }

  /**
   * Increment a numeric value
   */
  async incr(key, amount = 1) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      if (amount === 1) {
        return await this.client.incr(key);
      } else {
        return await this.client.incrBy(key, amount);
      }
    } catch (error) {
      console.error('Cache increment error:', error);
      return null;
    }
  }

  /**
   * Set expiration for a key
   */
  async expire(key, seconds) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client.expire(key, seconds);
      return true;
    } catch (error) {
      console.error('Cache expire error:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats() {
    if (!this.isAvailable()) {
      return {
        connected: false,
        enabled: this.isEnabled
      };
    }

    try {
      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');
      
      return {
        connected: this.isConnected,
        enabled: this.isEnabled,
        memory: info,
        keyspace: keyspace,
        defaultTTL: this.defaultTTL
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        connected: this.isConnected,
        enabled: this.isEnabled,
        error: error.message
      };
    }
  }

  /**
   * Clear all cache
   */
  async flush() {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client.flushAll();
      return true;
    } catch (error) {
      console.error('Cache flush error:', error);
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async disconnect() {
    if (this.client) {
      try {
        await this.client.quit();
        this.isConnected = false;
        console.log('🔌 Redis client disconnected gracefully');
      } catch (error) {
        console.error('Error disconnecting Redis client:', error);
      }
    }
  }

  /**
   * Cache middleware for Express routes
   */
  middleware(ttl = null) {
    return async (req, res, next) => {
      if (!this.isAvailable()) {
        return next();
      }

      const key = `cache:${req.method}:${req.originalUrl}`;
      
      try {
        const cachedData = await this.get(key);
        if (cachedData) {
          return res.json(cachedData);
        }

        // Store original res.json
        const originalJson = res.json;
        
        // Override res.json to cache the response
        res.json = (data) => {
          // Cache the response
          this.set(key, data, ttl);
          
          // Call original res.json
          return originalJson.call(res, data);
        };

        next();
      } catch (error) {
        console.error('Cache middleware error:', error);
        next();
      }
    };
  }
}

// Create singleton instance
const cacheService = new CacheService();

// Graceful shutdown
process.on('SIGINT', async () => {
  await cacheService.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await cacheService.disconnect();
  process.exit(0);
});

module.exports = cacheService;
