{"name": "nirvana-organics-production", "version": "1.0.0", "description": "Nirvana Organics E-commerce Production Environment", "main": "server/index.js", "scripts": {"start": "node server/index.js", "start:production": "NODE_ENV=production node server/index.js", "setup:database": "node scripts/setup-database.js", "create:tables": "node scripts/create-database-tables.js", "seed:database": "node scripts/seed-database.js", "test:database": "node scripts/test-database-connection.js", "backup:database": "node scripts/backup-database.js", "migrate": "node scripts/run-migrations.js", "status": "node scripts/system-status.js", "create:admin": "node scripts/create-default-admin.js", "deploy": "chmod +x deploy-production.sh && ./deploy-production.sh"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "square": "^30.0.0", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "winston": "^3.10.0", "redis": "^4.6.7"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "author": "Nirvana Organics Team", "license": "MIT"}