{"name": "nirvana-organics-production", "version": "1.0.0", "description": "Nirvana Organics E-commerce Platform - Production Environment", "main": "server/index.js", "scripts": {"start": "NODE_ENV=production node server/index.js", "migrate": "NODE_ENV=production node server/scripts/run-migrations.js", "seed": "NODE_ENV=production node server/scripts/seed-database.js", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js --env production", "pm2:reload": "pm2 reload ecosystem.config.js --env production", "pm2:delete": "pm2 delete ecosystem.config.js", "logs": "pm2 logs", "status": "pm2 status", "monit": "pm2 monit", "backup": "/usr/local/bin/nirvana-backup", "maintenance": "/usr/local/bin/nirvana-maintenance"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "sequelize": "^6.35.2", "mysql2": "^3.6.5", "redis": "^4.6.11", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "axios": "^1.6.2", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "uuid": "^9.0.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "moment": "^2.29.4", "lodash": "^4.17.21", "fs-extra": "^11.2.0", "archiver": "^6.0.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "pdf-lib": "^1.17.1", "qrcode": "^1.5.3", "stripe": "^14.9.0", "squareup": "^29.0.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "express-slow-down": "^2.0.1", "express-fileupload": "^1.4.3", "image-size": "^1.0.2", "mime-types": "^2.1.35", "sanitize-html": "^2.11.0", "validator": "^13.11.0", "xss": "^1.0.14", "hpp": "^0.2.3", "express-mongo-sanitize": "^2.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ecommerce", "organic", "production", "nodejs", "express", "mysql", "redis"], "author": "Nirvana Organics", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/nirvana-organics.git"}, "homepage": "https://shopnirvanaorganics.com"}