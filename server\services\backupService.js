const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const cron = require('node-cron');

/**
 * Backup Service
 * Handles database and file backups with retention management
 */
class BackupService {
  constructor() {
    this.backupPath = process.env.BACKUP_PATH || path.join(process.cwd(), 'backups');
    this.retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 7;
    this.isEnabled = process.env.BACKUP_PATH && process.env.BACKUP_PATH !== '';
    
    // Database configuration
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      name: process.env.DB_NAME,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD
    };

    if (this.isEnabled) {
      this.initialize();
    } else {
      console.log('⚠️ Backup service disabled - BACKUP_PATH not configured');
    }
  }

  /**
   * Initialize backup service
   */
  async initialize() {
    try {
      // Create backup directory if it doesn't exist
      await this.ensureBackupDirectory();
      
      // Schedule automatic backups
      this.scheduleBackups();
      
      console.log('✅ Backup service initialized');
      console.log(`📁 Backup path: ${this.backupPath}`);
      console.log(`🗓️ Retention: ${this.retentionDays} days`);
    } catch (error) {
      console.error('❌ Failed to initialize backup service:', error);
      this.isEnabled = false;
    }
  }

  /**
   * Ensure backup directory exists
   */
  async ensureBackupDirectory() {
    try {
      await fs.access(this.backupPath);
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(this.backupPath, { recursive: true });
      console.log(`📁 Created backup directory: ${this.backupPath}`);
    }
  }

  /**
   * Create database backup
   */
  async createDatabaseBackup() {
    if (!this.isEnabled) {
      throw new Error('Backup service is not enabled');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `database_backup_${timestamp}.sql`;
    const backupFilePath = path.join(this.backupPath, backupFileName);

    try {
      console.log('🗄️ Creating database backup...');

      // Create mysqldump command
      const dumpCommand = [
        'mysqldump',
        `--host=${this.dbConfig.host}`,
        `--port=${this.dbConfig.port}`,
        `--user=${this.dbConfig.user}`,
        `--password=${this.dbConfig.password}`,
        '--single-transaction',
        '--routines',
        '--triggers',
        '--add-drop-table',
        '--extended-insert',
        this.dbConfig.name
      ].join(' ');

      // Execute backup
      const { stdout, stderr } = await execAsync(`${dumpCommand} > "${backupFilePath}"`);
      
      if (stderr && !stderr.includes('Warning')) {
        throw new Error(`Database backup failed: ${stderr}`);
      }

      // Verify backup file was created and has content
      const stats = await fs.stat(backupFilePath);
      if (stats.size === 0) {
        throw new Error('Database backup file is empty');
      }

      console.log(`✅ Database backup created: ${backupFileName} (${this.formatFileSize(stats.size)})`);
      
      return {
        success: true,
        fileName: backupFileName,
        filePath: backupFilePath,
        size: stats.size,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Database backup failed:', error);
      
      // Clean up failed backup file
      try {
        await fs.unlink(backupFilePath);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      
      throw error;
    }
  }

  /**
   * Create file system backup (uploads, logs, etc.)
   */
  async createFileBackup() {
    if (!this.isEnabled) {
      throw new Error('Backup service is not enabled');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `files_backup_${timestamp}.tar.gz`;
    const backupFilePath = path.join(this.backupPath, backupFileName);

    try {
      console.log('📁 Creating file system backup...');

      // Directories to backup
      const backupDirs = [
        process.env.UPLOAD_PATH || path.join(process.cwd(), 'uploads'),
        path.join(process.cwd(), 'logs'),
        path.join(process.cwd(), 'config')
      ].filter(dir => {
        try {
          require('fs').accessSync(dir);
          return true;
        } catch {
          return false;
        }
      });

      if (backupDirs.length === 0) {
        console.log('⚠️ No directories found to backup');
        return null;
      }

      // Create tar command
      const tarCommand = [
        'tar',
        '-czf',
        `"${backupFilePath}"`,
        ...backupDirs.map(dir => `"${dir}"`)
      ].join(' ');

      // Execute backup
      const { stdout, stderr } = await execAsync(tarCommand);
      
      if (stderr && !stderr.includes('Warning')) {
        console.warn('File backup warnings:', stderr);
      }

      // Verify backup file was created
      const stats = await fs.stat(backupFilePath);
      
      console.log(`✅ File backup created: ${backupFileName} (${this.formatFileSize(stats.size)})`);
      
      return {
        success: true,
        fileName: backupFileName,
        filePath: backupFilePath,
        size: stats.size,
        directories: backupDirs,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ File backup failed:', error);
      
      // Clean up failed backup file
      try {
        await fs.unlink(backupFilePath);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      
      throw error;
    }
  }

  /**
   * Create complete backup (database + files)
   */
  async createCompleteBackup() {
    if (!this.isEnabled) {
      throw new Error('Backup service is not enabled');
    }

    console.log('🚀 Starting complete backup...');
    
    const results = {
      database: null,
      files: null,
      timestamp: new Date().toISOString(),
      success: false
    };

    try {
      // Create database backup
      results.database = await this.createDatabaseBackup();
      
      // Create file backup
      results.files = await this.createFileBackup();
      
      results.success = true;
      console.log('✅ Complete backup finished successfully');
      
      // Clean up old backups
      await this.cleanupOldBackups();
      
      return results;
    } catch (error) {
      console.error('❌ Complete backup failed:', error);
      results.error = error.message;
      throw error;
    }
  }

  /**
   * Clean up old backup files based on retention policy
   */
  async cleanupOldBackups() {
    if (!this.isEnabled) {
      return;
    }

    try {
      console.log('🧹 Cleaning up old backups...');
      
      const files = await fs.readdir(this.backupPath);
      const backupFiles = files.filter(file => 
        file.includes('backup_') && (file.endsWith('.sql') || file.endsWith('.tar.gz'))
      );

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

      let deletedCount = 0;
      let deletedSize = 0;

      for (const file of backupFiles) {
        const filePath = path.join(this.backupPath, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedCount++;
          deletedSize += stats.size;
          console.log(`🗑️ Deleted old backup: ${file}`);
        }
      }

      if (deletedCount > 0) {
        console.log(`✅ Cleaned up ${deletedCount} old backups (${this.formatFileSize(deletedSize)} freed)`);
      } else {
        console.log('✅ No old backups to clean up');
      }
    } catch (error) {
      console.error('❌ Backup cleanup failed:', error);
    }
  }

  /**
   * List available backups
   */
  async listBackups() {
    if (!this.isEnabled) {
      return [];
    }

    try {
      const files = await fs.readdir(this.backupPath);
      const backupFiles = [];

      for (const file of files) {
        if (file.includes('backup_') && (file.endsWith('.sql') || file.endsWith('.tar.gz'))) {
          const filePath = path.join(this.backupPath, file);
          const stats = await fs.stat(filePath);
          
          backupFiles.push({
            fileName: file,
            filePath: filePath,
            size: stats.size,
            sizeFormatted: this.formatFileSize(stats.size),
            type: file.endsWith('.sql') ? 'database' : 'files',
            created: stats.mtime,
            age: this.getFileAge(stats.mtime)
          });
        }
      }

      // Sort by creation date (newest first)
      backupFiles.sort((a, b) => b.created - a.created);
      
      return backupFiles;
    } catch (error) {
      console.error('❌ Failed to list backups:', error);
      return [];
    }
  }

  /**
   * Get backup service status
   */
  async getStatus() {
    const status = {
      enabled: this.isEnabled,
      backupPath: this.backupPath,
      retentionDays: this.retentionDays,
      lastBackup: null,
      totalBackups: 0,
      totalSize: 0
    };

    if (this.isEnabled) {
      try {
        const backups = await this.listBackups();
        status.totalBackups = backups.length;
        status.totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
        status.totalSizeFormatted = this.formatFileSize(status.totalSize);
        
        if (backups.length > 0) {
          status.lastBackup = backups[0];
        }
      } catch (error) {
        status.error = error.message;
      }
    }

    return status;
  }

  /**
   * Schedule automatic backups
   */
  scheduleBackups() {
    if (!this.isEnabled) {
      return;
    }

    // Schedule daily backup at 2 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        console.log('⏰ Starting scheduled backup...');
        await this.createCompleteBackup();
      } catch (error) {
        console.error('❌ Scheduled backup failed:', error);
      }
    });

    console.log('⏰ Scheduled daily backups at 2:00 AM');
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  /**
   * Get file age in human readable format
   */
  getFileAge(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    }
  }
}

// Create singleton instance
const backupService = new BackupService();

module.exports = backupService;
