const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { authenticate } = require('../middleware/auth');
const discountController = require('../controllers/discountController');

// Public routes
// @route   POST /api/coupons/validate
// @desc    Validate coupon code
// @access  Public
router.post('/coupons/validate', [
  body('couponCode')
    .notEmpty()
    .withMessage('Coupon code is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Coupon code must be between 3 and 50 characters'),
  body('orderData')
    .notEmpty()
    .withMessage('Order data is required')
    .isObject()
    .withMessage('Order data must be an object'),
  body('orderData.subtotal')
    .isNumeric()
    .withMessage('Subtotal must be a number')
    .custom(value => value >= 0)
    .withMessage('Subtotal must be non-negative'),
  body('orderData.items')
    .isArray()
    .withMessage('Items must be an array')
    .notEmpty()
    .withMessage('Items array cannot be empty')
], discountController.validateCoupon);

// @route   GET /api/coupons/available
// @desc    Get available coupons
// @access  Public
router.get('/coupons/available', discountController.getAvailableCoupons);

// @route   POST /api/referrals/apply
// @desc    Apply referral code during checkout
// @access  Public
router.post('/referrals/apply', [
  body('referralCode')
    .notEmpty()
    .withMessage('Referral code is required')
    .isLength({ min: 6, max: 20 })
    .withMessage('Referral code must be between 6 and 20 characters')
], discountController.applyReferralCode);

// Protected routes - require authentication
// @route   POST /api/referrals/generate
// @desc    Generate referral code for user
// @access  Private
router.post('/referrals/generate', authenticate, discountController.generateReferralCode);

// @route   GET /api/referrals/stats
// @desc    Get user's referral statistics
// @access  Private
router.get('/referrals/stats', authenticate, discountController.getReferralStats);

// @route   POST /api/social-shares
// @desc    Record social media share
// @access  Private
router.post('/social-shares', authenticate, [
  body('platform')
    .isIn(['facebook', 'twitter', 'instagram', 'whatsapp', 'email'])
    .withMessage('Invalid social media platform'),
  body('shareUrl')
    .isURL()
    .withMessage('Valid share URL is required'),
  body('couponId')
    .optional()
    .isInt()
    .withMessage('Coupon ID must be an integer')
], discountController.recordSocialShare);

// @route   GET /api/coupons/usage-history
// @desc    Get user's coupon usage history
// @access  Private
router.get('/coupons/usage-history', authenticate, discountController.getCouponUsageHistory);

// @route   GET /api/social-shares/history
// @desc    Get user's social sharing history
// @access  Private
router.get('/social-shares/history', authenticate, discountController.getSocialShareHistory);

module.exports = router;
