#!/usr/bin/env node

/**
 * Security Validation Script for Nirvana Organics E-commerce
 * This script validates that all critical security measures are properly implemented
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}[STEP]${colors.reset} ${msg}`)
};

let securityScore = 0;
let totalChecks = 0;
const issues = [];

function addCheck(passed, message, severity = 'medium') {
  totalChecks++;
  if (passed) {
    securityScore++;
    log.success(message);
  } else {
    issues.push({ message, severity });
    if (severity === 'critical') {
      log.error(message);
    } else {
      log.warning(message);
    }
  }
}

function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  addCheck(exists, `${description}: ${exists ? 'Found' : 'Missing'}`, exists ? 'low' : 'high');
  return exists;
}

function checkEnvironmentSecurity() {
  log.step('1. Checking Environment Security...');
  
  // Check if .env exists
  const envExists = checkFileExists('.env', 'Environment file');
  
  if (envExists) {
    const envContent = fs.readFileSync('.env', 'utf8');
    
    // Check for template values
    const templateValues = envContent.match(/your-[a-zA-Z-]+/g);
    addCheck(!templateValues, 'No template values in .env file', 'critical');
    
    // Check for exposed credentials in repository
    const gitignoreExists = fs.existsSync('.gitignore');
    if (gitignoreExists) {
      const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
      addCheck(gitignoreContent.includes('.env'), '.env file is in .gitignore', 'critical');
    }
    
    // Check JWT secrets strength
    const jwtSecret = envContent.match(/JWT_SECRET=(.+)/);
    const jwtRefreshSecret = envContent.match(/JWT_REFRESH_SECRET=(.+)/);
    
    if (jwtSecret && jwtSecret[1]) {
      addCheck(jwtSecret[1].length >= 32, 'JWT secret is at least 32 characters', 'critical');
      addCheck(!jwtSecret[1].includes('CHANGE_THIS'), 'JWT secret is not default value', 'critical');
    }
    
    if (jwtRefreshSecret && jwtRefreshSecret[1]) {
      addCheck(jwtRefreshSecret[1].length >= 32, 'JWT refresh secret is at least 32 characters', 'critical');
      addCheck(!jwtRefreshSecret[1].includes('CHANGE_THIS'), 'JWT refresh secret is not default value', 'critical');
    }
    
    // Check database password strength
    const dbPassword = envContent.match(/DB_PASSWORD=(.+)/);
    if (dbPassword && dbPassword[1]) {
      addCheck(dbPassword[1].length >= 8, 'Database password is at least 8 characters', 'high');
      addCheck(!/^(password|123456|admin)$/i.test(dbPassword[1]), 'Database password is not common weak password', 'critical');
    }
  }
}

function checkSecurityMiddleware() {
  log.step('2. Checking Security Middleware...');
  
  // Check if security middleware exists
  const securityMiddlewareExists = checkFileExists('server/middleware/security.js', 'Security middleware');
  
  if (securityMiddlewareExists) {
    const securityContent = fs.readFileSync('server/middleware/security.js', 'utf8');
    
    // Check for helmet
    addCheck(securityContent.includes('helmet'), 'Helmet security headers implemented');
    
    // Check for rate limiting
    addCheck(securityContent.includes('rateLimit'), 'Rate limiting implemented');
    
    // Check for input validation
    addCheck(securityContent.includes('express-validator'), 'Input validation implemented');
    
    // Check for XSS protection
    addCheck(securityContent.includes('xss') || securityContent.includes('DOMPurify'), 'XSS protection implemented');
    
    // Check for CORS configuration
    addCheck(securityContent.includes('cors'), 'CORS configuration implemented');
  }
}

function checkLoggingSystem() {
  log.step('3. Checking Logging System...');
  
  // Check if logger exists
  const loggerExists = checkFileExists('server/utils/logger.js', 'Winston logger');
  
  if (loggerExists) {
    const loggerContent = fs.readFileSync('server/utils/logger.js', 'utf8');
    
    // Check for winston
    addCheck(loggerContent.includes('winston'), 'Winston logging library used');
    
    // Check for security logging
    addCheck(loggerContent.includes('securityLogger'), 'Security event logging implemented');
    
    // Check for error logging
    addCheck(loggerContent.includes('globalErrorHandler'), 'Global error handler implemented');
    
    // Check for log rotation
    addCheck(loggerContent.includes('maxsize') && loggerContent.includes('maxFiles'), 'Log rotation configured');
  }
  
  // Check if logs directory exists
  checkFileExists('logs', 'Logs directory');
}

function checkDatabaseSecurity() {
  log.step('4. Checking Database Security...');
  
  // Check database configuration
  const dbConfigExists = checkFileExists('server/config/database.js', 'Database configuration');
  
  if (dbConfigExists) {
    const dbContent = fs.readFileSync('server/config/database.js', 'utf8');
    
    // Check for connection pooling
    addCheck(dbContent.includes('pool'), 'Database connection pooling configured');
    
    // Check for SSL configuration
    addCheck(dbContent.includes('ssl'), 'Database SSL configuration present');
    
    // Check for retry logic
    addCheck(dbContent.includes('retry'), 'Database retry logic implemented');
    
    // Check for timeout configuration
    addCheck(dbContent.includes('timeout'), 'Database timeout configuration present');
  }
}

function checkHealthMonitoring() {
  log.step('5. Checking Health Monitoring...');
  
  // Check if health routes exist
  const healthRoutesExists = checkFileExists('server/routes/health.js', 'Health check routes');
  
  if (healthRoutesExists) {
    const healthContent = fs.readFileSync('server/routes/health.js', 'utf8');
    
    // Check for comprehensive health checks
    addCheck(healthContent.includes('checkDatabase'), 'Database health check implemented');
    addCheck(healthContent.includes('checkEmailService'), 'Email service health check implemented');
    addCheck(healthContent.includes('checkFileStorage'), 'File storage health check implemented');
    addCheck(healthContent.includes('checkMemoryUsage'), 'Memory usage monitoring implemented');
  }
}

function checkProductionConfiguration() {
  log.step('6. Checking Production Configuration...');
  
  // Check PM2 configuration
  const pm2ConfigExists = checkFileExists('ecosystem.config.production.js', 'PM2 production configuration');
  
  if (pm2ConfigExists) {
    const pm2Content = fs.readFileSync('ecosystem.config.production.js', 'utf8');
    
    // Check for cluster mode
    addCheck(pm2Content.includes('cluster'), 'PM2 cluster mode configured');
    
    // Check for memory limits
    addCheck(pm2Content.includes('max_memory_restart'), 'PM2 memory restart limits configured');
    
    // Check for log configuration
    addCheck(pm2Content.includes('error_file') && pm2Content.includes('out_file'), 'PM2 logging configured');
  }
  
  // Check Nginx configuration
  const nginxConfigExists = checkFileExists('nginx.production.conf', 'Nginx production configuration');
  
  if (nginxConfigExists) {
    const nginxContent = fs.readFileSync('nginx.production.conf', 'utf8');
    
    // Check for security headers
    addCheck(nginxContent.includes('X-Frame-Options'), 'Nginx security headers configured');
    
    // Check for rate limiting
    addCheck(nginxContent.includes('limit_req'), 'Nginx rate limiting configured');
    
    // Check for SSL configuration
    addCheck(nginxContent.includes('ssl_'), 'Nginx SSL configuration present');
    
    // Check for gzip compression
    addCheck(nginxContent.includes('gzip'), 'Nginx compression configured');
  }
}

function checkFilePermissions() {
  log.step('7. Checking File Permissions...');
  
  // Check sensitive files
  const sensitiveFiles = ['.env'];
  
  sensitiveFiles.forEach(file => {
    if (fs.existsSync(file)) {
      try {
        const stats = fs.statSync(file);
        const mode = stats.mode & parseInt('777', 8);
        addCheck(mode <= parseInt('600', 8), `${file} has secure permissions (${mode.toString(8)})`, 'high');
      } catch (error) {
        addCheck(false, `Could not check permissions for ${file}`, 'medium');
      }
    }
  });
}

function checkDependencySecurity() {
  log.step('8. Checking Dependency Security...');
  
  // Check package.json for security dependencies
  const packageJsonExists = checkFileExists('package.json', 'Package.json');
  
  if (packageJsonExists) {
    const packageContent = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = { ...packageContent.dependencies, ...packageContent.devDependencies };
    
    // Check for security-related packages
    addCheck(!!dependencies.helmet, 'Helmet security package installed');
    addCheck(!!dependencies['express-rate-limit'], 'Express rate limit package installed');
    addCheck(!!dependencies['express-validator'], 'Express validator package installed');
    addCheck(!!dependencies.winston, 'Winston logging package installed');
    addCheck(!!dependencies.bcrypt || !!dependencies.bcryptjs, 'Password hashing package installed');
  }
}

function generateSecurityReport() {
  log.step('9. Generating Security Report...');
  
  const percentage = Math.round((securityScore / totalChecks) * 100);
  const grade = percentage >= 90 ? 'A' : percentage >= 80 ? 'B' : percentage >= 70 ? 'C' : percentage >= 60 ? 'D' : 'F';
  
  console.log('\n' + '='.repeat(60));
  console.log(`${colors.bright}SECURITY ASSESSMENT REPORT${colors.reset}`);
  console.log('='.repeat(60));
  console.log(`Security Score: ${colors.bright}${securityScore}/${totalChecks} (${percentage}%)${colors.reset}`);
  console.log(`Security Grade: ${colors.bright}${grade}${colors.reset}`);
  console.log('='.repeat(60));
  
  if (issues.length > 0) {
    console.log(`\n${colors.red}SECURITY ISSUES FOUND:${colors.reset}`);
    
    const criticalIssues = issues.filter(i => i.severity === 'critical');
    const highIssues = issues.filter(i => i.severity === 'high');
    const mediumIssues = issues.filter(i => i.severity === 'medium');
    
    if (criticalIssues.length > 0) {
      console.log(`\n${colors.red}CRITICAL (${criticalIssues.length}):${colors.reset}`);
      criticalIssues.forEach(issue => console.log(`  ❌ ${issue.message}`));
    }
    
    if (highIssues.length > 0) {
      console.log(`\n${colors.yellow}HIGH (${highIssues.length}):${colors.reset}`);
      highIssues.forEach(issue => console.log(`  ⚠️  ${issue.message}`));
    }
    
    if (mediumIssues.length > 0) {
      console.log(`\n${colors.blue}MEDIUM (${mediumIssues.length}):${colors.reset}`);
      mediumIssues.forEach(issue => console.log(`  ℹ️  ${issue.message}`));
    }
  } else {
    console.log(`\n${colors.green}✅ No security issues found!${colors.reset}`);
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (percentage < 80) {
    console.log(`${colors.red}⚠️  SECURITY SCORE BELOW RECOMMENDED THRESHOLD${colors.reset}`);
    console.log('Please address the issues above before deploying to production.');
  } else if (percentage < 90) {
    console.log(`${colors.yellow}⚠️  GOOD SECURITY SCORE, BUT ROOM FOR IMPROVEMENT${colors.reset}`);
    console.log('Consider addressing the remaining issues for better security.');
  } else {
    console.log(`${colors.green}✅ EXCELLENT SECURITY SCORE!${colors.reset}`);
    console.log('Your application meets high security standards.');
  }
  
  return percentage >= 80;
}

// Main execution
async function main() {
  console.log(`${colors.bright}Nirvana Organics E-commerce Security Validation${colors.reset}\n`);
  
  try {
    checkEnvironmentSecurity();
    checkSecurityMiddleware();
    checkLoggingSystem();
    checkDatabaseSecurity();
    checkHealthMonitoring();
    checkProductionConfiguration();
    checkFilePermissions();
    checkDependencySecurity();
    
    const passed = generateSecurityReport();
    
    process.exit(passed ? 0 : 1);
  } catch (error) {
    log.error(`Security validation failed: ${error.message}`);
    process.exit(1);
  }
}

main();
