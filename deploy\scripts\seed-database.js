#!/usr/bin/env node

/**
 * Database Seeding Script for Nirvana Organics
 * Seeds initial data for production deployment
 */

const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`)
};

/**
 * Initial categories for Nirvana Organics
 */
const initialCategories = [
  {
    name: 'CBD Flower',
    slug: 'cbd-flower',
    description: 'Premium CBD flower products',
    image: '/images/categories/cbd-flower.jpg',
    isActive: true,
    sortOrder: 1
  },
  {
    name: 'CBD Edibles',
    slug: 'cbd-edibles',
    description: 'Delicious CBD-infused edibles',
    image: '/images/categories/cbd-edibles.jpg',
    isActive: true,
    sortOrder: 2
  },
  {
    name: 'CBD Oils & Tinctures',
    slug: 'cbd-oils-tinctures',
    description: 'High-quality CBD oils and tinctures',
    image: '/images/categories/cbd-oils.jpg',
    isActive: true,
    sortOrder: 3
  },
  {
    name: 'CBD Topicals',
    slug: 'cbd-topicals',
    description: 'CBD creams, balms, and topical products',
    image: '/images/categories/cbd-topicals.jpg',
    isActive: true,
    sortOrder: 4
  },
  {
    name: 'Accessories',
    slug: 'accessories',
    description: 'Smoking accessories and tools',
    image: '/images/categories/accessories.jpg',
    isActive: true,
    sortOrder: 5
  }
];

/**
 * Sample products for demonstration
 */
const sampleProducts = [
  {
    name: 'Premium CBD Flower - Sour Diesel',
    slug: 'premium-cbd-flower-sour-diesel',
    description: 'High-quality CBD flower with energizing effects',
    shortDescription: 'Premium Sour Diesel CBD flower',
    price: 29.99,
    comparePrice: 39.99,
    sku: 'CBD-FLOWER-SD-001',
    trackQuantity: true,
    quantity: 100,
    stock: 100,
    lowStockThreshold: 10,
    weight: 3.5,
    weightUnit: 'g',
    isActive: true,
    isFeatured: true,
    tags: ['cbd', 'flower', 'sativa', 'energizing'],
    images: [
      {
        url: '/images/products/cbd-flower-sour-diesel.jpg',
        alt: 'Premium CBD Flower - Sour Diesel',
        position: 0
      }
    ],
    seoTitle: 'Premium CBD Flower - Sour Diesel | Nirvana Organics',
    seoDescription: 'High-quality Sour Diesel CBD flower with energizing effects. Premium organic CBD products.',
    rating: 4.8,
    reviewCount: 24
  }
];

/**
 * Create Sequelize instance
 */
function createSequelizeInstance() {
  return new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      dialect: 'mysql',
      timezone: '+00:00',
      logging: false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      dialectOptions: {
        charset: 'utf8mb4',
        supportBigNumbers: true,
        bigNumberStrings: true
      }
    }
  );
}

/**
 * Define models inline to avoid import issues
 */
function defineModels(sequelize) {
  const { DataTypes } = require('sequelize');

  const Category = sequelize.define('Category', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    name: { type: DataTypes.STRING, allowNull: false },
    slug: { type: DataTypes.STRING, allowNull: false, unique: true },
    description: { type: DataTypes.TEXT },
    image: { type: DataTypes.STRING },
    isActive: { type: DataTypes.BOOLEAN, defaultValue: true },
    sortOrder: { type: DataTypes.INTEGER, defaultValue: 0 }
  });

  const Product = sequelize.define('Product', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    name: { type: DataTypes.STRING, allowNull: false },
    slug: { type: DataTypes.STRING, allowNull: false, unique: true },
    description: { type: DataTypes.TEXT },
    shortDescription: { type: DataTypes.STRING },
    price: { type: DataTypes.DECIMAL(10, 2), allowNull: false },
    comparePrice: { type: DataTypes.DECIMAL(10, 2) },
    sku: { type: DataTypes.STRING, unique: true },
    trackQuantity: { type: DataTypes.BOOLEAN, defaultValue: true },
    quantity: { type: DataTypes.INTEGER, defaultValue: 0 },
    stock: { type: DataTypes.INTEGER, defaultValue: 0 },
    lowStockThreshold: { type: DataTypes.INTEGER, defaultValue: 10 },
    weight: { type: DataTypes.DECIMAL(8, 2) },
    weightUnit: { type: DataTypes.STRING, defaultValue: 'g' },
    isActive: { type: DataTypes.BOOLEAN, defaultValue: true },
    isFeatured: { type: DataTypes.BOOLEAN, defaultValue: false },
    tags: { type: DataTypes.JSON },
    images: { type: DataTypes.JSON },
    seoTitle: { type: DataTypes.STRING },
    seoDescription: { type: DataTypes.TEXT },
    rating: { type: DataTypes.DECIMAL(3, 2), defaultValue: 0 },
    reviewCount: { type: DataTypes.INTEGER, defaultValue: 0 },
    categoryId: { type: DataTypes.INTEGER, references: { model: 'Categories', key: 'id' } }
  });

  const User = sequelize.define('User', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    firstName: { type: DataTypes.STRING, allowNull: false },
    lastName: { type: DataTypes.STRING, allowNull: false },
    email: { type: DataTypes.STRING, allowNull: false, unique: true },
    password: { type: DataTypes.STRING, allowNull: false },
    role: { type: DataTypes.ENUM('customer', 'admin'), defaultValue: 'customer' },
    isEmailVerified: { type: DataTypes.BOOLEAN, defaultValue: false },
    isActive: { type: DataTypes.BOOLEAN, defaultValue: true }
  });

  // Set up associations
  Category.hasMany(Product, { foreignKey: 'categoryId' });
  Product.belongsTo(Category, { foreignKey: 'categoryId' });

  return { Category, Product, User };
}

/**
 * Seed categories
 */
async function seedCategories(models) {
  log.step('Seeding categories...');

  try {
    for (const categoryData of initialCategories) {
      const [category, created] = await models.Category.findOrCreate({
        where: { slug: categoryData.slug },
        defaults: categoryData
      });

      if (created) {
        log.success(`Created category: ${category.name}`);
      } else {
        log.info(`Category already exists: ${category.name}`);
      }
    }

    return true;
  } catch (error) {
    log.error(`Failed to seed categories: ${error.message}`);
    return false;
  }
}

/**
 * Seed sample products
 */
async function seedProducts(models) {
  log.step('Seeding sample products...');

  try {
    // Get the CBD Flower category
    const cbdFlowerCategory = await models.Category.findOne({ where: { slug: 'cbd-flower' } });

    if (!cbdFlowerCategory) {
      log.warning('CBD Flower category not found, skipping product seeding');
      return true;
    }

    for (const productData of sampleProducts) {
      productData.categoryId = cbdFlowerCategory.id;

      const [product, created] = await models.Product.findOrCreate({
        where: { slug: productData.slug },
        defaults: productData
      });

      if (created) {
        log.success(`Created product: ${product.name}`);
      } else {
        log.info(`Product already exists: ${product.name}`);
      }
    }

    return true;
  } catch (error) {
    log.error(`Failed to seed products: ${error.message}`);
    return false;
  }
}

/**
 * Create admin user
 */
async function createAdminUser(models) {
  log.step('Creating admin user...');

  try {
    const bcrypt = require('bcrypt');

    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'NirvanaAdmin2025!';

    const [admin, created] = await models.User.findOrCreate({
      where: { email: adminEmail },
      defaults: {
        firstName: 'Admin',
        lastName: 'User',
        email: adminEmail,
        password: await bcrypt.hash(adminPassword, 12),
        role: 'admin',
        isEmailVerified: true,
        isActive: true
      }
    });

    if (created) {
      log.success(`Created admin user: ${admin.email}`);
      log.warning(`Admin password: ${adminPassword}`);
      log.warning('Please change the admin password after first login!');
    } else {
      log.info(`Admin user already exists: ${admin.email}`);
    }

    return true;
  } catch (error) {
    log.error(`Failed to create admin user: ${error.message}`);
    return false;
  }
}

/**
 * Main seeding function
 */
async function seedDatabase() {
  log.info('Seeding Nirvana Organics Database...');
  log.info('====================================');

  const sequelize = createSequelizeInstance();

  try {
    // Test connection
    await sequelize.authenticate();
    log.success('Database connection established');

    // Define models
    const models = defineModels(sequelize);

    // Seed data
    const categorySuccess = await seedCategories(models);
    const productSuccess = await seedProducts(models);
    const adminSuccess = await createAdminUser(models);

    if (categorySuccess && productSuccess && adminSuccess) {
      log.success('Database seeding completed successfully!');
      return true;
    } else {
      log.error('Some seeding operations failed');
      return false;
    }

  } catch (error) {
    log.error(`Database seeding failed: ${error.message}`);
    return false;
  } finally {
    await sequelize.close();
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const success = await seedDatabase();
    
    log.info('====================================');
    if (success) {
      log.success('Database seeding completed!');
      log.info('Your database is ready for production use.');
      process.exit(0);
    } else {
      log.error('Database seeding failed!');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Seeding failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle script arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node scripts/seed-database.js [--help]');
  console.log('');
  console.log('This script seeds the database with initial data including:');
  console.log('- Product categories');
  console.log('- Sample products');
  console.log('- Admin user account');
  console.log('');
  console.log('Environment variables:');
  console.log('- ADMIN_EMAIL: Admin user email (default: <EMAIL>)');
  console.log('- ADMIN_PASSWORD: Admin user password (default: NirvanaAdmin2025!)');
  process.exit(0);
}

// Run seeding if called directly
if (require.main === module) {
  main();
}

module.exports = { seedDatabase };
