#!/bin/bash

# Nginx Configuration Script for Nirvana Organics E-commerce
# This script configures Nginx for the unified deployment

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${BLUE}======================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}======================================${NC}"
}

# Configuration variables
NGINX_CONF="/etc/nginx/sites-available/nirvana-organics"
NGINX_ENABLED="/etc/nginx/sites-enabled/nirvana-organics"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

log_header "🌐 Configuring Nginx for Nirvana Organics"

# Prompt for domain name
read -p "Enter your domain name (e.g., yourdomain.com): " DOMAIN_NAME
if [ -z "$DOMAIN_NAME" ]; then
    log_error "Domain name is required"
    exit 1
fi

log_info "Configuring Nginx for domain: $DOMAIN_NAME"

# Create Nginx configuration
log_info "Creating Nginx configuration..."
cat > $NGINX_CONF << EOF
# Nirvana Organics E-commerce Nginx Configuration - Unified Deployment
# Domain: $DOMAIN_NAME

# Redirect HTTP to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    
    # Let's Encrypt challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# Main server block - serves both main frontend and admin panel
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;

    # SSL Configuration (will be configured by Certbot)
    ssl_certificate /etc/ssl/certs/$DOMAIN_NAME.crt;
    ssl_certificate_key /etc/ssl/private/$DOMAIN_NAME.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers (General)
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Root directory for unified frontend (contains both main app and admin)
    root /var/www/nirvana-backend/frontend;
    index index.html;

    # Rate limiting for API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        # API routes - proxy to Node.js backend
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Rate limiting for authentication endpoints
    location ~ ^/api/(auth|login|register) {
        limit_req zone=login burst=5 nodelay;
        
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Admin panel routes with enhanced security headers
    location /admin {
        # Admin-specific security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Robots-Tag "noindex, nofollow" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';" always;
        
        # Handle admin panel routing
        alias /var/www/nirvana-backend/frontend/admin;
        try_files \$uri \$uri/ /admin/admin.html;
    }
    
    # Admin assets and static files
    location /admin/ {
        # Admin-specific security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Robots-Tag "noindex, nofollow" always;
        
        alias /var/www/nirvana-backend/frontend/admin/;
        try_files \$uri \$uri/ /admin/admin.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
        }
    }

    # WebSocket support for real-time features
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Static files and uploads
    location /uploads/ {
        alias /var/www/nirvana-backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Security for uploaded files
        location ~* \.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }

    # Main frontend routes with general security headers
    location / {
        # Main frontend security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
        }
    }

    # Security: Block access to sensitive files
    location ~ /\.(ht|git|env) {
        deny all;
    }
    
    location ~ /\.(log|conf)$ {
        deny all;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
    
    # Security and performance optimizations
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
}
EOF

log_success "Nginx configuration created"

# Test Nginx configuration
log_info "Testing Nginx configuration..."
if nginx -t; then
    log_success "Nginx configuration is valid"
else
    log_error "Nginx configuration test failed"
    exit 1
fi

# Enable the site
log_info "Enabling Nginx site..."
ln -sf $NGINX_CONF $NGINX_ENABLED

# Remove default site if it exists
if [ -f "/etc/nginx/sites-enabled/default" ]; then
    rm -f /etc/nginx/sites-enabled/default
    log_info "Removed default Nginx site"
fi

# Reload Nginx
log_info "Reloading Nginx..."
systemctl reload nginx
log_success "Nginx reloaded successfully"

# Create directory for Let's Encrypt challenges
mkdir -p /var/www/html/.well-known/acme-challenge
chown -R www-data:www-data /var/www/html
log_success "Created Let's Encrypt challenge directory"

log_header "✅ Nginx Configuration Complete"
log_success "Nginx has been configured for $DOMAIN_NAME"
log_info ""
log_info "Configuration details:"
log_info "  • Domain: $DOMAIN_NAME"
log_info "  • Main frontend: https://$DOMAIN_NAME/"
log_info "  • Admin panel: https://$DOMAIN_NAME/admin"
log_info "  • API endpoints: https://$DOMAIN_NAME/api/*"
log_info ""
log_warning "Next steps:"
log_warning "1. Ensure your domain DNS points to this server"
log_warning "2. Install SSL certificate with: certbot --nginx -d $DOMAIN_NAME -d www.$DOMAIN_NAME"
log_warning "3. Start your Node.js application"
log_info ""
log_info "To install SSL certificate now, run:"
log_info "  certbot --nginx -d $DOMAIN_NAME -d www.$DOMAIN_NAME"
