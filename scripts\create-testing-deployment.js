#!/usr/bin/env node

/**
 * Testing Environment Deployment Package Creator
 * 
 * Creates a complete testing deployment package with:
 * - Staging configuration for test.shopnirvanaorganics.com
 * - Debug logging and relaxed rate limits
 * - All required files and scripts for VPS deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(70));
    console.log(`🚀 ${msg}`);
    console.log('='.repeat(70));
  }
};

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log.info(`Created directory: ${dirPath}`);
  }
}

function copyDirectory(src, dest) {
  ensureDirectoryExists(dest);
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    log.info(`Removed directory: ${dirPath}`);
  }
}

async function createTestingDeployment() {
  try {
    log.header('Creating Testing Environment Deployment Package');
    
    const deploymentDir = 'test.deploy';
    
    // Step 1: Create directory structure
    log.header('Creating Directory Structure');
    const requiredDirs = [
      'config',
      'scripts',
      'nginx',
      'systemd',
      'ssl',
      'backups',
      'logs',
      'docs'
    ];
    
    ensureDirectoryExists(deploymentDir);
    requiredDirs.forEach(dir => {
      ensureDirectoryExists(path.join(deploymentDir, dir));
    });
    
    // Step 2: Copy unified frontend build
    log.header('Copying Frontend Build');
    if (!fs.existsSync('dist')) {
      throw new Error('Unified build not found. Please run npm run build:unified first.');
    }
    copyDirectory('dist', path.join(deploymentDir, 'dist'));
    log.success('Copied unified frontend build');
    
    // Step 3: Copy backend server
    log.header('Copying Backend Server');
    copyDirectory('server', path.join(deploymentDir, 'server'));
    log.success('Copied backend server');
    
    // Step 4: Copy scripts
    log.header('Copying Scripts');
    copyDirectory('scripts', path.join(deploymentDir, 'scripts'));
    log.success('Copied deployment scripts');
    
    // Step 5: Create testing-specific configurations
    log.header('Creating Testing Environment Configurations');
    await createTestingConfigurations(deploymentDir);
    
    // Step 6: Create deployment scripts
    log.header('Creating Deployment Scripts');
    await createTestingDeploymentScripts(deploymentDir);
    
    // Step 7: Create package files
    log.header('Creating Package Files');
    await createTestingPackageFiles(deploymentDir);
    
    // Step 8: Create documentation
    log.header('Creating Documentation');
    await createTestingDocumentation(deploymentDir);
    
    log.header('Testing Deployment Package Complete');
    log.success('Testing environment deployment package created successfully!');
    log.info(`Package location: ${path.resolve(deploymentDir)}`);
    log.info(`Target domain: test.shopnirvanaorganics.com`);
    log.info(`Environment: Testing/Staging`);
    
  } catch (error) {
    log.error(`Testing deployment package creation failed: ${error.message}`);
    process.exit(1);
  }
}

async function createTestingConfigurations(deploymentDir) {
  // Create testing environment configuration
  const envTemplate = `# Nirvana Organics E-commerce - Testing Environment Configuration
# Target Domain: test.shopnirvanaorganics.com

# Application Configuration
NODE_ENV=testing
PORT=5000
APP_NAME="Nirvana Organics E-commerce (Testing)"
APP_URL=https://test.shopnirvanaorganics.com

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_testing
DB_USER=nirvana_test_user
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# JWT Configuration
JWT_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_REFRESH_SECRET=CHANGE_THIS_TO_ANOTHER_VERY_SECURE_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
EMAIL_FROM="Nirvana Organics Testing <<EMAIL>>"

# Square Payment Integration (Sandbox)
SQUARE_ACCESS_TOKEN=your-square-sandbox-access-token
SQUARE_APPLICATION_ID=your-square-sandbox-application-id
SQUARE_ENVIRONMENT=sandbox
SQUARE_WEBHOOK_SIGNATURE_KEY=your-webhook-signature-key

# File Upload Configuration
UPLOAD_PATH=/var/www/nirvana-backend/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Security Configuration
BCRYPT_ROUNDS=10
SESSION_SECRET=CHANGE_THIS_TO_A_SECURE_SESSION_SECRET
CORS_ORIGIN=https://test.shopnirvanaorganics.com

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/test.shopnirvanaorganics.com.crt
SSL_KEY_PATH=/etc/ssl/private/test.shopnirvanaorganics.com.key

# Backup Configuration
BACKUP_PATH=/var/www/nirvana-backend/backups
BACKUP_RETENTION_DAYS=7

# Logging Configuration (Debug level for testing)
LOG_LEVEL=debug
LOG_PATH=/var/www/nirvana-backend/logs

# Rate Limiting (Relaxed for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200

# Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_TTL=1800`;

  fs.writeFileSync(path.join(deploymentDir, 'config', '.env.testing.template'), envTemplate);
  log.success('Created testing environment configuration template');

  // Create testing nginx configuration
  const nginxConfig = `# Nirvana Organics E-commerce - Testing Environment Nginx Configuration
# Target Domain: test.shopnirvanaorganics.com

# Rate limiting zones - RELAXED FOR TESTING
limit_req_zone $binary_remote_addr zone=api:10m rate=200r/m;
limit_req_zone $binary_remote_addr zone=auth:10m rate=60r/m;
limit_req_zone $binary_remote_addr zone=admin:10m rate=120r/m;
limit_req_zone $binary_remote_addr zone=upload:10m rate=20r/m;

# Upstream backend servers
upstream nirvana_backend {
    least_conn;
    server 127.0.0.1:5000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Main server block
server {
    listen 80;
    listen [::]:80;
    server_name test.shopnirvanaorganics.com;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "noindex, nofollow" always;
    add_header X-Environment "testing" always;

    # Hide Nginx version
    server_tokens off;

    # Root directory
    root /var/www/nirvana-backend/dist;
    index index.html;

    # Logging (verbose for testing)
    access_log /var/log/nginx/nirvana-testing-access.log combined;
    error_log /var/log/nginx/nirvana-testing-error.log debug;

    # Client settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;

    # API routes with relaxed rate limiting
    location /api/ {
        limit_req zone=api burst=40 nodelay;
        limit_req_status 429;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        proxy_cache_bypass $http_upgrade;
        proxy_hide_header X-Powered-By;
    }

    # Authentication routes
    location /api/auth/ {
        limit_req zone=auth burst=10 nodelay;
        limit_req_status 429;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Admin API routes (more permissive for testing)
    location /api/admin/ {
        limit_req zone=admin burst=30 nodelay;
        limit_req_status 429;

        add_header X-Admin-API "true" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Environment "testing" always;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Admin-Request "true";
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 30s;
        proxy_send_timeout 45s;
        proxy_read_timeout 45s;

        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Admin panel static files
    location /admin/ {
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header X-Robots-Tag "noindex, nofollow" always;
        add_header X-Environment "testing" always;

        limit_req zone=admin burst=20 nodelay;
        limit_req_status 429;

        try_files $uri $uri/ /admin/admin.html;

        location ~* ^/admin/.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
            add_header Vary "Accept-Encoding";
            add_header X-Content-Type-Options "nosniff";
            add_header X-Frame-Options "DENY";
            add_header X-Environment "testing";
        }

        location ~* ^/admin/.*\\.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header X-Frame-Options "DENY";
            add_header X-XSS-Protection "1; mode=block";
            add_header X-Content-Type-Options "nosniff";
            add_header X-Robots-Tag "noindex, nofollow";
            add_header X-Environment "testing";
        }
    }

    # Static file serving
    location /uploads/ {
        alias /var/www/nirvana-backend/uploads/;

        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        add_header X-Content-Type-Options "nosniff";
        add_header X-Environment "testing";

        location ~* \\.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }

        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Range";
    }

    # Static assets
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        add_header X-Environment "testing";

        gzip_static on;

        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header X-Content-Type-Options "nosniff";

        try_files $uri $uri/ =404;
    }

    # Frontend SPA routing
    location / {
        try_files $uri $uri/ /index.html;

        location ~* \\.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
            add_header X-Environment "testing";
        }

        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
        add_header X-Environment "testing";
    }

    # Health check endpoint
    location /api/health {
        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "testing";

        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;

        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        add_header X-Environment "testing";
    }

    # Robots.txt for testing
    location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\\nDisallow: /\\n";
    }

    # Security: Block access to sensitive files
    location ~ /\\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \\.(env|log|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~* \\.(php|asp|aspx|jsp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}`;

  fs.writeFileSync(path.join(deploymentDir, 'nginx', 'nirvana-testing.conf'), nginxConfig);
  log.success('Created testing nginx configuration');

  // Create PM2 ecosystem configuration for testing
  const pm2Config = `module.exports = {
  apps: [
    {
      name: 'nirvana-testing',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend',
      instances: 1,
      exec_mode: 'fork',

      env: {
        NODE_ENV: 'testing',
        PORT: 5000
      },
      env_file: '.env',

      max_memory_restart: '512M',
      node_args: '--max-old-space-size=512',

      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/testing-error.log',
      out_file: './logs/testing-out.log',
      log_file: './logs/testing-combined.log',
      merge_logs: true,
      time: true,

      autorestart: true,
      max_restarts: 10,
      min_uptime: '30s',
      restart_delay: 3000,

      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'dist',
        '.git'
      ],

      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000
    }
  ]
};`;

  fs.writeFileSync(path.join(deploymentDir, 'ecosystem.config.testing.js'), pm2Config);
  log.success('Created PM2 testing configuration');
}

async function createTestingDeploymentScripts(deploymentDir) {
  // Create main deployment script for testing
  const deployScript = `#!/bin/bash

# Nirvana Organics E-commerce - Testing Environment Deployment Script
# Target Domain: test.shopnirvanaorganics.com

set -e

# Color codes
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
BLUE='\\033[0;34m'
NC='\\033[0m'

log_info() {
    echo -e "\${BLUE}[INFO]\${NC} \$1"
}

log_success() {
    echo -e "\${GREEN}[SUCCESS]\${NC} \$1"
}

log_warning() {
    echo -e "\${YELLOW}[WARNING]\${NC} \$1"
}

log_error() {
    echo -e "\${RED}[ERROR]\${NC} \$1"
}

log_header() {
    echo -e "\\n\${BLUE}======================================\${NC}"
    echo -e "\${BLUE}\$1\${NC}"
    echo -e "\${BLUE}======================================\${NC}"
}

# Configuration
APP_NAME="nirvana-organics-testing"
APP_USER="nirvana"
APP_DIR="/var/www/nirvana-backend"
NGINX_CONF="/etc/nginx/sites-available/nirvana-testing"
SYSTEMD_SERVICE="/etc/systemd/system/nirvana-testing.service"

if [[ \$EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

log_header "🚀 Starting Nirvana Organics Testing Environment Deployment"

# System preparation
log_header "📋 System Preparation"
apt update
apt install -y curl wget gnupg2 software-properties-common

# Install Node.js
log_info "Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# Install Nginx
log_info "Installing Nginx..."
apt install -y nginx

# Install MySQL
log_info "Installing MySQL..."
apt install -y mysql-server

# Install PM2
log_info "Installing PM2..."
npm install -g pm2

# Create application user
log_header "👤 Setting Up Application User"
if ! id "\$APP_USER" &>/dev/null; then
    log_info "Creating application user: \$APP_USER"
    useradd -m -s /bin/bash \$APP_USER
    usermod -aG sudo \$APP_USER
    log_success "Created application user: \$APP_USER"
else
    log_info "Application user already exists: \$APP_USER"
fi

# Create directory structure
log_header "📁 Creating Directory Structure"
mkdir -p \$APP_DIR/{logs,uploads,backups,config}
mkdir -p \$APP_DIR/uploads/{products,categories,banners,documents}

# Copy application files
log_header "📋 Copying Application Files"
cp -r server/ \$APP_DIR/
cp -r dist/ \$APP_DIR/
cp -r scripts/ \$APP_DIR/
cp -r config/ \$APP_DIR/
cp ecosystem.config.testing.js \$APP_DIR/ecosystem.config.js

# Set permissions
log_header "🔒 Setting File Permissions"
chown -R \$APP_USER:\$APP_USER \$APP_DIR
chmod -R 755 \$APP_DIR
chmod 700 \$APP_DIR/config

# Install dependencies
log_header "📦 Installing Dependencies"
cd \$APP_DIR
sudo -u \$APP_USER npm ci --production --silent

# Configure environment
log_header "⚙️ Environment Configuration"
if [ ! -f "\$APP_DIR/.env" ]; then
    cp config/.env.testing.template \$APP_DIR/.env
    chown \$APP_USER:\$APP_USER \$APP_DIR/.env
    chmod 600 \$APP_DIR/.env
    log_warning "Please edit \$APP_DIR/.env with your actual configuration values"
    read -p "Press Enter after you have configured the .env file..."
fi

# Configure Nginx
log_header "🌐 Configuring Nginx"
cp nginx/nirvana-testing.conf \$NGINX_CONF
ln -sf \$NGINX_CONF /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
systemctl enable nginx

# Setup database
log_header "🗄️ Database Setup"
sudo -u \$APP_USER node scripts/setup-database.js

# Start application with PM2
log_header "🚀 Starting Application"
sudo -u \$APP_USER pm2 start ecosystem.config.js
sudo -u \$APP_USER pm2 save
pm2 startup
sudo env PATH=\$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u \$APP_USER --hp /home/<USER>

log_header "🎉 Testing Environment Deployment Complete!"
log_success "Nirvana Organics Testing Environment deployed successfully!"
log_info "Domain: test.shopnirvanaorganics.com"
log_info "Environment: Testing/Staging"
log_info "Application Directory: \$APP_DIR"`;

  fs.writeFileSync(path.join(deploymentDir, 'deploy-testing.sh'), deployScript);
  log.success('Created testing deployment script');
}

async function createTestingPackageFiles(deploymentDir) {
  // Create package.json for testing deployment
  const packageJson = {
    name: "nirvana-organics-testing",
    version: "1.0.0",
    description: "Nirvana Organics E-commerce Testing Environment",
    main: "server/index.js",
    scripts: {
      start: "node server/index.js",
      "start:testing": "NODE_ENV=testing node server/index.js",
      "setup:database": "node scripts/setup-database.js",
      "create:tables": "node scripts/create-database-tables.js",
      "seed:database": "node scripts/seed-database.js",
      "test:database": "node scripts/test-database-connection.js",
      "backup:database": "node scripts/backup-database.js",
      "migrate": "node scripts/run-migrations.js",
      "status": "node scripts/system-status.js",
      "create:admin": "node scripts/create-default-admin.js",
      "deploy": "chmod +x deploy-testing.sh && ./deploy-testing.sh"
    },
    dependencies: {
      express: "^4.18.2",
      mysql2: "^3.6.0",
      sequelize: "^6.32.1",
      bcryptjs: "^2.4.3",
      jsonwebtoken: "^9.0.2",
      cors: "^2.8.5",
      helmet: "^7.0.0",
      "express-rate-limit": "^6.8.1",
      multer: "^1.4.5-lts.1",
      nodemailer: "^6.9.4",
      square: "^30.0.0",
      dotenv: "^16.3.1",
      "express-validator": "^7.0.1",
      winston: "^3.10.0"
    },
    engines: {
      node: ">=18.0.0",
      npm: ">=8.0.0"
    },
    author: "Nirvana Organics Team",
    license: "MIT"
  };

  fs.writeFileSync(
    path.join(deploymentDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );
  log.success('Created testing package.json');
}

async function createTestingDocumentation(deploymentDir) {
  const readmeContent = `# Nirvana Organics E-commerce - Testing Environment

This package contains the testing/staging deployment for the Nirvana Organics E-commerce platform.

## 🎯 Testing Environment Details

- **Target Domain**: test.shopnirvanaorganics.com
- **Environment**: Testing/Staging
- **Purpose**: Pre-production testing and validation
- **Configuration**: Debug logging, relaxed rate limits

## 🚀 Quick Deployment

1. **Upload this package to your VPS server**
2. **Extract and run the deployment script:**
   \`\`\`bash
   tar -xzf nirvana-testing-deployment.tar.gz
   cd test.deploy
   chmod +x deploy-testing.sh
   sudo ./deploy-testing.sh
   \`\`\`
3. **Configure your domain DNS to point to the server**
4. **Set up SSL certificates for test.shopnirvanaorganics.com**

## 📋 Prerequisites

- Ubuntu 20.04+ or CentOS 8+ VPS server
- Root or sudo access
- Minimum 1GB RAM, 10GB storage
- Domain name: test.shopnirvanaorganics.com

## 🏗️ What's Included

### Frontend Applications
- **Main E-commerce Store**: Accessible at \`/\` (root path)
- **Admin Panel**: Accessible at \`/admin\` path
- **Unified Build**: Both applications served from single domain

### Backend Services
- **Node.js API Server**: Complete REST API with authentication
- **Database Integration**: MySQL with automated setup
- **Square Payment Integration**: Sandbox environment
- **Email System**: SMTP configuration for notifications

### Testing-Specific Features
- **Debug Logging**: Verbose logging for troubleshooting
- **Relaxed Rate Limits**: Higher limits for testing scenarios
- **Testing Headers**: X-Environment headers for identification
- **Sandbox Payments**: Square sandbox integration

## ⚙️ Configuration

### Environment Variables
Edit \`/var/www/nirvana-backend/.env\` with your testing values:

- **Database**: MySQL connection for testing database
- **JWT Secrets**: Secure authentication tokens
- **Email**: SMTP configuration for test notifications
- **Square**: Sandbox payment processing credentials
- **Domain**: test.shopnirvanaorganics.com

### SSL Certificates
For Let's Encrypt:
\`\`\`bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d test.shopnirvanaorganics.com
\`\`\`

## 🔧 Management Commands

### Application Management
\`\`\`bash
# Check application status
sudo pm2 status

# View application logs
sudo pm2 logs nirvana-testing

# Restart application
sudo pm2 restart nirvana-testing

# Stop application
sudo pm2 stop nirvana-testing
\`\`\`

### Database Management
\`\`\`bash
# Create database backup
cd /var/www/nirvana-backend
node scripts/backup-database.js

# Run database migrations
node scripts/run-migrations.js

# Seed test data
node scripts/seed-database.js

# Create admin user
node scripts/create-default-admin.js
\`\`\`

## 🧪 Testing Features

### Admin Panel Testing
- Access: https://test.shopnirvanaorganics.com/admin
- Default admin credentials will be created during setup
- Full admin functionality for testing

### Payment Testing
- Square Sandbox environment
- Test credit card numbers
- No real transactions processed

### Email Testing
- All emails sent to configured test email
- Email templates and functionality testing

## 🔒 Security Features

- **Testing Environment Headers**: Clear identification
- **Robots.txt**: Blocks search engine indexing
- **Relaxed Rate Limits**: Suitable for testing scenarios
- **Debug Logging**: Detailed logs for troubleshooting

## 📊 Monitoring

### Log Files
- **Application Logs**: \`/var/www/nirvana-backend/logs/\`
- **PM2 Logs**: \`pm2 logs nirvana-testing\`
- **Nginx Logs**: \`/var/log/nginx/nirvana-testing-*\`

### Health Checks
\`\`\`bash
# Check all services
cd /var/www/nirvana-backend
node scripts/system-status.js

# Test API health
curl https://test.shopnirvanaorganics.com/api/health
\`\`\`

## 🆘 Troubleshooting

### Common Issues

**Application won't start:**
\`\`\`bash
# Check PM2 logs
sudo pm2 logs nirvana-testing

# Verify environment file
cat /var/www/nirvana-backend/.env

# Test database connection
cd /var/www/nirvana-backend
node scripts/test-database-connection.js
\`\`\`

**Admin panel not accessible:**
\`\`\`bash
# Check if admin build exists
ls -la /var/www/nirvana-backend/dist/admin/

# Verify nginx configuration
sudo nginx -t

# Check admin routes in logs
sudo tail -f /var/log/nginx/nirvana-testing-access.log
\`\`\`

## 🔄 Updates

To update the testing environment:
1. Upload new deployment package
2. Stop the application: \`sudo pm2 stop nirvana-testing\`
3. Backup current installation
4. Replace application files
5. Run migrations if needed
6. Start the application: \`sudo pm2 start nirvana-testing\`

---

**Environment**: Testing/Staging
**Target Domain**: test.shopnirvanaorganics.com
**Last Updated**: ${new Date().toISOString().split('T')[0]}`;

  fs.writeFileSync(path.join(deploymentDir, 'README.md'), readmeContent);
  log.success('Created testing environment documentation');
}

// Run the deployment creation if this script is executed directly
if (require.main === module) {
  createTestingDeployment();
}

module.exports = { createTestingDeployment };
