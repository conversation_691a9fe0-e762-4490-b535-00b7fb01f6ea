# Testing Environment Configuration
# test.shopnirvanaorganics.com

# Application Configuration
NODE_ENV=testing
PORT=5001
HTTPS_PORT=5443
ADMIN_PORT=5002

# Domain Configuration
FRONTEND_URL=https://test.shopnirvanaorganics.com
BACKEND_URL=https://test.shopnirvanaorganics.com
CORS_ORIGIN=https://test.shopnirvanaorganics.com

# Database Configuration (Testing)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_testing
DB_USER=nirvana_test_user
DB_PASSWORD=your-secure-test-db-password
DB_SSL=false
DB_POOL_MAX=5
DB_POOL_MIN=2

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-testing-32-chars-minimum
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-for-testing-32-chars-minimum
SESSION_SECRET=your-super-secure-session-secret-for-testing-32-chars-minimum
BCRYPT_SALT_ROUNDS=12

# Email Configuration (Testing)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-test-email-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME="Nirvana Organics (Testing)"

# Specialized Email Addresses
EMAIL_ORDERS=<EMAIL>
EMAIL_SUPPORT=<EMAIL>
EMAIL_CUSTOMER_SERVICE=<EMAIL>

# Email SMTP Configuration for specialized addresses
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=your-orders-test-email-password
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=your-support-test-email-password

# Google OAuth Configuration (Testing)
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret

# Square Payment Integration (Sandbox)
SQUARE_ACCESS_TOKEN=your-sandbox-square-access-token
SQUARE_APPLICATION_ID=your-sandbox-square-application-id
SQUARE_ENVIRONMENT=sandbox
SQUARE_WEBHOOK_SIGNATURE_KEY=your-sandbox-webhook-signature-key
SQUARE_LOCATION_ID=your-sandbox-square-location-id

# Square OAuth Configuration (Testing)
SQUARE_OAUTH_CLIENT_ID=your-sandbox-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-sandbox-square-oauth-client-secret
SQUARE_OAUTH_CALLBACK_URL=https://test.shopnirvanaorganics.com/api/auth/square/callback

# File Upload Configuration
UPLOAD_PATH=/var/www/nirvana-test/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=debug
LOG_PATH=/var/www/nirvana-test/logs

# Redis Cache Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
CACHE_TTL=3600

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_EMAIL_SECONDARY=<EMAIL>
ADMIN_SECURITY_MODE=enhanced

# Security Configuration
FORCE_HTTPS=true
WEBHOOK_SECRET=your-webhook-secret-testing-32-chars-minimum
API_KEY=your-api-key-testing-24-chars-minimum

# USPS Shipping Integration (Testing)
USPS_USER_ID=your-usps-user-id-testing
USPS_API_URL=https://secure.shippingapis.com/ShippingAPI.dll

# WhatsApp Business API Integration (Testing)
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token-testing
WHATSAPP_PHONE_NUMBER_ID=your-whatsapp-phone-number-id-testing
WHATSAPP_BUSINESS_ACCOUNT_ID=your-whatsapp-business-account-id-testing
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-whatsapp-webhook-verify-token-testing

# VAPID Keys for Push Notifications
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_EMAIL=mailto:<EMAIL>

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/test.shopnirvanaorganics.com.crt
SSL_KEY_PATH=/etc/ssl/private/test.shopnirvanaorganics.com.key

# Backup Configuration
BACKUP_PATH=/var/www/nirvana-test/backups
BACKUP_RETENTION_DAYS=7

# Monitoring and Health Checks
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Security Headers
HELMET_ENABLED=true
TRUST_PROXY=true

# Frontend Environment Variables (VITE_ prefixed for Vite builds)
VITE_API_URL=https://test.shopnirvanaorganics.com/api
VITE_ADMIN_API_URL=/api
VITE_APP_NAME="Nirvana Organics (Testing)"
VITE_ENVIRONMENT=testing
VITE_GOOGLE_CLIENT_ID=your-test-google-client-id
VITE_FRONTEND_URL=https://test.shopnirvanaorganics.com
VITE_BACKEND_URL=https://test.shopnirvanaorganics.com
VITE_SQUARE_APPLICATION_ID=your-sandbox-square-application-id
VITE_SQUARE_ENVIRONMENT=sandbox
VITE_VAPID_PUBLIC_KEY=your-vapid-public-key
VITE_CORS_ORIGIN=https://test.shopnirvanaorganics.com

# Frontend Feature Flags (Testing Environment)
VITE_ENABLE_GOOGLE_OAUTH=true
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_SQUARE_PAYMENTS=true
VITE_ENABLE_ANALYTICS=true
VITE_DEBUG_MODE=false
VITE_ENABLE_CONSOLE_LOGS=false
VITE_ENABLE_REDUX_DEVTOOLS=false
VITE_ENABLE_DATA_ENVIRONMENT=true
VITE_ENABLE_MOCK_DATA=true

# Social Media Integration (Testing)
# Facebook
FACEBOOK_CLIENT_ID=your-facebook-test-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-test-app-secret
FACEBOOK_PAGE_ID=your-facebook-test-page-id

# Twitter/X
TWITTER_CLIENT_ID=your-twitter-test-client-id
TWITTER_CLIENT_SECRET=your-twitter-test-client-secret
TWITTER_BEARER_TOKEN=your-twitter-test-bearer-token

# Instagram
INSTAGRAM_CLIENT_ID=your-instagram-test-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-test-client-secret

# LinkedIn
LINKEDIN_CLIENT_ID=your-linkedin-test-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-test-client-secret

# YouTube
YOUTUBE_CLIENT_ID=your-youtube-test-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-test-client-secret
YOUTUBE_API_KEY=your-youtube-test-api-key

# Pinterest
PINTEREST_CLIENT_ID=your-pinterest-test-client-id
PINTEREST_CLIENT_SECRET=your-pinterest-test-client-secret

# TikTok
TIKTOK_CLIENT_ID=your-tiktok-test-client-id
TIKTOK_CLIENT_SECRET=your-tiktok-test-client-secret

# Social Media Configuration
SOCIAL_MEDIA_CALLBACK_URL=https://test.shopnirvanaorganics.com/api/admin/social-media/callback
SOCIAL_MEDIA_SCHEDULER_ENABLED=true
SOCIAL_MEDIA_ANALYTICS_ENABLED=true

# Frontend Contact Information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_ORDERS_EMAIL=<EMAIL>
VITE_CUSTOMER_SERVICE_EMAIL=<EMAIL>
