const { Client, Environment } = require('square');
require('dotenv').config();

// Initialize Square client
const squareClient = new Client({
  accessToken: process.env.SQUARE_ACCESS_TOKEN,
  environment: process.env.SQUARE_ENVIRONMENT === 'production' ? Environment.Production : Environment.Sandbox
});

const paymentsApi = squareClient.paymentsApi;
const ordersApi = squareClient.ordersApi;
const locationsApi = squareClient.locationsApi;
const invoicesApi = squareClient.invoicesApi;

class SquareService {
  // Get locations
  static async getLocations() {
    try {
      const response = await locationsApi.listLocations();

      if (response.result && response.result.locations) {
        return response.result.locations.map(location => ({
          id: location.id,
          name: location.name,
          address: location.address,
          status: location.status
        }));
      }

      return [];
    } catch (error) {
      console.error('Square locations error:', error);
      throw new Error(`Failed to get locations: ${error.message}`);
    }
  }
  // Create a payment
  static async createPayment(paymentData) {
    try {
      const { sourceId, amountMoney, orderId, buyerEmailAddress, billingAddress, shippingAddress } = paymentData;

      const request = {
        sourceId,
        idempotencyKey: `${orderId}-${Date.now()}`,
        amountMoney: {
          amount: Math.round(amountMoney.amount * 100), // Convert to cents
          currency: amountMoney.currency || 'USD'
        },
        orderId,
        buyerEmailAddress,
        billingAddress: billingAddress ? {
          addressLine1: billingAddress.address1,
          addressLine2: billingAddress.address2,
          locality: billingAddress.city,
          administrativeDistrictLevel1: billingAddress.state,
          postalCode: billingAddress.zipCode,
          country: billingAddress.country || 'US',
          firstName: billingAddress.firstName,
          lastName: billingAddress.lastName
        } : undefined,
        shippingAddress: shippingAddress ? {
          addressLine1: shippingAddress.address1,
          addressLine2: shippingAddress.address2,
          locality: shippingAddress.city,
          administrativeDistrictLevel1: shippingAddress.state,
          postalCode: shippingAddress.zipCode,
          country: shippingAddress.country || 'US',
          firstName: shippingAddress.firstName,
          lastName: shippingAddress.lastName
        } : undefined,
        autocomplete: true,
        locationId: process.env.SQUARE_LOCATION_ID,
        referenceId: orderId,
        note: `Payment for order ${orderId}`
      };

      const response = await paymentsApi.createPayment(request);
      
      if (response.result && response.result.payment) {
        return {
          success: true,
          payment: response.result.payment
        };
      } else {
        throw new Error('Payment creation failed');
      }
    } catch (error) {
      console.error('Square payment error:', error);
      return this.handlePaymentError(error);
    }
  }

  // Create a Square order
  static async createOrder(orderData) {
    try {
      const { items, locationId, referenceId } = orderData;

      const lineItems = items.map(item => ({
        name: item.name,
        quantity: item.quantity.toString(),
        basePriceMoney: {
          amount: Math.round(item.price * 100), // Convert to cents
          currency: 'USD'
        },
        variationName: item.variant ? `${item.variant.name}: ${item.variant.value}` : undefined
      }));

      const request = {
        order: {
          locationId: locationId || process.env.SQUARE_LOCATION_ID,
          referenceId,
          lineItems
        },
        idempotencyKey: `order-${referenceId}-${Date.now()}`
      };

      const response = await ordersApi.createOrder(request);
      
      if (response.result && response.result.order) {
        return {
          success: true,
          order: response.result.order
        };
      } else {
        throw new Error('Order creation failed');
      }
    } catch (error) {
      console.error('Square order creation error:', error);
      return {
        success: false,
        error: error.message || 'Order creation failed',
        details: error.errors || []
      };
    }
  }

  // Get payment details
  static async getPayment(paymentId) {
    try {
      const response = await paymentsApi.getPayment(paymentId);
      
      if (response.result && response.result.payment) {
        return {
          success: true,
          payment: response.result.payment
        };
      } else {
        throw new Error('Payment not found');
      }
    } catch (error) {
      console.error('Square get payment error:', error);
      return {
        success: false,
        error: error.message || 'Failed to retrieve payment',
        details: error.errors || []
      };
    }
  }

  // Refund a payment
  static async refundPayment(refundData) {
    try {
      const { paymentId, amountMoney, reason } = refundData;

      const request = {
        idempotencyKey: `refund-${paymentId}-${Date.now()}`,
        amountMoney: {
          amount: Math.round(amountMoney.amount * 100), // Convert to cents
          currency: amountMoney.currency || 'USD'
        },
        paymentId,
        reason: reason || 'Customer refund request'
      };

      const response = await squareClient.refunds.refundPayment(request);
      
      if (response.result && response.result.refund) {
        return {
          success: true,
          refund: response.result.refund
        };
      } else {
        throw new Error('Refund failed');
      }
    } catch (error) {
      console.error('Square refund error:', error);
      return {
        success: false,
        error: error.message || 'Refund processing failed',
        details: error.errors || []
      };
    }
  }

  // Verify webhook signature
  static verifyWebhookSignature(body, signature, signatureKey) {
    try {
      const crypto = require('crypto');
      const hmac = crypto.createHmac('sha256', signatureKey);
      hmac.update(body);
      const expectedSignature = hmac.digest('base64');
      
      return signature === expectedSignature;
    } catch (error) {
      console.error('Webhook signature verification error:', error);
      return false;
    }
  }

  // Process webhook event
  static async processWebhookEvent(event) {
    try {
      const { type, data } = event;

      switch (type) {
        case 'payment.updated':
          return await this.handlePaymentUpdated(data.object.payment);
        case 'payment.created':
          return await this.handlePaymentCreated(data.object.payment);
        case 'refund.updated':
          return await this.handleRefundUpdated(data.object.refund);
        default:
          console.log(`Unhandled webhook event type: ${type}`);
          return { success: true, message: 'Event type not handled' };
      }
    } catch (error) {
      console.error('Webhook processing error:', error);
      return {
        success: false,
        error: error.message || 'Webhook processing failed'
      };
    }
  }

  // Handle payment updated webhook
  static async handlePaymentUpdated(payment) {
    try {
      const { Order } = require('../models');

      if (payment.referenceId) {
        const order = await Order.findOne({ where: { orderNumber: payment.referenceId } });

        if (order) {
          let paymentStatus = 'pending';
          let orderStatus = order.status;

          switch (payment.status) {
            case 'COMPLETED':
              paymentStatus = 'paid';
              if (order.status === 'pending') {
                orderStatus = 'confirmed';
              }
              break;
            case 'FAILED':
            case 'CANCELED':
              paymentStatus = 'failed';
              orderStatus = 'cancelled';
              break;
            case 'PENDING':
              paymentStatus = 'pending';
              break;
            default:
              paymentStatus = 'pending';
          }

          // Update status history
          const statusHistory = order.statusHistory || [];
          statusHistory.push({
            status: orderStatus,
            paymentStatus,
            timestamp: new Date(),
            note: `Payment ${payment.status.toLowerCase()} - Square Payment ID: ${payment.id}`
          });

          await order.update({
            status: orderStatus,
            paymentStatus,
            squarePaymentId: payment.id,
            statusHistory
          });

          // Send notification email if payment completed
          if (payment.status === 'COMPLETED') {
            await this.sendPaymentConfirmationEmail(order);
          }

          return { success: true, message: 'Order payment status updated' };
        }
      }

      return { success: true, message: 'No matching order found' };
    } catch (error) {
      console.error('Payment updated handler error:', error);
      return { success: false, error: error.message };
    }
  }

  // Handle payment created webhook
  static async handlePaymentCreated(payment) {
    // Similar to handlePaymentUpdated but for new payments
    return await this.handlePaymentUpdated(payment);
  }

  // Handle refund updated webhook
  static async handleRefundUpdated(refund) {
    try {
      const { Order } = require('../models');
      
      if (refund.paymentId) {
        const order = await Order.findOne({ where: { squarePaymentId: refund.paymentId } });
        
        if (order && refund.status === 'COMPLETED') {
          await order.update({
            paymentStatus: 'refunded',
            status: 'refunded',
            refundAmount: refund.amountMoney.amount / 100 // Convert from cents
          });

          return { success: true, message: 'Order refund status updated' };
        }
      }

      return { success: true, message: 'No matching order found for refund' };
    } catch (error) {
      console.error('Refund updated handler error:', error);
      return { success: false, error: error.message };
    }
  }

  // Send payment confirmation email
  static async sendPaymentConfirmationEmail(order) {
    try {
      const nodemailer = require('nodemailer');

      const transporter = nodemailer.createTransporter({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: false,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });

      const email = order.guestInfo ? order.guestInfo.email : order.User?.email;
      const customerName = order.guestInfo ?
        `${order.guestInfo.firstName} ${order.guestInfo.lastName}` :
        `${order.User?.firstName} ${order.User?.lastName}`;

      if (!email) {
        console.warn('No email found for order confirmation:', order.orderNumber);
        return;
      }

      const itemsList = order.items.map(item =>
        `<li>${item.name} x ${item.quantity} - $${item.total.toFixed(2)}</li>`
      ).join('');

      await transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: email,
        subject: `Payment Confirmed - Order ${order.orderNumber}`,
        html: `
          <h1>Payment Confirmed!</h1>
          <p>Dear ${customerName},</p>
          <p>Your payment has been successfully processed for order ${order.orderNumber}.</p>

          <h3>Order Details:</h3>
          <ul>
            ${itemsList}
          </ul>

          <p><strong>Total: $${order.total.toFixed(2)}</strong></p>

          <p>Your order is now being processed and will be shipped soon.</p>

          <p>Thank you for choosing Nirvana Organics!</p>
        `
      });

      console.log('Payment confirmation email sent for order:', order.orderNumber);
    } catch (error) {
      console.error('Failed to send payment confirmation email:', error);
    }
  }

  // Enhanced payment error handling
  static handlePaymentError(error) {
    if (error.errors && error.errors.length > 0) {
      const squareError = error.errors[0];

      switch (squareError.code) {
        case 'CARD_DECLINED':
          return {
            success: false,
            error: 'Your card was declined. Please try a different payment method.',
            code: 'CARD_DECLINED'
          };
        case 'INSUFFICIENT_FUNDS':
          return {
            success: false,
            error: 'Insufficient funds. Please try a different payment method.',
            code: 'INSUFFICIENT_FUNDS'
          };
        case 'CVV_FAILURE':
          return {
            success: false,
            error: 'Invalid CVV. Please check your card details.',
            code: 'CVV_FAILURE'
          };
        case 'EXPIRATION_FAILURE':
          return {
            success: false,
            error: 'Your card has expired. Please use a different payment method.',
            code: 'EXPIRATION_FAILURE'
          };
        case 'INVALID_CARD':
          return {
            success: false,
            error: 'Invalid card information. Please check your details.',
            code: 'INVALID_CARD'
          };
        case 'GENERIC_DECLINE':
          return {
            success: false,
            error: 'Payment was declined. Please contact your bank or try a different payment method.',
            code: 'GENERIC_DECLINE'
          };
        default:
          return {
            success: false,
            error: squareError.detail || 'Payment processing failed. Please try again.',
            code: squareError.code
          };
      }
    }

    return {
      success: false,
      error: 'Payment processing failed. Please try again.',
      code: 'UNKNOWN_ERROR'
    };
  }

  // Get dashboard statistics from Square
  static async getDashboardStats() {
    try {
      // Get payments from Square (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const paymentsResponse = await paymentsApi.listPayments({
        beginTime: thirtyDaysAgo.toISOString(),
        endTime: new Date().toISOString(),
        sortOrder: 'DESC'
      });

      const payments = paymentsResponse.result.payments || [];

      const squareRevenue = payments
        .filter(payment => payment.status === 'COMPLETED')
        .reduce((total, payment) => {
          return total + (payment.amountMoney?.amount || 0);
        }, 0) / 100; // Convert from cents to dollars

      const squareOrders = payments.filter(payment => payment.status === 'COMPLETED').length;

      return {
        squareRevenue,
        squareOrders
      };

    } catch (error) {
      console.error('Square dashboard stats error:', error);
      return {
        squareRevenue: 0,
        squareOrders: 0
      };
    }
  }

  // Get inventory count for a product
  static async getInventoryCount(itemId) {
    try {
      if (!itemId) return null;

      const inventoryResponse = await squareClient.inventory.retrieveInventoryCount({
        catalogObjectId: itemId
      });

      return inventoryResponse.result.counts?.[0]?.quantity || 0;

    } catch (error) {
      console.error('Square inventory count error:', error);
      return null;
    }
  }

  /**
   * Create Square invoice for order
   */
  static async createInvoice(orderData) {
    try {
      const invoiceRequest = {
        requestMethod: 'CREATE',
        invoice: {
          locationId: process.env.SQUARE_LOCATION_ID,
          orderRequest: {
            order: {
              locationId: process.env.SQUARE_LOCATION_ID,
              referenceId: orderData.orderNumber,
              lineItems: orderData.items.map(item => ({
                name: item.productName,
                quantity: item.quantity.toString(),
                basePriceMoney: {
                  amount: Math.round(parseFloat(item.price) * 100), // Convert to cents
                  currency: 'USD'
                }
              })),
              taxes: [{
                name: 'Sales Tax',
                percentage: '8.25', // Adjust based on location
                scope: 'ORDER'
              }]
            }
          },
          primaryRecipient: {
            customerId: orderData.customer.squareCustomerId || null
          },
          paymentRequests: [{
            requestMethod: 'EMAIL',
            requestType: 'BALANCE',
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
          }],
          deliveryMethod: 'EMAIL',
          invoiceNumber: `INV-${orderData.orderNumber}`,
          title: 'Nirvana Organics Invoice',
          description: `Invoice for order ${orderData.orderNumber}`,
          scheduledAt: new Date().toISOString(),
          acceptedPaymentMethods: {
            card: true,
            squareGiftCard: false,
            bankAccount: false,
            buyNowPayLater: false
          }
        }
      };

      const response = await invoicesApi.createInvoice(invoiceRequest);

      if (response.result && response.result.invoice) {
        const invoice = response.result.invoice;

        // Send the invoice
        await this.sendInvoice(invoice.id);

        return {
          success: true,
          invoice: invoice,
          invoiceId: invoice.id,
          invoiceUrl: invoice.publicUrl
        };
      } else {
        throw new Error('Invoice creation failed');
      }

    } catch (error) {
      console.error('Square invoice creation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send Square invoice via email
   */
  static async sendInvoice(invoiceId) {
    try {
      const response = await invoicesApi.sendInvoice(invoiceId, {
        requestMethod: 'EMAIL'
      });

      return {
        success: true,
        result: response.result
      };

    } catch (error) {
      console.error('Square invoice send error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get invoice details
   */
  static async getInvoice(invoiceId) {
    try {
      const response = await invoicesApi.getInvoice(invoiceId);

      return {
        success: true,
        invoice: response.result.invoice
      };

    } catch (error) {
      console.error('Square get invoice error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = SquareService;
