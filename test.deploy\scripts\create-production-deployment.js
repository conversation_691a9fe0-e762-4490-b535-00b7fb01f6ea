#!/usr/bin/env node

/**
 * Production Deployment Creation Script
 * Creates a complete deployment package for VPS deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
};

async function createProductionDeployment() {
  log.header('🚀 Creating Production Deployment Package');

  const deploymentDir = path.join(process.cwd(), 'deployment');
  
  try {
    // Create deployment directory
    if (fs.existsSync(deploymentDir)) {
      log.info('Cleaning existing deployment directory...');
      fs.rmSync(deploymentDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(deploymentDir, { recursive: true });
    log.success('Created deployment directory');

    // Create subdirectories
    const subdirs = [
      'server',
      'frontend',
      'admin',
      'scripts',
      'config',
      'nginx',
      'systemd'
    ];

    subdirs.forEach(dir => {
      fs.mkdirSync(path.join(deploymentDir, dir), { recursive: true });
    });

    // Copy server files
    log.header('📦 Copying Server Files');
    const serverFiles = [
      'server',
      'package.json',
      'server-package.json',
      'ecosystem.config.js'
    ];

    serverFiles.forEach(file => {
      const srcPath = path.join(process.cwd(), file);
      const destPath = path.join(deploymentDir, file);
      
      if (fs.existsSync(srcPath)) {
        if (fs.statSync(srcPath).isDirectory()) {
          copyDirectory(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
        log.success(`Copied ${file}`);
      }
    });

    // Copy unified build (contains both frontend and admin)
    log.header('🌐 Copying Unified Build (Frontend + Admin)');
    if (fs.existsSync('dist')) {
      copyDirectory('dist', path.join(deploymentDir, 'frontend'));
      log.success('Copied unified build (main app at root, admin at /admin)');

      // Verify admin panel exists in unified build
      const adminPath = path.join(deploymentDir, 'frontend', 'admin');
      if (fs.existsSync(adminPath)) {
        log.success('Admin panel found in unified build at /admin');
      } else {
        log.warning('Admin panel not found in unified build - may need to run "npm run build:unified"');
      }
    } else {
      log.error('Unified build not found. Run "npm run build:unified" first');
      log.info('The unified build contains both the main frontend and admin panel in a single structure');
    }

    // Copy deployment scripts
    log.header('📜 Creating Deployment Scripts');
    const deploymentScripts = [
      'setup-database.js',
      'create-database-tables.js',
      'seed-database.js',
      'system-status.js'
    ];

    deploymentScripts.forEach(script => {
      const srcPath = path.join(process.cwd(), 'scripts', script);
      const destPath = path.join(deploymentDir, 'scripts', script);
      
      if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, destPath);
        log.success(`Copied ${script}`);
      }
    });

    // Create production environment template
    createProductionEnvTemplate(deploymentDir);
    
    // Create Nginx configuration
    createNginxConfig(deploymentDir);
    
    // Create systemd service files
    createSystemdServices(deploymentDir);
    
    // Create deployment script
    createDeploymentScript(deploymentDir);
    
    // Create README
    createDeploymentReadme(deploymentDir);

    log.header('📊 Deployment Package Summary');
    log.success(`Deployment package created at: ${deploymentDir}`);
    log.info('Package contents:');
    log.info('  - Server application and dependencies');
    log.info('  - Built frontend application');
    log.info('  - Built admin panel');
    log.info('  - Nginx configuration');
    log.info('  - Systemd service files');
    log.info('  - Database setup scripts');
    log.info('  - Production environment template');
    log.info('  - Deployment instructions');

    log.header('🎯 Next Steps');
    log.info('1. Update the production environment file with your actual values');
    log.info('2. Transfer the deployment folder to your VPS server');
    log.info('3. Run the deployment script on your VPS');
    log.info('4. Configure your domain and SSL certificates');

  } catch (error) {
    log.error(`Deployment package creation failed: ${error.message}`);
    process.exit(1);
  }
}

function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function createProductionEnvTemplate(deploymentDir) {
  const envTemplate = `# Production Environment Configuration
# Update all values with your actual production settings

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_production
DB_USER=nirvana_user
DB_PASSWORD=CHANGE_THIS_TO_SECURE_PASSWORD

# JWT Configuration
JWT_SECRET=CHANGE_THIS_TO_SECURE_JWT_SECRET_PRODUCTION
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=CHANGE_THIS_TO_SECURE_REFRESH_SECRET_PRODUCTION
JWT_REFRESH_EXPIRES_IN=30d

# Server Configuration
PORT=5000
NODE_ENV=production
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com

# Frontend URLs
FRONTEND_URL=https://yourdomain.com
ADMIN_FRONTEND_URL=https://admin.yourdomain.com

# Email Configuration (Production Gmail)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-app-password
EMAIL_FROM=<EMAIL>

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
GOOGLE_REDIRECT_URI=https://yourdomain.com/api/auth/google/callback

# Payment Configuration
STRIPE_SECRET_KEY=sk_live_your_live_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret

SQUARE_APPLICATION_ID=your_production_square_app_id
SQUARE_ACCESS_TOKEN=your_production_square_access_token
SQUARE_ENVIRONMENT=production

# File Upload Configuration
UPLOAD_PATH=/var/www/nirvana-backend/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Security Configuration
SESSION_SECRET=CHANGE_THIS_TO_SECURE_SESSION_SECRET_PRODUCTION
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=CHANGE_THIS_TO_SECURE_ADMIN_PASSWORD
ADMIN_SECURITY_MODE=true

# Logging Configuration
LOG_LEVEL=warn
LOG_FILE=/var/www/nirvana-backend/logs/app.log

# Real-time Configuration
SOCKET_IO_CORS_ORIGIN=https://yourdomain.com,https://admin.yourdomain.com

# Cache Configuration
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Backup Configuration
BACKUP_PATH=/var/www/nirvana-backend/backups
BACKUP_RETENTION_DAYS=30

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/yourdomain.com.crt
SSL_KEY_PATH=/etc/ssl/private/yourdomain.com.key`;

  fs.writeFileSync(path.join(deploymentDir, 'config', '.env.production'), envTemplate);
  log.success('Created production environment template');
}

function createNginxConfig(deploymentDir) {
  const nginxConfig = `# Nirvana Organics E-commerce Nginx Configuration
# Replace yourdomain.com with your actual domain

# Main website
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Root directory for frontend
    root /var/www/nirvana-backend/frontend;
    index index.html;

    # API routes - proxy to Node.js backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support for real-time features
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files
    location /uploads/ {
        alias /var/www/nirvana-backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Frontend routes - serve index.html for SPA
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}

# Admin panel
server {
    listen 80;
    listen [::]:80;
    server_name admin.yourdomain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name admin.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Root directory for admin panel
    root /var/www/nirvana-backend/admin;
    index admin.html;

    # API routes - proxy to Node.js backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Admin panel routes - serve admin.html for SPA
    location / {
        try_files $uri $uri/ /admin.html;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}`;

  fs.writeFileSync(path.join(deploymentDir, 'nginx', 'nirvana-organics.conf'), nginxConfig);
  log.success('Created Nginx configuration');
}

function createSystemdServices(deploymentDir) {
  const systemdService = `[Unit]
Description=Nirvana Organics E-commerce Backend
Documentation=https://github.com/your-username/nirvana-organics-backend
After=network.target

[Service]
Type=simple
User=nirvana
WorkingDirectory=/var/www/nirvana-backend
Environment=NODE_ENV=production
ExecStart=/usr/bin/node server/index.js
Restart=on-failure
RestartSec=10
KillMode=mixed
KillSignal=SIGINT
TimeoutStopSec=5
SyslogIdentifier=nirvana-backend
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/var/www/nirvana-backend/uploads /var/www/nirvana-backend/logs

[Install]
WantedBy=multi-user.target`;

  fs.writeFileSync(path.join(deploymentDir, 'systemd', 'nirvana-backend.service'), systemdService);
  log.success('Created systemd service file');
}

function createDeploymentScript(deploymentDir) {
  const deployScript = `#!/bin/bash

# Nirvana Organics E-commerce Deployment Script
# Run this script on your VPS server as root

set -e

echo "🚀 Starting Nirvana Organics E-commerce Deployment"

# Variables
APP_NAME="nirvana-backend"
APP_USER="nirvana"
APP_DIR="/var/www/$APP_NAME"
NGINX_CONF="/etc/nginx/sites-available/nirvana-organics"
SYSTEMD_SERVICE="/etc/systemd/system/nirvana-backend.service"

# Create application user
if ! id "$APP_USER" &>/dev/null; then
    echo "Creating application user: $APP_USER"
    useradd -r -s /bin/false -d $APP_DIR $APP_USER
fi

# Create application directory
echo "Creating application directory: $APP_DIR"
mkdir -p $APP_DIR
mkdir -p $APP_DIR/logs
mkdir -p $APP_DIR/uploads
mkdir -p $APP_DIR/backups

# Copy application files
echo "Copying application files..."
cp -r server/ $APP_DIR/
cp -r frontend/ $APP_DIR/
cp -r admin/ $APP_DIR/
cp -r scripts/ $APP_DIR/
cp package.json $APP_DIR/
cp server-package.json $APP_DIR/
cp ecosystem.config.js $APP_DIR/
cp config/.env.production $APP_DIR/.env

# Set permissions
chown -R $APP_USER:$APP_USER $APP_DIR
chmod -R 755 $APP_DIR
chmod 600 $APP_DIR/.env

# Install Node.js dependencies
echo "Installing Node.js dependencies..."
cd $APP_DIR
sudo -u $APP_USER npm ci --production

# Install Nginx configuration
echo "Installing Nginx configuration..."
cp nginx/nirvana-organics.conf $NGINX_CONF
ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# Install systemd service
echo "Installing systemd service..."
cp systemd/nirvana-backend.service $SYSTEMD_SERVICE
systemctl daemon-reload
systemctl enable nirvana-backend

# Setup database (if needed)
echo "Setting up database..."
sudo -u $APP_USER node scripts/setup-database.js

# Start the application
echo "Starting the application..."
systemctl start nirvana-backend

echo "✅ Deployment completed successfully!"
echo "🌐 Your application should now be accessible at your domain"
echo "📊 Check status with: systemctl status nirvana-backend"
echo "📝 View logs with: journalctl -u nirvana-backend -f"`;

  fs.writeFileSync(path.join(deploymentDir, 'deploy.sh'), deployScript);
  fs.chmodSync(path.join(deploymentDir, 'deploy.sh'), '755');
  log.success('Created deployment script');
}

function createDeploymentReadme(deploymentDir) {
  const readme = `# Nirvana Organics E-commerce - Production Deployment

This package contains everything needed to deploy the Nirvana Organics E-commerce platform to your VPS server.

## Prerequisites

Before deploying, ensure your VPS server has:

- Ubuntu 20.04+ or CentOS 8+
- Node.js 18+ installed
- Nginx installed
- MySQL/MariaDB installed
- SSL certificates for your domain

## Deployment Steps

### 1. Prepare Environment Configuration

1. Edit \`config/.env.production\` with your actual production values:
   - Database credentials
   - JWT secrets (generate secure random strings)
   - Email configuration
   - Domain names
   - Payment gateway credentials
   - SSL certificate paths

### 2. Transfer Files to Server

Upload this entire deployment folder to your VPS server:

\`\`\`bash
scp -r deployment/ root@your-server-ip:/tmp/
\`\`\`

### 3. Run Deployment Script

On your VPS server, run the deployment script as root:

\`\`\`bash
cd /tmp/deployment
chmod +x deploy.sh
./deploy.sh
\`\`\`

### 4. Configure SSL Certificates

Update the Nginx configuration with your actual SSL certificate paths and restart Nginx:

\`\`\`bash
systemctl restart nginx
\`\`\`

### 5. Verify Deployment

Check that all services are running:

\`\`\`bash
# Check application status
systemctl status nirvana-backend

# Check Nginx status
systemctl status nginx

# View application logs
journalctl -u nirvana-backend -f
\`\`\`

## Post-Deployment

### Database Setup

The deployment script automatically sets up the database, but you may need to:

1. Create an admin user:
   \`\`\`bash
   cd /var/www/nirvana-backend
   sudo -u nirvana node scripts/create-admin-user.js
   \`\`\`

2. Seed initial data (optional):
   \`\`\`bash
   sudo -u nirvana node scripts/seed-database.js
   \`\`\`

### Monitoring

- Application logs: \`journalctl -u nirvana-backend -f\`
- Nginx logs: \`tail -f /var/log/nginx/access.log\`
- Error logs: \`tail -f /var/log/nginx/error.log\`

### Backup

Set up regular backups:

\`\`\`bash
# Database backup
sudo -u nirvana node scripts/backup-database.js

# File backup
tar -czf /var/backups/nirvana-files-$(date +%Y%m%d).tar.gz /var/www/nirvana-backend/uploads
\`\`\`

## Troubleshooting

### Common Issues

1. **Application won't start**: Check logs with \`journalctl -u nirvana-backend\`
2. **Database connection failed**: Verify database credentials in \`.env\`
3. **Nginx 502 error**: Ensure the Node.js application is running on port 5000
4. **SSL issues**: Verify certificate paths and permissions

### Support

For support, check the application logs and ensure all environment variables are correctly configured.

## Security Checklist

- [ ] Changed all default passwords
- [ ] Generated secure JWT secrets
- [ ] Configured proper SSL certificates
- [ ] Set up firewall rules
- [ ] Configured regular backups
- [ ] Updated all environment variables
- [ ] Verified file permissions

## File Structure

\`\`\`
/var/www/nirvana-backend/
├── server/           # Backend application
├── frontend/         # Built frontend files
├── admin/           # Built admin panel
├── uploads/         # User uploaded files
├── logs/           # Application logs
├── backups/        # Database backups
├── scripts/        # Utility scripts
├── .env           # Environment configuration
└── package.json   # Dependencies
\`\`\`

Your application should now be accessible at:
- Main site: https://yourdomain.com
- Admin panel: https://admin.yourdomain.com
`;

  fs.writeFileSync(path.join(deploymentDir, 'README.md'), readme);
  log.success('Created deployment README');
}

// Run the deployment creation
createProductionDeployment().catch(error => {
  console.error('Deployment creation failed:', error.message);
  process.exit(1);
});
