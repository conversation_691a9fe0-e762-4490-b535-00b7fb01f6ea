#!/bin/bash

# Dependencies Installation Script for Nirvana Organics E-commerce
# This script installs all required dependencies for the application

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${BLUE}======================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}======================================${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

log_header "📦 Installing Application Dependencies"

# Install Node.js 18.x LTS
log_info "Installing Node.js 18.x LTS..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs
log_success "Node.js $(node --version) installed"
log_success "npm $(npm --version) installed"

# Install MySQL Server
log_info "Installing MySQL Server..."
apt install -y mysql-server mysql-client
systemctl start mysql
systemctl enable mysql

# Secure MySQL installation
log_info "Securing MySQL installation..."
mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'temp_root_password';"
mysql -u root -ptemp_root_password -e "DELETE FROM mysql.user WHERE User='';"
mysql -u root -ptemp_root_password -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');"
mysql -u root -ptemp_root_password -e "DROP DATABASE IF EXISTS test;"
mysql -u root -ptemp_root_password -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';"
mysql -u root -ptemp_root_password -e "FLUSH PRIVILEGES;"

# Create application database and user
log_info "Creating application database and user..."
mysql -u root -ptemp_root_password -e "CREATE DATABASE IF NOT EXISTS nirvana_organics_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -ptemp_root_password -e "CREATE USER IF NOT EXISTS 'nirvana_user'@'localhost' IDENTIFIED BY 'secure_db_password_change_this';"
mysql -u root -ptemp_root_password -e "GRANT ALL PRIVILEGES ON nirvana_organics_production.* TO 'nirvana_user'@'localhost';"
mysql -u root -ptemp_root_password -e "FLUSH PRIVILEGES;"

# Reset root password to empty for local development (you should change this in production)
mysql -u root -ptemp_root_password -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '';"

log_success "MySQL Server installed and configured"

# Install Nginx
log_info "Installing Nginx..."
apt install -y nginx
systemctl start nginx
systemctl enable nginx

# Remove default Nginx site
rm -f /etc/nginx/sites-enabled/default
log_success "Nginx installed and configured"

# Install Redis (for caching and sessions)
log_info "Installing Redis..."
apt install -y redis-server
systemctl start redis-server
systemctl enable redis-server

# Configure Redis for production
sed -i 's/^# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf
systemctl restart redis-server
log_success "Redis installed and configured"

# Install PM2 globally (alternative process manager)
log_info "Installing PM2 process manager..."
npm install -g pm2
log_success "PM2 installed globally"

# Install Certbot for SSL certificates
log_info "Installing Certbot for SSL certificates..."
apt install -y certbot python3-certbot-nginx
log_success "Certbot installed"

# Install ImageMagick for image processing
log_info "Installing ImageMagick for image processing..."
apt install -y imagemagick libmagickwand-dev
log_success "ImageMagick installed"

# Install additional utilities
log_info "Installing additional utilities..."
apt install -y zip unzip curl wget git nano vim htop
log_success "Additional utilities installed"

# Configure MySQL for better performance
log_info "Optimizing MySQL configuration..."
cat >> /etc/mysql/mysql.conf.d/mysqld.cnf << EOF

# Nirvana Organics optimizations
max_connections = 200
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 32M
tmp_table_size = 32M
max_heap_table_size = 32M
EOF

systemctl restart mysql
log_success "MySQL configuration optimized"

# Configure Nginx for better performance
log_info "Optimizing Nginx configuration..."
cat > /etc/nginx/nginx.conf << EOF
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=login:10m rate=1r/s;
    
    # Logging
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF

nginx -t && systemctl reload nginx
log_success "Nginx configuration optimized"

# Install Node.js production dependencies globally
log_info "Installing global Node.js production tools..."
npm install -g nodemon forever
log_success "Global Node.js tools installed"

# Create log directories
log_info "Creating log directories..."
mkdir -p /var/log/nirvana-organics
mkdir -p /var/log/nginx
chown -R www-data:www-data /var/log/nginx
log_success "Log directories created"

# Set up log rotation for application logs
cat > /etc/logrotate.d/nirvana-organics << EOF
/var/log/nirvana-organics/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nirvana nirvana
}
EOF

log_header "✅ Dependencies Installation Complete"
log_success "All required dependencies have been installed successfully"
log_info "Installed components:"
log_info "  • Node.js $(node --version)"
log_info "  • npm $(npm --version)"
log_info "  • MySQL Server"
log_info "  • Nginx"
log_info "  • Redis"
log_info "  • PM2"
log_info "  • Certbot"
log_info "  • ImageMagick"
log_info ""
log_warning "Important: Please update the database passwords in your .env file"
log_warning "Default database user: nirvana_user"
log_warning "Default database password: secure_db_password_change_this"
log_info ""
log_info "Next: Configure the application environment"
