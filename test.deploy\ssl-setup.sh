#!/bin/bash

# SSL Certificate Setup Script for Testing Environment
# test.shopnirvanaorganics.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="test.shopnirvanaorganics.com"
EMAIL="<EMAIL>"
WEBROOT="/var/www/nirvana-test/dist"
SSL_DIR="/etc/ssl"
CERT_PATH="$SSL_DIR/certs"
KEY_PATH="$SSL_DIR/private"

echo -e "${BLUE}🔒 SSL Certificate Setup for $DOMAIN${NC}"
echo "=================================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   exit 1
fi

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Install Certbot if not already installed
install_certbot() {
    log_info "Installing Certbot..."
    
    if command -v certbot &> /dev/null; then
        log_info "Certbot is already installed"
        return
    fi
    
    # Update package list
    apt update
    
    # Install snapd if not installed
    if ! command -v snap &> /dev/null; then
        apt install -y snapd
        systemctl enable snapd
        systemctl start snapd
    fi
    
    # Install certbot via snap
    snap install core; snap refresh core
    snap install --classic certbot
    
    # Create symlink
    ln -sf /snap/bin/certbot /usr/bin/certbot
    
    log_info "Certbot installed successfully"
}

# Create SSL directories
create_ssl_directories() {
    log_info "Creating SSL directories..."
    
    mkdir -p "$CERT_PATH"
    mkdir -p "$KEY_PATH"
    
    # Set proper permissions
    chmod 755 "$CERT_PATH"
    chmod 700 "$KEY_PATH"
    
    log_info "SSL directories created"
}

# Create webroot directory
create_webroot() {
    log_info "Creating webroot directory..."
    
    mkdir -p "$WEBROOT"
    chown -R www-data:www-data "$WEBROOT"
    
    log_info "Webroot directory created: $WEBROOT"
}

# Obtain SSL certificate
obtain_certificate() {
    log_info "Obtaining SSL certificate for $DOMAIN..."
    
    # Stop nginx temporarily
    systemctl stop nginx || true
    
    # Obtain certificate using standalone mode
    certbot certonly \
        --standalone \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        --domains "$DOMAIN" \
        --non-interactive
    
    if [ $? -eq 0 ]; then
        log_info "SSL certificate obtained successfully"
    else
        log_error "Failed to obtain SSL certificate"
        exit 1
    fi
}

# Copy certificates to custom location
copy_certificates() {
    log_info "Copying certificates to custom location..."
    
    # Copy certificate files
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$CERT_PATH/$DOMAIN.crt"
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$KEY_PATH/$DOMAIN.key"
    
    # Set proper permissions
    chmod 644 "$CERT_PATH/$DOMAIN.crt"
    chmod 600 "$KEY_PATH/$DOMAIN.key"
    chown root:root "$CERT_PATH/$DOMAIN.crt"
    chown root:root "$KEY_PATH/$DOMAIN.key"
    
    log_info "Certificates copied to custom location"
}

# Setup auto-renewal
setup_auto_renewal() {
    log_info "Setting up automatic certificate renewal..."
    
    # Create renewal script
    cat > /usr/local/bin/renew-ssl-test.sh << 'EOF'
#!/bin/bash

# Renew certificates
certbot renew --quiet

# Copy renewed certificates
if [ -f "/etc/letsencrypt/live/test.shopnirvanaorganics.com/fullchain.pem" ]; then
    cp "/etc/letsencrypt/live/test.shopnirvanaorganics.com/fullchain.pem" "/etc/ssl/certs/test.shopnirvanaorganics.com.crt"
    cp "/etc/letsencrypt/live/test.shopnirvanaorganics.com/privkey.pem" "/etc/ssl/private/test.shopnirvanaorganics.com.key"
    
    # Set permissions
    chmod 644 "/etc/ssl/certs/test.shopnirvanaorganics.com.crt"
    chmod 600 "/etc/ssl/private/test.shopnirvanaorganics.com.key"
    
    # Reload nginx
    systemctl reload nginx
    
    echo "SSL certificates renewed and nginx reloaded"
fi
EOF

    chmod +x /usr/local/bin/renew-ssl-test.sh
    
    # Add cron job for automatic renewal
    (crontab -l 2>/dev/null; echo "0 3 * * * /usr/local/bin/renew-ssl-test.sh >> /var/log/ssl-renewal.log 2>&1") | crontab -
    
    log_info "Auto-renewal setup completed"
}

# Verify certificate
verify_certificate() {
    log_info "Verifying SSL certificate..."
    
    if [ -f "$CERT_PATH/$DOMAIN.crt" ] && [ -f "$KEY_PATH/$DOMAIN.key" ]; then
        # Check certificate validity
        openssl x509 -in "$CERT_PATH/$DOMAIN.crt" -text -noout | grep -E "(Subject:|Issuer:|Not After)"
        
        log_info "SSL certificate verification completed"
    else
        log_error "SSL certificate files not found"
        exit 1
    fi
}

# Main execution
main() {
    log_info "Starting SSL setup for testing environment..."
    
    install_certbot
    create_ssl_directories
    create_webroot
    obtain_certificate
    copy_certificates
    setup_auto_renewal
    verify_certificate
    
    echo ""
    echo -e "${GREEN}✅ SSL Certificate Setup Completed!${NC}"
    echo "=================================================="
    echo -e "Domain: ${BLUE}$DOMAIN${NC}"
    echo -e "Certificate: ${BLUE}$CERT_PATH/$DOMAIN.crt${NC}"
    echo -e "Private Key: ${BLUE}$KEY_PATH/$DOMAIN.key${NC}"
    echo -e "Auto-renewal: ${GREEN}Enabled${NC} (runs daily at 3 AM)"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Update your Nginx configuration to use the SSL certificates"
    echo "2. Start/reload Nginx: systemctl reload nginx"
    echo "3. Test HTTPS access: https://$DOMAIN"
    echo ""
}

# Run main function
main "$@"
