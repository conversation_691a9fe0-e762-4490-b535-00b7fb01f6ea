#!/bin/bash

# Nirvana Organics E-commerce - Production Environment Deployment Script
# Target Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${BLUE}======================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}======================================${NC}"
}

# Configuration
APP_NAME="nirvana-organics-production"
APP_USER="nirvana"
APP_DIR="/var/www/nirvana-backend"
NGINX_CONF="/etc/nginx/sites-available/nirvana-production"
SYSTEMD_SERVICE="/etc/systemd/system/nirvana-production.service"

if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

log_header "🚀 Starting Nirvana Organics Production Environment Deployment"

# System preparation
log_header "📋 System Preparation"
apt update
apt install -y curl wget gnupg2 software-properties-common ufw fail2ban

# Install Node.js
log_info "Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# Install Nginx
log_info "Installing Nginx..."
apt install -y nginx

# Install MySQL
log_info "Installing MySQL..."
apt install -y mysql-server

# Install PM2
log_info "Installing PM2..."
npm install -g pm2

# Install Redis (for caching)
log_info "Installing Redis..."
apt install -y redis-server

# Configure firewall
log_header "🔒 Configuring Firewall"
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 'Nginx Full'
ufw --force enable

# Create application user
log_header "👤 Setting Up Application User"
if ! id "$APP_USER" &>/dev/null; then
    log_info "Creating application user: $APP_USER"
    useradd -m -s /bin/bash $APP_USER
    usermod -aG sudo $APP_USER
    log_success "Created application user: $APP_USER"
else
    log_info "Application user already exists: $APP_USER"
fi

# Create directory structure
log_header "📁 Creating Directory Structure"
mkdir -p $APP_DIR/{logs,uploads,backups,config}
mkdir -p $APP_DIR/uploads/{products,categories,banners,documents}

# Copy application files
log_header "📋 Copying Application Files"
cp -r server/ $APP_DIR/
cp -r dist/ $APP_DIR/
cp -r scripts/ $APP_DIR/
cp -r config/ $APP_DIR/
cp ecosystem.config.production.js $APP_DIR/ecosystem.config.js

# Set permissions
log_header "🔒 Setting File Permissions"
chown -R $APP_USER:$APP_USER $APP_DIR
chmod -R 755 $APP_DIR
chmod 700 $APP_DIR/config

# Install dependencies
log_header "📦 Installing Dependencies"
cd $APP_DIR
sudo -u $APP_USER npm ci --production --silent

# Configure environment
log_header "⚙️ Environment Configuration"
if [ ! -f "$APP_DIR/.env" ]; then
    cp config/.env.production.template $APP_DIR/.env
    chown $APP_USER:$APP_USER $APP_DIR/.env
    chmod 600 $APP_DIR/.env
    log_warning "IMPORTANT: Please edit $APP_DIR/.env with your actual production values"
    log_warning "This includes database credentials, JWT secrets, and API keys"
    read -p "Press Enter after you have configured the .env file..."
fi

# Configure Nginx
log_header "🌐 Configuring Nginx"
cp nginx/nirvana-production.conf $NGINX_CONF
ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
systemctl enable nginx

# Setup database
log_header "🗄️ Database Setup"
sudo -u $APP_USER node scripts/setup-database.js

# Start application with PM2
log_header "🚀 Starting Application"
sudo -u $APP_USER pm2 start ecosystem.config.js
sudo -u $APP_USER pm2 save
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $APP_USER --hp /home/<USER>

log_header "🎉 Production Environment Deployment Complete!"
log_success "Nirvana Organics Production Environment deployed successfully!"
log_info "Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com"
log_info "Environment: Production"
log_info "Application Directory: $APP_DIR"
log_warning "Next Steps:"
log_warning "1. Configure SSL certificates with Let's Encrypt"
log_warning "2. Update DNS records to point to this server"
log_warning "3. Test all functionality thoroughly"
log_warning "4. Set up monitoring and backups"