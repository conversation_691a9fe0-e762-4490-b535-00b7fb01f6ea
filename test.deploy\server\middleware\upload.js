const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure upload paths for Hostinger hosting
const getUploadPath = () => {
  // For Hostinger, uploads should be in public_html/uploads or similar
  if (process.env.NODE_ENV === 'production') {
    return process.env.UPLOAD_PATH || path.join(process.cwd(), 'public/uploads');
  }
  return path.join(__dirname, '../../public/uploads');
};

// Ensure upload directories exist
const uploadDir = getUploadPath();
const productImagesDir = path.join(uploadDir, 'products');
const categoryImagesDir = path.join(uploadDir, 'categories');
const bannerImagesDir = path.join(uploadDir, 'banners');
const documentsDir = path.join(uploadDir, 'documents');

// Create all necessary upload directories
const createUploadDirectories = () => {
  const directories = [uploadDir, productImagesDir, categoryImagesDir, bannerImagesDir, documentsDir];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
      console.log(`Created upload directory: ${dir}`);
    }
  });
};

// Initialize directories
createUploadDirectories();

// Configure storage for product images
const productImageStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, productImagesDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `product-${uniqueSuffix}${ext}`);
  }
});

// File filter for images
const imageFileFilter = (req, file, cb) => {
  // Check file type
  if (file.mimetype.startsWith('image/')) {
    // Get allowed image types from environment or use defaults
    const allowedTypesString = process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,webp,gif';
    const allowedExtensions = allowedTypesString.split(',').map(ext => ext.trim());
    const allowedTypes = allowedExtensions.map(ext => {
      switch(ext.toLowerCase()) {
        case 'jpg': case 'jpeg': return 'image/jpeg';
        case 'png': return 'image/png';
        case 'webp': return 'image/webp';
        case 'gif': return 'image/gif';
        default: return `image/${ext}`;
      }
    });

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type. Only ${allowedExtensions.join(', ').toUpperCase()} images are allowed.`), false);
    }
  } else {
    cb(new Error('Only image files are allowed.'), false);
  }
};

// Configure multer for product images
const uploadProductImages = multer({
  storage: productImageStorage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || (5 * 1024 * 1024), // Use env var or default to 5MB
    files: 10 // Maximum 10 files per upload
  }
});

// Middleware for single product image upload
const uploadSingleProductImage = uploadProductImages.single('image');

// Middleware for multiple product images upload
const uploadMultipleProductImages = uploadProductImages.array('images', 10);

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      const maxSizeMB = Math.round((parseInt(process.env.MAX_FILE_SIZE) || (5 * 1024 * 1024)) / (1024 * 1024));
      return res.status(400).json({
        success: false,
        message: `File too large. Maximum size is ${maxSizeMB}MB per file.`
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum 10 files allowed.'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Unexpected field name for file upload.'
      });
    }
  }
  
  if (error.message.includes('Invalid file type') || error.message.includes('Only image files')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  next(error);
};

// Helper function to delete uploaded files
const deleteUploadedFiles = (files) => {
  if (!files) return;
  
  const filesToDelete = Array.isArray(files) ? files : [files];
  
  filesToDelete.forEach(file => {
    if (file && file.path && fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }
  });
};

// Helper function to get file URL
const getFileUrl = (filename) => {
  return `/uploads/products/${filename}`;
};

module.exports = {
  uploadSingleProductImage,
  uploadMultipleProductImages,
  handleUploadError,
  deleteUploadedFiles,
  getFileUrl
};
