const express = require('express');
const router = express.Router();
const { authenticate } = require('../middleware/auth');
const notificationController = require('../controllers/notificationController');
const emailController = require('../controllers/emailController');
const bannerController = require('../controllers/bannerController');

// Push notification routes (user)
router.post('/push/subscribe', authenticate, notificationController.subscribeToPush);
router.post('/push/unsubscribe', authenticate, notificationController.unsubscribeFromPush);

// Newsletter routes (public)
router.post('/newsletter/subscribe', emailController.subscribeToNewsletter);
router.post('/newsletter/unsubscribe', emailController.unsubscribeFromNewsletter);

// Banner routes (public)
router.get('/banner/active', bannerController.getActiveBanner);

module.exports = router;
