const { models } = require('../models');
const { validationResult } = require('express-validator');
const DiscountService = require('../services/discountService');
const { Op } = require('sequelize');

/**
 * Admin Discount Controller
 * Handles admin-specific discount and coupon management
 */
class AdminDiscountController {
  /**
   * Get all coupons with pagination and filtering
   * @route GET /api/admin/coupons
   * @access Private (Admin)
   */
  static async getAllCoupons(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        search = '',
        type = '',
        status = '',
        sortBy = 'createdAt',
        sortOrder = 'DESC'
      } = req.query;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Apply filters
      if (search) {
        whereClause[Op.or] = [
          { code: { [Op.iLike]: `%${search}%` } },
          { name: { [Op.iLike]: `%${search}%` } }
        ];
      }

      if (type) {
        whereClause.type = type;
      }

      if (status === 'active') {
        whereClause.isActive = true;
        whereClause.validFrom = { [Op.lte]: new Date() };
        whereClause.validUntil = { [Op.gte]: new Date() };
      } else if (status === 'inactive') {
        whereClause[Op.or] = [
          { isActive: false },
          { validUntil: { [Op.lt]: new Date() } }
        ];
      } else if (status === 'expired') {
        whereClause.validUntil = { [Op.lt]: new Date() };
      }

      const { count, rows: coupons } = await models.Coupon.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: models.CouponUsage,
            as: 'usages',
            attributes: ['id', 'discountAmount', 'createdAt']
          },
          {
            model: models.User,
            as: 'creator',
            attributes: ['firstName', 'lastName'],
            required: false
          }
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      // Calculate usage statistics for each coupon
      const couponsWithStats = coupons.map(coupon => {
        const totalUsages = coupon.usages.length;
        const totalDiscount = coupon.usages.reduce((sum, usage) => sum + parseFloat(usage.discountAmount), 0);
        const usageRate = coupon.usageLimit ? (totalUsages / coupon.usageLimit) * 100 : 0;

        return {
          ...coupon.toJSON(),
          statistics: {
            totalUsages,
            totalDiscount,
            usageRate: Math.round(usageRate * 100) / 100,
            isExpired: new Date(coupon.validUntil) < new Date(),
            daysUntilExpiry: Math.ceil((new Date(coupon.validUntil) - new Date()) / (1000 * 60 * 60 * 24))
          }
        };
      });

      res.json({
        success: true,
        data: {
          coupons: couponsWithStats,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalCoupons: count,
            limit: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('Get all coupons error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch coupons',
        error: error.message
      });
    }
  }

  /**
   * Create new coupon
   * @route POST /api/admin/coupons
   * @access Private (Admin)
   */
  static async createCoupon(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const couponData = {
        ...req.body,
        code: req.body.code.toUpperCase(),
        createdBy: req.user.id
      };

      // Check if coupon code already exists
      const existingCoupon = await models.Coupon.findOne({
        where: { code: couponData.code }
      });

      if (existingCoupon) {
        return res.status(400).json({
          success: false,
          message: 'Coupon code already exists'
        });
      }

      const coupon = await models.Coupon.create(couponData);

      res.status(201).json({
        success: true,
        data: coupon,
        message: 'Coupon created successfully'
      });

    } catch (error) {
      console.error('Create coupon error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create coupon',
        error: error.message
      });
    }
  }

  /**
   * Update coupon
   * @route PUT /api/admin/coupons/:id
   * @access Private (Admin)
   */
  static async updateCoupon(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const coupon = await models.Coupon.findByPk(id);
      if (!coupon) {
        return res.status(404).json({
          success: false,
          message: 'Coupon not found'
        });
      }

      // Check if code is being changed and if new code already exists
      if (req.body.code && req.body.code.toUpperCase() !== coupon.code) {
        const existingCoupon = await models.Coupon.findOne({
          where: { 
            code: req.body.code.toUpperCase(),
            id: { [Op.ne]: id }
          }
        });

        if (existingCoupon) {
          return res.status(400).json({
            success: false,
            message: 'Coupon code already exists'
          });
        }
      }

      const updateData = {
        ...req.body,
        code: req.body.code ? req.body.code.toUpperCase() : coupon.code
      };

      await coupon.update(updateData);

      res.json({
        success: true,
        data: coupon,
        message: 'Coupon updated successfully'
      });

    } catch (error) {
      console.error('Update coupon error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update coupon',
        error: error.message
      });
    }
  }

  /**
   * Delete coupon
   * @route DELETE /api/admin/coupons/:id
   * @access Private (Admin)
   */
  static async deleteCoupon(req, res) {
    try {
      const { id } = req.params;

      const coupon = await models.Coupon.findByPk(id, {
        include: [
          {
            model: models.CouponUsage,
            as: 'usages'
          }
        ]
      });

      if (!coupon) {
        return res.status(404).json({
          success: false,
          message: 'Coupon not found'
        });
      }

      // Check if coupon has been used
      if (coupon.usages.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete coupon that has been used. Consider deactivating it instead.'
        });
      }

      await coupon.destroy();

      res.json({
        success: true,
        message: 'Coupon deleted successfully'
      });

    } catch (error) {
      console.error('Delete coupon error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete coupon',
        error: error.message
      });
    }
  }

  /**
   * Get coupon usage analytics
   * @route GET /api/admin/coupons/analytics
   * @access Private (Admin)
   */
  static async getCouponAnalytics(req, res) {
    try {
      const { dateFrom, dateTo } = req.query;
      
      const dateFilter = {};
      if (dateFrom) dateFilter[Op.gte] = new Date(dateFrom);
      if (dateTo) dateFilter[Op.lte] = new Date(dateTo);

      // Get overall statistics
      const totalCoupons = await models.Coupon.count();
      const activeCoupons = await models.Coupon.count({
        where: {
          isActive: true,
          validFrom: { [Op.lte]: new Date() },
          validUntil: { [Op.gte]: new Date() }
        }
      });

      const totalUsages = await models.CouponUsage.count({
        where: dateFrom || dateTo ? { createdAt: dateFilter } : {}
      });

      const totalDiscountAmount = await models.CouponUsage.sum('discountAmount', {
        where: dateFrom || dateTo ? { createdAt: dateFilter } : {}
      });

      // Get usage by coupon type
      const usageByType = await models.CouponUsage.findAll({
        attributes: [
          [models.sequelize.col('coupon.type'), 'type'],
          [models.sequelize.fn('COUNT', models.sequelize.col('CouponUsage.id')), 'count'],
          [models.sequelize.fn('SUM', models.sequelize.col('CouponUsage.discountAmount')), 'totalDiscount']
        ],
        include: [
          {
            model: models.Coupon,
            as: 'coupon',
            attributes: []
          }
        ],
        where: dateFrom || dateTo ? { createdAt: dateFilter } : {},
        group: ['coupon.type'],
        raw: true
      });

      // Get top performing coupons
      const topCoupons = await models.CouponUsage.findAll({
        attributes: [
          [models.sequelize.col('coupon.code'), 'code'],
          [models.sequelize.col('coupon.name'), 'name'],
          [models.sequelize.fn('COUNT', models.sequelize.col('CouponUsage.id')), 'usageCount'],
          [models.sequelize.fn('SUM', models.sequelize.col('CouponUsage.discountAmount')), 'totalDiscount']
        ],
        include: [
          {
            model: models.Coupon,
            as: 'coupon',
            attributes: []
          }
        ],
        where: dateFrom || dateTo ? { createdAt: dateFilter } : {},
        group: ['coupon.id', 'coupon.code', 'coupon.name'],
        order: [[models.sequelize.fn('COUNT', models.sequelize.col('CouponUsage.id')), 'DESC']],
        limit: 10,
        raw: true
      });

      res.json({
        success: true,
        data: {
          overview: {
            totalCoupons,
            activeCoupons,
            expiredCoupons: totalCoupons - activeCoupons,
            totalUsages,
            totalDiscountAmount: totalDiscountAmount || 0,
            averageDiscountPerUse: totalUsages > 0 ? (totalDiscountAmount || 0) / totalUsages : 0
          },
          usageByType,
          topCoupons
        }
      });

    } catch (error) {
      console.error('Get coupon analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch coupon analytics',
        error: error.message
      });
    }
  }

  /**
   * Get referral system analytics
   * @route GET /api/admin/referrals/analytics
   * @access Private (Admin)
   */
  static async getReferralAnalytics(req, res) {
    try {
      const totalReferrals = await models.Referral.count();
      const completedReferrals = await models.Referral.count({
        where: { status: 'completed' }
      });
      const pendingReferrals = await models.Referral.count({
        where: { status: 'pending' }
      });

      const totalRewards = await models.Referral.sum('rewardAmount', {
        where: { status: 'rewarded' }
      });

      // Get top referrers
      const topReferrers = await models.Referral.findAll({
        attributes: [
          [models.sequelize.col('referrer.firstName'), 'firstName'],
          [models.sequelize.col('referrer.lastName'), 'lastName'],
          [models.sequelize.col('referrer.email'), 'email'],
          [models.sequelize.fn('COUNT', models.sequelize.col('Referral.id')), 'referralCount'],
          [models.sequelize.fn('SUM', models.sequelize.col('Referral.rewardAmount')), 'totalRewards']
        ],
        include: [
          {
            model: models.User,
            as: 'referrer',
            attributes: []
          }
        ],
        where: { status: { [Op.in]: ['completed', 'rewarded'] } },
        group: ['referrer.id', 'referrer.firstName', 'referrer.lastName', 'referrer.email'],
        order: [[models.sequelize.fn('COUNT', models.sequelize.col('Referral.id')), 'DESC']],
        limit: 10,
        raw: true
      });

      res.json({
        success: true,
        data: {
          overview: {
            totalReferrals,
            completedReferrals,
            pendingReferrals,
            conversionRate: totalReferrals > 0 ? (completedReferrals / totalReferrals) * 100 : 0,
            totalRewards: totalRewards || 0
          },
          topReferrers
        }
      });

    } catch (error) {
      console.error('Get referral analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch referral analytics',
        error: error.message
      });
    }
  }

  /**
   * Verify social media share
   * @route PUT /api/admin/social-shares/:id/verify
   * @access Private (Admin)
   */
  static async verifySocialShare(req, res) {
    try {
      const { id } = req.params;
      const { bonusAmount = 1.00 } = req.body;

      const result = await DiscountService.verifySocialShare(id, bonusAmount);

      if (result.success) {
        res.json({
          success: true,
          data: result,
          message: result.message
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message
        });
      }

    } catch (error) {
      console.error('Verify social share error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to verify social share',
        error: error.message
      });
    }
  }
}

module.exports = AdminDiscountController;
