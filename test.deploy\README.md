# Nirvana Organics E-commerce - Testing Environment

This package contains the testing/staging deployment for the Nirvana Organics E-commerce platform.

## 🎯 Testing Environment Details

- **Target Domain**: test.shopnirvanaorganics.com
- **Environment**: Testing/Staging
- **Purpose**: Pre-production testing and validation
- **Configuration**: Debug logging, relaxed rate limits

## 🚀 Quick Deployment

1. **Upload this package to your VPS server**
2. **Extract and run the deployment script:**
   ```bash
   tar -xzf nirvana-testing-deployment.tar.gz
   cd test.deploy
   chmod +x deploy-testing.sh
   sudo ./deploy-testing.sh
   ```
3. **Configure your domain DNS to point to the server**
4. **Set up SSL certificates for test.shopnirvanaorganics.com**

## 📋 Prerequisites

- Ubuntu 20.04+ or CentOS 8+ VPS server
- Root or sudo access
- Minimum 1GB RAM, 10GB storage
- Domain name: test.shopnirvanaorganics.com

## 🏗️ What's Included

### Frontend Applications
- **Main E-commerce Store**: Accessible at `/` (root path)
- **Admin Panel**: Accessible at `/admin` path
- **Unified Build**: Both applications served from single domain

### Backend Services
- **Node.js API Server**: Complete REST API with authentication
- **Database Integration**: MySQL with automated setup
- **Square Payment Integration**: Sandbox environment
- **Email System**: SMTP configuration for notifications

### Testing-Specific Features
- **Debug Logging**: Verbose logging for troubleshooting
- **Relaxed Rate Limits**: Higher limits for testing scenarios
- **Testing Headers**: X-Environment headers for identification
- **Sandbox Payments**: Square sandbox integration

## ⚙️ Configuration

### Environment Variables
Edit `/var/www/nirvana-backend/.env` with your testing values:

- **Database**: MySQL connection for testing database
- **JWT Secrets**: Secure authentication tokens
- **Email**: SMTP configuration for test notifications
- **Square**: Sandbox payment processing credentials
- **Domain**: test.shopnirvanaorganics.com

### SSL Certificates
For Let's Encrypt:
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d test.shopnirvanaorganics.com
```

## 🔧 Management Commands

### Application Management
```bash
# Check application status
sudo pm2 status

# View application logs
sudo pm2 logs nirvana-testing

# Restart application
sudo pm2 restart nirvana-testing

# Stop application
sudo pm2 stop nirvana-testing
```

### Database Management
```bash
# Create database backup
cd /var/www/nirvana-backend
node scripts/backup-database.js

# Run database migrations
node scripts/run-migrations.js

# Seed test data
node scripts/seed-database.js

# Create admin user
node scripts/create-default-admin.js
```

## 🧪 Testing Features

### Admin Panel Testing
- Access: https://test.shopnirvanaorganics.com/admin
- Default admin credentials will be created during setup
- Full admin functionality for testing

### Payment Testing
- Square Sandbox environment
- Test credit card numbers
- No real transactions processed

### Email Testing
- All emails sent to configured test email
- Email templates and functionality testing

## 🔒 Security Features

- **Testing Environment Headers**: Clear identification
- **Robots.txt**: Blocks search engine indexing
- **Relaxed Rate Limits**: Suitable for testing scenarios
- **Debug Logging**: Detailed logs for troubleshooting

## 📊 Monitoring

### Log Files
- **Application Logs**: `/var/www/nirvana-backend/logs/`
- **PM2 Logs**: `pm2 logs nirvana-testing`
- **Nginx Logs**: `/var/log/nginx/nirvana-testing-*`

### Health Checks
```bash
# Check all services
cd /var/www/nirvana-backend
node scripts/system-status.js

# Test API health
curl https://test.shopnirvanaorganics.com/api/health
```

## 🆘 Troubleshooting

### Common Issues

**Application won't start:**
```bash
# Check PM2 logs
sudo pm2 logs nirvana-testing

# Verify environment file
cat /var/www/nirvana-backend/.env

# Test database connection
cd /var/www/nirvana-backend
node scripts/test-database-connection.js
```

**Admin panel not accessible:**
```bash
# Check if admin build exists
ls -la /var/www/nirvana-backend/dist/admin/

# Verify nginx configuration
sudo nginx -t

# Check admin routes in logs
sudo tail -f /var/log/nginx/nirvana-testing-access.log
```

## 🔄 Updates

To update the testing environment:
1. Upload new deployment package
2. Stop the application: `sudo pm2 stop nirvana-testing`
3. Backup current installation
4. Replace application files
5. Run migrations if needed
6. Start the application: `sudo pm2 start nirvana-testing`

---

**Environment**: Testing/Staging
**Target Domain**: test.shopnirvanaorganics.com
**Last Updated**: 2025-07-30