-- Database Setup Script for Testing Environment
-- test.shopnirvanaorganics.com

-- Create testing database
CREATE DATABASE IF NOT EXISTS nirvana_organics_testing
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- Create testing user
CREATE USER IF NOT EXISTS 'nirvana_test_user'@'localhost' IDENTIFIED BY 'your-secure-test-db-password';

-- Grant privileges to testing user
GRANT ALL PRIVILEGES ON nirvana_organics_testing.* TO 'nirvana_test_user'@'localhost';

-- Grant specific privileges for backup operations
GRANT SELECT, LOCK TABLES ON nirvana_organics_testing.* TO 'nirvana_test_user'@'localhost';

-- Create backup user (optional, for automated backups)
CREATE USER IF NOT EXISTS 'nirvana_backup_test'@'localhost' IDENTIFIED BY 'your-secure-backup-password';
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON nirvana_organics_testing.* TO 'nirvana_backup_test'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the testing database
USE nirvana_organics_testing;

-- Create initial admin user (will be updated by migrations)
-- This is just a placeholder - actual schema will be created by Sequelize migrations

-- Show created database and users
SELECT 'Database created successfully' as Status;
SHOW DATABASES LIKE 'nirvana_organics_testing';

SELECT 'Users created successfully' as Status;
SELECT User, Host FROM mysql.user WHERE User LIKE 'nirvana_%';

-- Show granted privileges
SELECT 'Privileges granted successfully' as Status;
SHOW GRANTS FOR 'nirvana_test_user'@'localhost';
SHOW GRANTS FOR 'nirvana_backup_test'@'localhost';
