#!/usr/bin/env node

/**
 * Deployment Verification Script for Nirvana Organics E-commerce
 * Validates that the deployed application is working correctly
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}[STEP]${colors.reset} ${msg}`)
};

// Configuration
const config = {
  domain: process.env.APP_URL || 'https://test.shopnirvanaorganics.com',
  timeout: 10000,
  expectedStatusCodes: {
    health: 200,
    frontend: 200,
    api: 200
  }
};

let verificationScore = 0;
let totalChecks = 0;
const issues = [];

function addCheck(passed, message, severity = 'medium') {
  totalChecks++;
  if (passed) {
    verificationScore++;
    log.success(message);
  } else {
    issues.push({ message, severity });
    if (severity === 'critical') {
      log.error(message);
    } else {
      log.warning(message);
    }
  }
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const timeout = options.timeout || config.timeout;
    
    const req = protocol.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          responseTime: Date.now() - startTime
        });
      });
    });
    
    const startTime = Date.now();
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.setTimeout(timeout);
  });
}

async function checkHealthEndpoint() {
  log.step('1. Checking Health Endpoint...');
  
  try {
    const healthUrl = `${config.domain}/api/health`;
    const response = await makeRequest(healthUrl);
    
    addCheck(
      response.statusCode === 200,
      `Health endpoint responds with status ${response.statusCode}`,
      'critical'
    );
    
    if (response.statusCode === 200) {
      try {
        const healthData = JSON.parse(response.data);
        
        addCheck(
          healthData.status === 'ok' || healthData.status === 'degraded',
          `Health status: ${healthData.status}`,
          healthData.status === 'ok' ? 'low' : 'medium'
        );
        
        addCheck(
          healthData.responseTime && parseInt(healthData.responseTime) < 1000,
          `Health response time: ${healthData.responseTime}`,
          'medium'
        );
        
        if (healthData.services) {
          const services = Object.keys(healthData.services);
          addCheck(
            services.length >= 3,
            `Health monitoring covers ${services.length} services`,
            'medium'
          );
          
          // Check individual services
          if (healthData.services.memory) {
            addCheck(
              healthData.services.memory.status === 'healthy',
              `Memory monitoring: ${healthData.services.memory.status}`,
              'medium'
            );
          }
          
          if (healthData.services.storage) {
            addCheck(
              healthData.services.storage.status === 'healthy',
              `Storage monitoring: ${healthData.services.storage.status}`,
              'medium'
            );
          }
        }
        
      } catch (parseError) {
        addCheck(false, 'Health endpoint returns valid JSON', 'high');
      }
    }
    
    addCheck(
      response.responseTime < 2000,
      `Health endpoint response time: ${response.responseTime}ms`,
      'medium'
    );
    
  } catch (error) {
    addCheck(false, `Health endpoint accessible: ${error.message}`, 'critical');
  }
}

async function checkFrontendEndpoint() {
  log.step('2. Checking Frontend Endpoint...');
  
  try {
    const response = await makeRequest(config.domain);
    
    addCheck(
      response.statusCode === 200,
      `Frontend responds with status ${response.statusCode}`,
      'critical'
    );
    
    addCheck(
      response.data.includes('<html') || response.data.includes('<!DOCTYPE'),
      'Frontend returns HTML content',
      'high'
    );
    
    addCheck(
      response.data.includes('Nirvana') || response.data.includes('nirvana'),
      'Frontend contains application branding',
      'medium'
    );
    
    addCheck(
      response.responseTime < 3000,
      `Frontend response time: ${response.responseTime}ms`,
      'medium'
    );
    
  } catch (error) {
    addCheck(false, `Frontend accessible: ${error.message}`, 'critical');
  }
}

async function checkSecurityHeaders() {
  log.step('3. Checking Security Headers...');
  
  try {
    const response = await makeRequest(config.domain);
    const headers = response.headers;
    
    // Check for security headers
    addCheck(
      !!headers['x-frame-options'],
      `X-Frame-Options header present: ${headers['x-frame-options'] || 'missing'}`,
      'high'
    );
    
    addCheck(
      !!headers['x-xss-protection'],
      `X-XSS-Protection header present: ${headers['x-xss-protection'] || 'missing'}`,
      'high'
    );
    
    addCheck(
      !!headers['x-content-type-options'],
      `X-Content-Type-Options header present: ${headers['x-content-type-options'] || 'missing'}`,
      'high'
    );
    
    addCheck(
      !!headers['strict-transport-security'],
      `HSTS header present: ${headers['strict-transport-security'] || 'missing'}`,
      'high'
    );
    
    addCheck(
      !headers['server'] || !headers['server'].toLowerCase().includes('nginx'),
      'Server header hidden or generic',
      'medium'
    );
    
  } catch (error) {
    addCheck(false, `Security headers check failed: ${error.message}`, 'medium');
  }
}

async function checkSSLCertificate() {
  log.step('4. Checking SSL Certificate...');
  
  if (!config.domain.startsWith('https')) {
    addCheck(false, 'HTTPS is not configured', 'critical');
    return;
  }
  
  try {
    const response = await makeRequest(config.domain);
    addCheck(true, 'SSL certificate is valid and accessible', 'high');
    
    // Check if HTTP redirects to HTTPS
    if (config.domain.startsWith('https')) {
      const httpUrl = config.domain.replace('https', 'http');
      try {
        const httpResponse = await makeRequest(httpUrl);
        addCheck(
          httpResponse.statusCode === 301 || httpResponse.statusCode === 302,
          `HTTP redirects to HTTPS (${httpResponse.statusCode})`,
          'high'
        );
      } catch (httpError) {
        // This might be expected if HTTP is blocked
        log.info('HTTP endpoint not accessible (may be intentionally blocked)');
      }
    }
    
  } catch (error) {
    addCheck(false, `SSL certificate check failed: ${error.message}`, 'critical');
  }
}

async function checkAPIEndpoints() {
  log.step('5. Checking API Endpoints...');
  
  const apiEndpoints = [
    '/api/health/ready',
    '/api/health/live',
    '/api/health/metrics'
  ];
  
  for (const endpoint of apiEndpoints) {
    try {
      const url = `${config.domain}${endpoint}`;
      const response = await makeRequest(url);
      
      addCheck(
        response.statusCode === 200,
        `${endpoint} responds with status ${response.statusCode}`,
        'medium'
      );
      
    } catch (error) {
      addCheck(false, `${endpoint} accessible: ${error.message}`, 'medium');
    }
  }
}

async function checkRateLimiting() {
  log.step('6. Checking Rate Limiting...');
  
  try {
    const healthUrl = `${config.domain}/api/health`;
    const requests = [];
    
    // Make multiple rapid requests
    for (let i = 0; i < 10; i++) {
      requests.push(makeRequest(healthUrl));
    }
    
    const responses = await Promise.allSettled(requests);
    const successfulRequests = responses.filter(r => r.status === 'fulfilled' && r.value.statusCode === 200);
    
    addCheck(
      successfulRequests.length > 0,
      `Rate limiting allows normal requests (${successfulRequests.length}/10 succeeded)`,
      'medium'
    );
    
    // Note: We don't test for rate limit blocking as it might interfere with other checks
    log.info('Rate limiting configuration appears to be active');
    
  } catch (error) {
    addCheck(false, `Rate limiting check failed: ${error.message}`, 'low');
  }
}

async function checkFileSystem() {
  log.step('7. Checking File System...');
  
  // Check if we're running on the server
  const expectedPaths = [
    '/var/www/nirvana-backend/server',
    '/var/www/nirvana-backend/dist',
    '/var/www/nirvana-backend/.env',
    '/var/www/nirvana-backend/logs'
  ];
  
  for (const filePath of expectedPaths) {
    if (fs.existsSync(filePath)) {
      addCheck(true, `Required path exists: ${filePath}`, 'medium');
    } else {
      // Only warn if we're running on the server
      if (process.cwd().includes('/var/www/nirvana-backend')) {
        addCheck(false, `Required path missing: ${filePath}`, 'high');
      }
    }
  }
  
  // Check log files
  const logDir = '/var/www/nirvana-backend/logs';
  if (fs.existsSync(logDir)) {
    const logFiles = fs.readdirSync(logDir);
    addCheck(
      logFiles.length > 0,
      `Log files present: ${logFiles.length} files`,
      'medium'
    );
  }
}

function generateVerificationReport() {
  log.step('8. Generating Verification Report...');
  
  const percentage = Math.round((verificationScore / totalChecks) * 100);
  const grade = percentage >= 90 ? 'A' : percentage >= 80 ? 'B' : percentage >= 70 ? 'C' : percentage >= 60 ? 'D' : 'F';
  
  console.log('\n' + '='.repeat(60));
  console.log(`${colors.bright}DEPLOYMENT VERIFICATION REPORT${colors.reset}`);
  console.log('='.repeat(60));
  console.log(`Verification Score: ${colors.bright}${verificationScore}/${totalChecks} (${percentage}%)${colors.reset}`);
  console.log(`Deployment Grade: ${colors.bright}${grade}${colors.reset}`);
  console.log(`Domain: ${colors.cyan}${config.domain}${colors.reset}`);
  console.log('='.repeat(60));
  
  if (issues.length > 0) {
    console.log(`\n${colors.red}ISSUES FOUND:${colors.reset}`);
    
    const criticalIssues = issues.filter(i => i.severity === 'critical');
    const highIssues = issues.filter(i => i.severity === 'high');
    const mediumIssues = issues.filter(i => i.severity === 'medium');
    
    if (criticalIssues.length > 0) {
      console.log(`\n${colors.red}CRITICAL (${criticalIssues.length}):${colors.reset}`);
      criticalIssues.forEach(issue => console.log(`  ❌ ${issue.message}`));
    }
    
    if (highIssues.length > 0) {
      console.log(`\n${colors.yellow}HIGH (${highIssues.length}):${colors.reset}`);
      highIssues.forEach(issue => console.log(`  ⚠️  ${issue.message}`));
    }
    
    if (mediumIssues.length > 0) {
      console.log(`\n${colors.blue}MEDIUM (${mediumIssues.length}):${colors.reset}`);
      mediumIssues.forEach(issue => console.log(`  ℹ️  ${issue.message}`));
    }
  } else {
    console.log(`\n${colors.green}✅ No issues found!${colors.reset}`);
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (percentage < 70) {
    console.log(`${colors.red}❌ DEPLOYMENT VERIFICATION FAILED${colors.reset}`);
    console.log('Please address the critical and high-priority issues above.');
  } else if (percentage < 85) {
    console.log(`${colors.yellow}⚠️  DEPLOYMENT PARTIALLY VERIFIED${colors.reset}`);
    console.log('Consider addressing the remaining issues for better reliability.');
  } else {
    console.log(`${colors.green}✅ DEPLOYMENT SUCCESSFULLY VERIFIED!${colors.reset}`);
    console.log('Your application is running correctly and meets verification standards.');
  }
  
  return percentage >= 70;
}

// Main execution
async function main() {
  console.log(`${colors.bright}Nirvana Organics E-commerce Deployment Verification${colors.reset}\n`);
  console.log(`Target Domain: ${colors.cyan}${config.domain}${colors.reset}\n`);
  
  try {
    await checkHealthEndpoint();
    await checkFrontendEndpoint();
    await checkSecurityHeaders();
    await checkSSLCertificate();
    await checkAPIEndpoints();
    await checkRateLimiting();
    await checkFileSystem();
    
    const passed = generateVerificationReport();
    
    process.exit(passed ? 0 : 1);
  } catch (error) {
    log.error(`Verification failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.length > 2) {
  config.domain = process.argv[2];
}

main();
