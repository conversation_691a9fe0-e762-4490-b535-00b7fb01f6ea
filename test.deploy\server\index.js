require('dotenv').config();

// Validate critical environment variables
const validateEnvironment = () => {
  const requiredVars = [
    'NODE_ENV',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'EMAIL_HOST',
    'EMAIL_USER',
    'EMAIL_PASS'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars.join(', '));
    console.error('Please check your .env file and ensure all required variables are set.');
    process.exit(1);
  }

  // Validate production-specific requirements
  if (process.env.NODE_ENV === 'production') {
    const productionVars = ['FRONTEND_URL', 'API_BASE_URL'];
    const missingProdVars = productionVars.filter(varName => !process.env[varName]);

    if (missingProdVars.length > 0) {
      console.error('❌ Missing production environment variables:', missingProdVars.join(', '));
      process.exit(1);
    }

    // Check HTTPS enforcement
    if (process.env.FORCE_HTTPS !== 'true') {
      console.warn('⚠️ HTTPS is not enforced in production environment');
    }
  }

  console.log('✅ Environment validation passed');
};

// Run environment validation
validateEnvironment();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const http = require('http');
const https = require('https');
const { connectDB } = require('./config/database');

// Import SSL configuration
const sslConfig = require('./config/ssl');
const { initializeDebugMiddleware } = require('./middleware/debug');
const {
  generalLimiter,
  smartAuthLimiter,
  apiLimiter,
  speedLimiter,
  sanitizeInput,
  securityHeaders,
  requestSizeLimiter
} = require('./middleware/security');
const performanceMonitor = require('./utils/performanceMonitor');
const { globalErrorHandler, handleNotFound } = require('./middleware/errorHandler');
const { responseMiddleware } = require('./utils/apiResponse');
const {
  requestLogger,
  appLogger
} = require('./utils/logger');
const http = require('http');
const realTimeService = require('./services/realTimeService');
const socialMediaScheduler = require('./services/socialMediaScheduler');

// Environment Variables Validation
console.log('🔍 Validating environment variables...');
const requiredEnvVars = [
  'NODE_ENV',
  'PORT',
  'DB_HOST',
  'DB_USER',
  'DB_PASSWORD',
  'DB_NAME',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'SESSION_SECRET',
  'ADMIN_EMAIL'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars);
  console.error('Please check your .env file and ensure all required variables are set');
  process.exit(1);
}

// Validate critical security variables
if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
  console.error('❌ JWT_SECRET must be at least 32 characters long for security');
  process.exit(1);
}

if (process.env.SESSION_SECRET && process.env.SESSION_SECRET.length < 32) {
  console.error('❌ SESSION_SECRET must be at least 32 characters long for security');
  process.exit(1);
}

if (process.env.JWT_SECRET === process.env.SESSION_SECRET) {
  console.error('❌ JWT_SECRET and SESSION_SECRET must be different for security');
  process.exit(1);
}

console.log('✅ Environment variables validation passed');

// Import routes
const authRoutes = require('./routes/auth');
const productRoutes = require('./routes/products');
const categoryRoutes = require('./routes/categories');
const cartRoutes = require('./routes/cart');
const orderRoutes = require('./routes/orders');
const webhookRoutes = require('./routes/webhooks');
const adminRoutes = require('./routes/admin');
const rbacRoutes = require('./routes/rbac');
const notificationRoutes = require('./routes/notifications');
const uploadRoutes = require('./routes/upload');
const guestRoutes = require('./routes/guest');
const contactRoutes = require('./routes/contact');
const shopFinderRoutes = require('./routes/shopFinder');
const adminShopFinderRoutes = require('./routes/adminShopFinder');
const orderVerificationRoutes = require('./routes/orderVerification');
const trackingRoutes = require('./routes/tracking');
const adminSocialMediaRoutes = require('./routes/admin/socialMedia');
const discountRoutes = require('./routes/discount');
const adminDiscountRoutes = require('./routes/adminDiscount');
const campaignRoutes = require('./routes/campaign');
const emailManagementRoutes = require('./routes/emailManagement');
const healthRoutes = require('./routes/health');

// Initialize Express app
const app = express();

// Initialize Passport for social authentication
const passport = require('./config/passport');
const session = require('express-session');

// Session configuration for Passport
app.use(session({
  secret: process.env.SESSION_SECRET || (() => {
    console.error('❌ CRITICAL: SESSION_SECRET environment variable is required for security');
    console.error('Please set SESSION_SECRET in your .env file with a secure 32+ character string');
    process.exit(1);
  })(),
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Initialize Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Trust proxy for HTTPS (important for Hostinger)
if (process.env.NODE_ENV === 'production') {
  app.set('trust proxy', 1);
}

// Connect to database
connectDB();

// Force HTTPS in production (must be before other middleware)
if (process.env.NODE_ENV === 'production' && process.env.FORCE_HTTPS === 'true') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      return res.redirect(301, `https://${req.header('host')}${req.url}`);
    }
    next();
  });
}

// Request logging middleware (before other middleware)
app.use(requestLogger);

// Security middleware
app.use(securityHeaders);
app.use(helmet({
  contentSecurityPolicy: false, // We handle CSP in securityHeaders
  crossOriginEmbedderPolicy: false
}));

// Initialize debug middleware based on environment variables
initializeDebugMiddleware(app);

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Request size limiting
app.use(requestSizeLimiter('10mb'));

// General rate limiting
app.use(generalLimiter);

// Speed limiting for repeated requests
app.use(speedLimiter);

// CORS configuration with support for multiple origins
const corsOptions = {
  origin: process.env.CORS_ORIGIN ?
    process.env.CORS_ORIGIN.split(',').map(origin => origin.trim()) :
    'http://localhost:5173',
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// Performance monitoring middleware
app.use(performanceMonitor.trackRequest());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization
app.use(sanitizeInput);

// Add response helpers
app.use(responseMiddleware);

// Health check endpoint
// Health check routes (no rate limiting for health checks)
app.use('/api/health', healthRoutes);
app.use('/health', healthRoutes);

// API routes with specific rate limiting
app.use('/api/auth', smartAuthLimiter, authRoutes);
app.use('/api/products', apiLimiter, productRoutes);
app.use('/api/categories', apiLimiter, categoryRoutes);
app.use('/api/cart', apiLimiter, cartRoutes);
app.use('/api/orders', apiLimiter, orderRoutes);
app.use('/api/admin', apiLimiter, adminRoutes);
app.use('/api/admin/rbac', apiLimiter, rbacRoutes); // RBAC routes under admin
app.use('/api/auth', smartAuthLimiter, rbacRoutes); // Public RBAC routes under auth
app.use('/api/notifications', apiLimiter, notificationRoutes);
app.use('/api/upload', apiLimiter, uploadRoutes);
app.use('/api/guest', apiLimiter, guestRoutes);
app.use('/api/contact', contactRoutes); // Contact routes have their own rate limiting
app.use('/api/shop-finder', apiLimiter, shopFinderRoutes);
app.use('/api/admin/shop-finder', apiLimiter, adminShopFinderRoutes);
app.use('/api/admin/social-media', apiLimiter, adminSocialMediaRoutes);
app.use('/api', apiLimiter, discountRoutes);
app.use('/api/admin', apiLimiter, adminDiscountRoutes);
app.use('/api', apiLimiter, campaignRoutes);
app.use('/api/admin/email-management', apiLimiter, emailManagementRoutes);
app.use('/api/admin/order-verification', apiLimiter, orderVerificationRoutes);
app.use('/api/admin/tracking', apiLimiter, trackingRoutes);
app.use('/api/tracking', apiLimiter, trackingRoutes);
app.use('/api/webhooks', webhookRoutes); // No rate limiting for webhooks

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, '../public/uploads')));

// Serve public assets (logos, favicons, etc.)
app.use('/assets', express.static(path.join(__dirname, '../public')));

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static('dist'));

  app.get('*', (_, res) => {
    res.sendFile(path.resolve(__dirname, '../dist', 'index.html'));
  });
}

// Global error handler
app.use((err, _req, res, _next) => {
  console.error('Global error handler:', err);

  // Sequelize validation error
  if (err.name === 'SequelizeValidationError') {
    const errors = err.errors.map(e => e.message);
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      errors
    });
  }

  // Sequelize unique constraint error
  if (err.name === 'SequelizeUniqueConstraintError') {
    const field = err.errors[0]?.path || 'field';
    return res.status(400).json({
      success: false,
      message: `${field} already exists`
    });
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired'
    });
  }

  // Default error
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal Server Error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// 404 handler
app.use('*', (_, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Handle unhandled routes
app.all('*', handleNotFound);

// Global error handling middleware
app.use(globalErrorHandler);

// Create server (HTTP or HTTPS based on SSL configuration)
const PORT = process.env.PORT || 5000;
const HTTPS_PORT = process.env.HTTPS_PORT || 443;

let server;
let httpsServer;

// Create HTTP server
server = http.createServer(app);

// Create HTTPS server if SSL is configured
if (sslConfig.isEnabled()) {
  try {
    httpsServer = sslConfig.createHTTPSServer(app);
    console.log('🔒 HTTPS server configured with SSL certificates');
  } catch (error) {
    console.error('❌ Failed to create HTTPS server:', error);
    console.log('⚠️ Falling back to HTTP only');
  }
}

// Initialize real-time service with WebSocket
realTimeService.initialize(server);
if (httpsServer) {
  realTimeService.initialize(httpsServer);
}

// Start HTTP server
server.listen(PORT, '0.0.0.0', () => {
  const protocol = sslConfig.isEnabled() ? 'http' : 'http';
  const message = `🚀 HTTP Server running on port ${PORT} in ${process.env.NODE_ENV} mode`;
  console.log(message);
  console.log(`📱 Admin panel: ${protocol}://localhost:${PORT}/admin`);
  console.log(`🌐 API base URL: ${protocol}://localhost:${PORT}/api`);
  console.log(`📊 Health check: ${protocol}://localhost:${PORT}/api/health`);
  console.log(`🔌 WebSocket server initialized for real-time order monitoring`);

  // Initialize social media scheduler
  socialMediaScheduler.init();

  // Log application startup
  appLogger.startup(PORT, process.env.NODE_ENV);
});

// Start HTTPS server if configured
if (httpsServer) {
  httpsServer.listen(HTTPS_PORT, '0.0.0.0', () => {
    const message = `🔒 HTTPS Server running on port ${HTTPS_PORT} in ${process.env.NODE_ENV} mode`;
    console.log(message);
    console.log(`📱 Admin panel: https://localhost:${HTTPS_PORT}/admin`);
    console.log(`🌐 API base URL: https://localhost:${HTTPS_PORT}/api`);
    console.log(`📊 Health check: https://localhost:${HTTPS_PORT}/api/health`);

    // Log HTTPS startup
    appLogger.info('HTTPS server started', { port: HTTPS_PORT, ssl: true });
  });
}

// Graceful shutdown with logging
const gracefulShutdown = (signal) => {
  console.log(`${signal} received, shutting down gracefully`);
  appLogger.shutdown(signal);

  let serversToClose = 1;
  let serversClosed = 0;

  const checkAllClosed = () => {
    serversClosed++;
    if (serversClosed >= serversToClose) {
      console.log('All servers closed, process terminated');
      process.exit(0);
    }
  };

  // Close HTTP server
  server.close(() => {
    console.log('HTTP server closed');
    checkAllClosed();
  });

  // Close HTTPS server if it exists
  if (httpsServer) {
    serversToClose = 2;
    httpsServer.close(() => {
      console.log('HTTPS server closed');
      checkAllClosed();
    });
  }

  // Force close after 10 seconds
  setTimeout(() => {
    console.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle unhandled promise rejections with logging
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  appLogger.error(new Error(reason), {
    type: 'unhandled_rejection',
    promise: promise.toString()
  });
});

// Handle uncaught exceptions with logging
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  appLogger.error(err, { type: 'uncaught_exception' });
  process.exit(1);
});

module.exports = app;
