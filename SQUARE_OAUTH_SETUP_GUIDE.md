# Square OAuth Authentication Setup Guide

This guide will help you set up Square OAuth authentication for admin users in the Nirvana Organics e-commerce platform.

## 🔧 Prerequisites

1. **Square Developer Account**: You need a Square Developer account
2. **Square Application**: Create a Square application in the Developer Dashboard
3. **Environment Variables**: Properly configured environment variables

## 📋 Step 1: Square Developer Dashboard Setup

### 1.1 Create Square Application

1. Go to [Square Developer Dashboard](https://developer.squareup.com/apps)
2. Click "Create App"
3. Fill in application details:
   - **App Name**: "Nirvana Organics Admin Auth"
   - **Description**: "OAuth authentication for Nirvana Organics admin panel"
   - **App Type**: Choose appropriate type for your business

### 1.2 Configure OAuth Settings

1. In your Square app dashboard, go to "OAuth" tab
2. Add redirect URLs:
   - **Development**: `http://localhost:5000/api/auth/square/callback`
   - **Testing**: `https://test.shopnirvanaorganics.com/api/auth/square/callback`
   - **Production**: `https://shopnirvanaorganics.com/api/auth/square/callback`

3. Set OAuth scopes:
   - `MERCHANT_PROFILE_READ` (required for admin identification)
   - `EMPLOYEES_READ` (optional, for employee management)

### 1.3 Get OAuth Credentials

1. Copy your **Application ID** (OAuth Client ID)
2. Copy your **Application Secret** (OAuth Client Secret)
3. Note your **Environment** (Sandbox or Production)

## 🔐 Step 2: Environment Configuration

### 2.1 Update Environment Files

Add the following variables to your environment files:

#### `.env.development`
```env
# Square OAuth Configuration (Development)
SQUARE_OAUTH_CLIENT_ID=your-sandbox-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-sandbox-square-oauth-client-secret
SQUARE_OAUTH_CALLBACK_URL=http://localhost:5000/api/auth/square/callback
SQUARE_ENVIRONMENT=sandbox
```

#### `.env.production`
```env
# Square OAuth Configuration (Production)
SQUARE_OAUTH_CLIENT_ID=your-production-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-production-square-oauth-client-secret
SQUARE_OAUTH_CALLBACK_URL=https://shopnirvanaorganics.com/api/auth/square/callback
SQUARE_ENVIRONMENT=production
```

### 2.2 Admin Email Configuration

Ensure your admin emails are configured to grant admin access to Square OAuth users:

```env
ADMIN_EMAIL=<EMAIL>
ADMIN_EMAIL_SECONDARY=<EMAIL>
```

## 🗄️ Step 3: Database Migration

Run the database migration to add Square ID field:

```bash
# Run the migration
npm run migrate

# Or manually run the specific migration
node server/migrations/003-add-square-authentication.js
```

This adds:
- `square_id` column to Users table
- Updates `auth_provider` enum to include 'square'

## 🧪 Step 4: Testing the Integration

### 4.1 Run the Test Script

```bash
node scripts/test-square-oauth.js
```

This script tests:
- Environment variables
- Database migration
- OAuth endpoints
- Passport strategy configuration
- Frontend integration

### 4.2 Manual Testing

1. **Start the server**:
   ```bash
   npm run dev
   ```

2. **Test OAuth Flow**:
   - Go to `http://localhost:5000/admin/login`
   - Click "Sign in with Square"
   - Should redirect to Square OAuth
   - Complete Square authorization
   - Should redirect back with admin access

## 🔒 Step 5: Security Considerations

### 5.1 Admin Role Assignment

Square OAuth users are granted admin access if:
1. Their business email matches `ADMIN_EMAIL` or `ADMIN_EMAIL_SECONDARY`
2. Or they're using a recognized Square merchant account

### 5.2 Merchant Validation

The system validates Square merchants by:
- Checking merchant profile data
- Verifying business information
- Ensuring proper OAuth scopes

### 5.3 Error Handling

Common error scenarios handled:
- OAuth denial by user
- Invalid Square credentials
- Network failures
- Missing merchant data

## 🚀 Step 6: Deployment

### 6.1 Environment-Specific Setup

**Development**:
- Use Square Sandbox environment
- Test with sandbox merchant accounts

**Production**:
- Use Square Production environment
- Configure with real merchant accounts
- Ensure HTTPS for OAuth callbacks

### 6.2 Frontend Integration

The Square OAuth button is available in:
- Admin login page (`/admin/login`)
- Social login component (for regular users if needed)

## 🔧 Troubleshooting

### Common Issues

1. **"square_auth_failed" Error**:
   - Check OAuth credentials
   - Verify callback URL matches Square app settings
   - Ensure proper environment configuration

2. **Database Errors**:
   - Run migration: `npm run migrate`
   - Check database connection
   - Verify Users table structure

3. **Admin Access Denied**:
   - Check `ADMIN_EMAIL` configuration
   - Verify Square merchant email
   - Check role assignment logic

4. **OAuth Redirect Issues**:
   - Verify callback URL in Square app
   - Check HTTPS configuration for production
   - Ensure proper CORS settings

### Debug Mode

Enable debug logging:
```env
DEBUG_MODE=true
LOG_LEVEL=debug
```

## 📚 API Reference

### OAuth Endpoints

- **Login**: `GET /api/auth/square/login`
- **Callback**: `GET /api/auth/square/callback`

### Square API Integration

The system uses Square's OAuth 2.0 flow:
1. Redirect to Square authorization server
2. User authorizes application
3. Square redirects back with authorization code
4. Exchange code for access token
5. Fetch merchant profile
6. Create/update user account
7. Assign appropriate role

## 🎯 Next Steps

1. **Test thoroughly** in sandbox environment
2. **Configure production** Square application
3. **Update environment variables** with production credentials
4. **Deploy and test** in production environment
5. **Monitor OAuth flows** and error rates

## 📞 Support

For Square OAuth specific issues:
- [Square Developer Documentation](https://developer.squareup.com/docs/oauth-api/overview)
- [Square Developer Community](https://developer.squareup.com/forums)

For implementation issues:
- Check server logs for detailed error messages
- Run the test script for diagnostic information
- Verify all environment variables are properly set
