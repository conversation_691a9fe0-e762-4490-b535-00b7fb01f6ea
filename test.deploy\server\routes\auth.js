const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const passport = require('../config/passport');
const { authenticate, verifyAge, requireCustomerOrAdmin } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const { adminAuthLimiter, auditDataEnvironmentOperation } = require('../config/security');

// Rate limiting for auth endpoints (temporarily increased for testing)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // temporarily increased from 5 to 100 for testing
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.'
  }
});

const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 registration attempts per hour
  message: {
    success: false,
    message: 'Too many registration attempts, please try again later.'
  }
});

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register',
  registerLimiter,
  [
    body('firstName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters'),
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Password confirmation does not match password');
        }
        return true;
      }),
    body('dateOfBirth')
      .isISO8601()
      .withMessage('Please provide a valid date of birth')
      .custom((value) => {
        const age = Math.floor((Date.now() - new Date(value)) / (365.25 * 24 * 60 * 60 * 1000));
        if (age < 21) {
          throw new Error('You must be at least 21 years old to register');
        }
        return true;
      }),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number')
  ],
  authController.register
);

// @route   POST /api/auth/login
// @desc    Login user (regular users only)
// @access  Public
router.post('/login',
  authLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    body('password')
      .notEmpty()
      .withMessage('Password is required')
  ],
  authController.loginUser
);

// @route   POST /api/auth/admin/login
// @desc    Login admin user
// @access  Public
router.post('/admin/login',
  adminAuthLimiter,
  auditDataEnvironmentOperation('ADMIN_LOGIN'),
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    body('password')
      .notEmpty()
      .withMessage('Password is required')
  ],
  authController.loginAdmin
);

// @route   POST /api/auth/refresh
// @desc    Refresh access token
// @access  Public
router.post('/refresh', authController.refreshToken);

// @route   POST /api/auth/verify-email
// @desc    Verify user email
// @access  Public
router.post('/verify-email', authController.verifyEmail);

// @route   GET /api/auth/profile
// @desc    Get current user profile
// @access  Private
router.get('/profile', authenticate, authController.getProfile);

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile',
  authenticate,
  [
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number')
  ],
  authController.updateProfile
);

// @route   POST /api/auth/request-password-reset
// @desc    Request password reset
// @access  Public
router.post('/request-password-reset',
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email')
  ],
  authController.requestPasswordReset
);

// @route   POST /api/auth/reset-password
// @desc    Reset password with token
// @access  Public
router.post('/reset-password',
  [
    body('token')
      .notEmpty()
      .withMessage('Reset token is required'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
  ],
  authController.resetPassword
);

// @route   POST /api/auth/logout
// @desc    Logout user
// @access  Private
router.post('/logout', authenticate, authController.logout);

// @route   POST /api/auth/change-password
// @desc    Change user password
// @access  Private
router.post('/change-password',
  authenticate,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('Password confirmation does not match new password');
        }
        return true;
      })
  ],
  authController.changePassword
);

// @route   POST /api/auth/google
// @desc    Google OAuth login
// @access  Public
router.post('/google', [
  body('credential')
    .notEmpty()
    .withMessage('Google credential is required')
], authController.googleLogin);



// @route   DELETE /api/auth/account
// @desc    Delete user account
// @access  Private
router.delete('/account',
  authenticate,
  [
    body('password')
      .notEmpty()
      .withMessage('Password is required to delete account')
  ],
  authController.deleteAccount
);

// @route   POST /api/auth/verify-age
// @desc    Verify user age for cannabis products
// @access  Private
router.post('/verify-age', authenticate, verifyAge, (req, res) => {
  res.json({
    success: true,
    message: 'Age verification successful',
    data: {
      ageVerified: true,
      user: {
        id: req.user.id,
        email: req.user.email,
        ageVerified: true
      }
    }
  });
});

// @route   GET /api/auth/me
// @desc    Get current user with detailed info
// @access  Private
router.get('/me', authenticate, authController.getProfile);

// @route   GET /api/auth/orders
// @desc    Get user order history
// @access  Private
router.get('/orders', authenticate, authController.getOrderHistory);

// @route   GET /api/auth/orders/:orderId
// @desc    Get specific order details
// @access  Private
router.get('/orders/:orderId', authenticate, authController.getOrderDetails);

// @route   PUT /api/auth/address
// @desc    Update user address
// @access  Private
router.put('/address', authenticate, authController.updateAddress);

// @route   GET /api/auth/addresses
// @desc    Get user addresses
// @access  Private
router.get('/addresses', authenticate, authController.getAddresses);

// @route   PUT /api/auth/change-password
// @desc    Change user password
// @access  Private
router.put('/change-password', authenticate, authController.changePassword);

// ============================================================================
// SOCIAL AUTHENTICATION ROUTES (Passport OAuth)
// ============================================================================

// @route   GET /api/auth/google/login
// @desc    Initiate Google OAuth login
// @access  Public
router.get('/google/login', passport.authenticate('google', {
  scope: ['profile', 'email']
}));

// @route   GET /api/auth/google/callback
// @desc    Google OAuth callback
// @access  Public
router.get('/google/callback',
  passport.authenticate('google', { failureRedirect: `${process.env.FRONTEND_URL}/login?error=google_auth_failed` }),
  (req, res) => {
    // Successful authentication
    const jwt = require('../utils/jwt');

    // Create proper payload objects for JWT tokens
    const tokenPayload = {
      id: req.user.id,
      email: req.user.email,
      role: req.user.Role ? req.user.Role.name : 'customer'
    };

    const refreshTokenPayload = {
      id: req.user.id,
      type: 'refresh'
    };

    const token = jwt.generateToken(tokenPayload);
    const refreshToken = jwt.generateRefreshToken(refreshTokenPayload);

    // Redirect to frontend with tokens
    res.redirect(`${process.env.FRONTEND_URL}/auth/callback?token=${token}&refreshToken=${refreshToken}&provider=google`);
  }
);

// ============================================================================
// SQUARE OAUTH ROUTES
// ============================================================================

// @route   GET /api/auth/square/login
// @desc    Initiate Square OAuth login
// @access  Public
router.get('/square/login', passport.authenticate('square', {
  scope: ['MERCHANT_PROFILE_READ', 'EMPLOYEES_READ']
}));

// @route   GET /api/auth/square/callback
// @desc    Square OAuth callback
// @access  Public
router.get('/square/callback',
  passport.authenticate('square', { failureRedirect: `${process.env.FRONTEND_URL}/login?error=square_auth_failed` }),
  (req, res) => {
    // Successful authentication
    const jwt = require('../utils/jwt');

    // Create proper payload objects for JWT tokens
    const tokenPayload = {
      id: req.user.id,
      email: req.user.email,
      role: req.user.Role ? req.user.Role.name : 'customer'
    };

    const refreshTokenPayload = {
      id: req.user.id,
      type: 'refresh'
    };

    const token = jwt.generateToken(tokenPayload);
    const refreshToken = jwt.generateRefreshToken(refreshTokenPayload);

    // Redirect to frontend with tokens
    res.redirect(`${process.env.FRONTEND_URL}/auth/callback?token=${token}&refreshToken=${refreshToken}&provider=square`);
  }
);



module.exports = router;
