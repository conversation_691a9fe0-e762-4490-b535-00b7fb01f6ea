const { User, Product, Order, Customer } = require('../models');
const { Op, sequelize } = require('sequelize');
const { sequelize: dbSequelize } = require('../models/database');
const squareService = require('../services/squareService');
const dataContextService = require('../services/dataContextService');

// Get dashboard statistics
const getDashboardStats = async (req, res) => {
  try {
    // Use data context service to get stats based on current mode
    const stats = await dataContextService.getDashboardStats(req);

    // Add data mode indicator to response
    const dataModeIndicator = dataContextService.getDataModeIndicator(req);

    // If in real mode, also get Square data and additional stats
    if (!dataModeIndicator.isMockData) {
      // Get low stock products
      const lowStockProducts = await Product.count({
        where: {
          quantity: { [Op.lte]: dbSequelize.col('lowStockThreshold') },
          trackQuantity: true
        }
      });

      // Sync with Square data
      let squareStats = {};
      try {
        squareStats = await squareService.getDashboardStats();
      } catch (error) {
        console.error('Square sync error:', error);
        squareStats = { squareRevenue: 0, squareOrders: 0 };
      }

      // Add additional real data stats
      stats.lowStockProducts = lowStockProducts;
      stats.squareRevenue = squareStats.squareRevenue || 0;
      stats.squareOrders = squareStats.squareOrders || 0;
    } else {
      // Mock data defaults
      stats.lowStockProducts = 2;
      stats.squareRevenue = 0;
      stats.squareOrders = 0;
    }

      revenueGrowth: 12.5, // TODO: Calculate actual growth
      orderGrowth: 8.3, // TODO: Calculate actual growth
      ...dataModeIndicator
    };

    res.json({
      success: true,
      data: {
        stats: finalStats,
        dataMode: dataModeIndicator
      }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    });
  }
};

// Sync inventory with Square
const syncInventoryWithSquare = async (req, res) => {
  try {
    const products = await Product.findAll({
      where: { status: 'active' }
    });

    const syncResults = [];

    for (const product of products) {
      try {
        // Get Square inventory for this product
        const squareInventory = await squareService.getInventoryCount(product.squareItemId);
        
        if (squareInventory !== null && squareInventory !== product.quantity) {
          // Update local inventory to match Square
          await product.update({ quantity: squareInventory });
          
          syncResults.push({
            productId: product.id,
            name: product.name,
            oldQuantity: product.quantity,
            newQuantity: squareInventory,
            status: 'updated'
          });
        } else {
          syncResults.push({
            productId: product.id,
            name: product.name,
            quantity: product.quantity,
            status: 'in_sync'
          });
        }
      } catch (error) {
        syncResults.push({
          productId: product.id,
          name: product.name,
          status: 'error',
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: 'Inventory sync completed',
      data: { syncResults }
    });

  } catch (error) {
    console.error('Inventory sync error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync inventory with Square',
      error: error.message
    });
  }
};

// Get customer analytics
const getCustomerAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { [Op.gte]: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
        break;
      case '30d':
        dateFilter = { [Op.gte]: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
        break;
      case '90d':
        dateFilter = { [Op.gte]: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) };
        break;
    }

    // Get customer registration stats
    const newCustomers = await User.count({
      where: {
        role: 'customer',
        createdAt: dateFilter
      }
    });

    // Get customer order stats
    const customerOrders = await Order.findAll({
      attributes: [
        'userId',
        [dbSequelize.fn('COUNT', dbSequelize.col('id')), 'orderCount'],
        [dbSequelize.fn('SUM', dbSequelize.col('total')), 'totalSpent']
      ],
      where: {
        createdAt: dateFilter
      },
      group: ['userId'],
      include: [{
        model: User,
        attributes: ['id', 'firstName', 'lastName', 'email']
      }]
    });

    // Customer segmentation
    const segments = {
      new: customerOrders.filter(c => c.orderCount == 1).length,
      returning: customerOrders.filter(c => c.orderCount > 1 && c.orderCount <= 5).length,
      loyal: customerOrders.filter(c => c.orderCount > 5).length,
      highValue: customerOrders.filter(c => parseFloat(c.totalSpent) > 500).length
    };

    res.json({
      success: true,
      data: {
        newCustomers,
        totalActiveCustomers: customerOrders.length,
        segments,
        topCustomers: customerOrders
          .sort((a, b) => parseFloat(b.totalSpent) - parseFloat(a.totalSpent))
          .slice(0, 10)
      }
    });

  } catch (error) {
    console.error('Customer analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer analytics',
      error: error.message
    });
  }
};

// Get all orders with advanced filtering
const getAllOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      paymentStatus,
      startDate,
      endDate,
      search
    } = req.query;

    const where = {};

    if (status) where.status = status;
    if (paymentStatus) where.paymentStatus = paymentStatus;

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt[Op.gte] = new Date(startDate);
      if (endDate) where.createdAt[Op.lte] = new Date(endDate);
    }

    if (search) {
      where[Op.or] = [
        { orderNumber: { [Op.like]: `%${search}%` } },
        { 'guestInfo.email': { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ],
      order: [['createdAt', 'DESC']],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit)
    });

    const totalPages = Math.ceil(count / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalOrders: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get all orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
};

// Update order status with tracking
const updateOrderStatusAdmin = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status, trackingNumber, notes } = req.body;

    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Update status history
    const statusHistory = order.statusHistory || [];
    statusHistory.push({
      status,
      timestamp: new Date(),
      note: notes || `Status updated to ${status}`,
      updatedBy: req.user.id
    });

    const updateData = {
      status,
      statusHistory
    };

    if (trackingNumber) {
      updateData.trackingNumber = trackingNumber;
    }

    if (status === 'shipped' && trackingNumber) {
      // Calculate estimated delivery (5-7 business days)
      const estimatedDelivery = new Date();
      estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);
      updateData.estimatedDelivery = estimatedDelivery;
    }

    await order.update(updateData);

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: order
    });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: error.message
    });
  }
};

// Get all products with advanced filtering
const getAllProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      category,
      lowStock,
      search
    } = req.query;

    const where = {};

    if (status) where.isActive = status === 'active';
    if (category) where.categoryId = category;
    if (lowStock === 'true') {
      where[Op.and] = [
        { trackQuantity: true },
        dbSequelize.where(
          dbSequelize.col('quantity'),
          Op.lte,
          dbSequelize.col('lowStockThreshold')
        )
      ];
    }

    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { sku: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows: products } = await Product.findAndCountAll({
      where,
      include: [
        {
          model: Category,
          attributes: ['id', 'name', 'slug']
        }
      ],
      order: [['createdAt', 'DESC']],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit)
    });

    const totalPages = Math.ceil(count / parseInt(limit));

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalProducts: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get all products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: error.message
    });
  }
};

// Bulk update product status
const bulkUpdateProducts = async (req, res) => {
  try {
    const { productIds, updates } = req.body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Product IDs array is required'
      });
    }

    const result = await Product.update(updates, {
      where: { id: { [Op.in]: productIds } }
    });

    res.json({
      success: true,
      message: `${result[0]} products updated successfully`,
      data: { updatedCount: result[0] }
    });

  } catch (error) {
    console.error('Bulk update products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update products',
      error: error.message
    });
  }
};

// Get user management data
const getUserManagement = async (req, res) => {
  try {
    const { page = 1, limit = 20, search, status } = req.query;

    const where = {};
    if (status) where.status = status;
    if (search) {
      where[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows: users } = await User.findAndCountAll({
      where,
      attributes: { exclude: ['password', 'verificationToken', 'resetPasswordToken', 'resetPasswordExpires'] },
      order: [['createdAt', 'DESC']],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit)
    });

    const totalPages = Math.ceil(count / parseInt(limit));

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalUsers: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get user management error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
};

module.exports = {
  getDashboardStats,
  syncInventoryWithSquare,
  getCustomerAnalytics,
  getAllOrders,
  updateOrderStatusAdmin,
  getAllProducts,
  bulkUpdateProducts,
  getUserManagement
};
