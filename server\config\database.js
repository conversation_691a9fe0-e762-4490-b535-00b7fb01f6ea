const { Sequelize } = require('sequelize');
const { dbLogger } = require('../utils/logger');
const { sqlLogging } = require('../middleware/debug');
require('dotenv').config();

/**
 * Database connection configuration for Nirvana Organics
 * Supports both production (Hostinger) and test environments
 */

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    timezone: '+00:00',
    logging: (() => {
      // Enable SQL logging based on environment variables
      const sqlLogConfig = sqlLogging();
      if (sqlLogConfig && sqlLogConfig.logging) {
        return sqlLogConfig.logging;
      }
      // Fallback to development logging
      return process.env.NODE_ENV === 'development' ? console.log : false;
    })(),
    pool: {
      max: parseInt(process.env.DB_POOL_MAX) || (process.env.NODE_ENV === 'test' ? 5 : 8),
      min: parseInt(process.env.DB_POOL_MIN) || 2,
      acquire: 30000,
      idle: 10000,
      evict: 1000,
      handleDisconnects: true
    },
    dialectOptions: {
      connectTimeout: 60000,
      acquireTimeout: 60000,
      timeout: 60000,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      supportBigNumbers: true,
      bigNumberStrings: true,
      ssl: process.env.DB_SSL === 'true' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    },
    retry: {
      max: 3
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      underscored: false
    }
  }
);

/**
 * Connect to database with retry logic
 */
const connectDB = async () => {
  const maxRetries = 5;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      await sequelize.authenticate();
      console.log('✅ Database connection established successfully');
      dbLogger.connection('connected', {
        host: process.env.DB_HOST,
        database: process.env.DB_NAME,
        attempt: retries + 1
      });

      // Sync models in development/test environment
      if (process.env.NODE_ENV !== 'production') {
        await sequelize.sync({ alter: false });
        console.log('✅ Database models synchronized');
      }

      return sequelize;
    } catch (error) {
      retries++;
      console.error(`❌ Database connection attempt ${retries} failed:`, error.message);
      dbLogger.connection('failed', {
        host: process.env.DB_HOST,
        database: process.env.DB_NAME,
        attempt: retries,
        error: error.message
      });
      
      if (retries >= maxRetries) {
        console.error('❌ Max database connection retries reached');
        throw error;
      }
      
      // Wait before retrying (exponential backoff)
      const delay = Math.pow(2, retries) * 1000;
      console.log(`⏳ Retrying database connection in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

/**
 * Close database connection
 */
const closeDB = async () => {
  try {
    await sequelize.close();
    console.log('✅ Database connection closed successfully');
  } catch (error) {
    console.error('❌ Error closing database connection:', error.message);
    throw error;
  }
};

/**
 * Test database connection
 */
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection test successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    return false;
  }
};

module.exports = {
  sequelize,
  connectDB,
  closeDB,
  testConnection
};
