const express = require('express');
const router = express.Router();
const guestController = require('../controllers/guestController');
const { body, query } = require('express-validator');

// Guest order validation
const validateGuestOrder = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('items.*.productId')
    .isInt({ min: 1 })
    .withMessage('Invalid product ID'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  body('guestInfo.email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('guestInfo.firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required'),
  body('guestInfo.lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required'),
  body('guestInfo.ageVerified')
    .isBoolean()
    .withMessage('Age verification is required'),
  body('billingAddress.firstName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Billing first name is required'),
  body('billingAddress.lastName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Billing last name is required'),
  body('billingAddress.address1')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Billing address is required'),
  body('billingAddress.city')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Billing city is required'),
  body('billingAddress.state')
    .trim()
    .isLength({ min: 2, max: 2 })
    .withMessage('Billing state is required'),
  body('billingAddress.zipCode')
    .trim()
    .isLength({ min: 5 })
    .withMessage('Billing ZIP code is required'),
  body('shippingAddress.firstName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Shipping first name is required'),
  body('shippingAddress.lastName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Shipping last name is required'),
  body('shippingAddress.address1')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Shipping address is required'),
  body('shippingAddress.city')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Shipping city is required'),
  body('shippingAddress.state')
    .trim()
    .isLength({ min: 2, max: 2 })
    .withMessage('Shipping state is required'),
  body('shippingAddress.zipCode')
    .trim()
    .isLength({ min: 5 })
    .withMessage('Shipping ZIP code is required'),
  body('paymentMethod')
    .isIn(['square', 'paypal'])
    .withMessage('Invalid payment method')
];

// Guest order tracking validation
const validateGuestTracking = [
  query('orderNumber')
    .notEmpty()
    .withMessage('Order number is required'),
  query('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  query('trackingToken')
    .optional()
    .isLength({ min: 32 })
    .withMessage('Invalid tracking token')
];

// @route   POST /api/guest/orders
// @desc    Create guest order (checkout)
// @access  Public
router.post('/orders', validateGuestOrder, guestController.createGuestOrder);

// @route   GET /api/guest/orders/track
// @desc    Track guest order
// @access  Public
router.get('/orders/track', validateGuestTracking, guestController.trackGuestOrder);

module.exports = router;
