#!/usr/bin/env node

/**
 * Production Security Validation Script
 * Validates security configurations and settings
 */

const fs = require('fs');
const path = require('path');

const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`)
};

function validateSecurity() {
  log.info('Running production security validation...');

  let issues = 0;

  // Check environment file
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');

    if (envContent.includes('CHANGE_THIS')) {
      log.error('Environment file contains default values that must be changed');
      issues++;
    }

    if (!envContent.includes('NODE_ENV=production')) {
      log.error('NODE_ENV is not set to production');
      issues++;
    }

    log.success('Environment file exists');
  } else {
    log.error('Environment file (.env) not found');
    issues++;
  }

  // Check file permissions
  try {
    const stats = fs.statSync('.env');
    const mode = stats.mode & parseInt('777', 8);
    if (mode !== parseInt('600', 8)) {
      log.warning('Environment file permissions should be 600');
    } else {
      log.success('Environment file permissions are secure');
    }
  } catch (error) {
    log.error('Could not check environment file permissions');
    issues++;
  }

  // Check SSL certificates
  if (fs.existsSync('/etc/ssl/certs/shopnirvanaorganics.com.crt')) {
    log.success('SSL certificate found');
  } else {
    log.warning('SSL certificate not found - configure SSL before going live');
  }

  if (issues === 0) {
    log.success('Security validation passed');
    process.exit(0);
  } else {
    log.error(`Security validation failed with ${issues} issues`);
    process.exit(1);
  }
}

validateSecurity();