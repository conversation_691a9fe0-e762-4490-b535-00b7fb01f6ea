const nodemailer = require('nodemailer');
const models = require('../models');
const fs = require('fs').promises;
const path = require('path');
const handlebars = require('handlebars');

// Register Handlebars helpers
handlebars.registerHelper('eq', function(a, b) {
  return a === b;
});

handlebars.registerHelper('gt', function(a, b) {
  return a > b;
});

handlebars.registerHelper('lt', function(a, b) {
  return a < b;
});

handlebars.registerHelper('formatCurrency', function(amount) {
  return parseFloat(amount || 0).toFixed(2);
});

handlebars.registerHelper('formatDate', function(date) {
  return new Date(date).toLocaleDateString();
});

/**
 * Email Notification Service
 * Handles automated email notifications for orders, confirmations, and admin alerts
 */
class EmailNotificationService {
  constructor() {
    this.transporter = null;
    this.templates = new Map();
    this.emailQueue = [];
    this.retryAttempts = 3;
    this.retryDelay = 5000; // 5 seconds
    
    this.initializeTransporter();
    this.loadEmailTemplates();
    this.startEmailProcessor();
  }

  /**
   * Initialize email transporter
   */
  initializeTransporter() {
    // For test environment, use a mock transporter
    if (process.env.NODE_ENV === 'test') {
      this.transporter = nodemailer.createTransport({
        streamTransport: true,
        newline: 'unix',
        buffer: true
      });
      console.log('✅ Mock email transporter initialized for testing');
      return;
    }

    // For production/development, use real SMTP
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || process.env.EMAIL_HOST || 'smtp.hostinger.com',
      port: process.env.SMTP_PORT || process.env.EMAIL_PORT || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER || process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.SMTP_PASS || process.env.EMAIL_PASS
      },
      pool: true, // Use connection pooling
      maxConnections: 5,
      maxMessages: 100,
      rateDelta: 1000, // 1 second
      rateLimit: 5 // max 5 emails per second
    });

    // Only verify in non-test environments
    if (process.env.NODE_ENV !== 'test') {
      this.transporter.verify((error, success) => {
        if (error) {
          console.error('Email transporter verification failed:', error);
        } else {
          console.log('Email transporter is ready to send messages');
        }
      });
    }
  }

  /**
   * Load email templates
   */
  async loadEmailTemplates() {
    try {
      const templatesDir = path.join(__dirname, '../templates/email');
      
      // Load order confirmation template
      const orderConfirmationHtml = await fs.readFile(
        path.join(templatesDir, 'order-confirmation.html'), 
        'utf8'
      );
      this.templates.set('order-confirmation', handlebars.compile(orderConfirmationHtml));

      // Load admin notification template
      const adminNotificationHtml = await fs.readFile(
        path.join(templatesDir, 'admin-order-notification.html'), 
        'utf8'
      );
      this.templates.set('admin-notification', handlebars.compile(adminNotificationHtml));

      // Load order status update template
      const statusUpdateHtml = await fs.readFile(
        path.join(templatesDir, 'order-status-update.html'), 
        'utf8'
      );
      this.templates.set('status-update', handlebars.compile(statusUpdateHtml));

      console.log('Email templates loaded successfully');
    } catch (error) {
      console.error('Error loading email templates:', error);
      // Create fallback templates
      this.createFallbackTemplates();
    }
  }

  /**
   * Create fallback templates if files are not found
   */
  createFallbackTemplates() {
    // Simple fallback templates
    this.templates.set('order-confirmation', handlebars.compile(`
      <h2>Order Confirmation - {{orderNumber}}</h2>
      <p>Dear {{customerName}},</p>
      <p>Thank you for your order! Your order has been received and is being processed.</p>
      <p><strong>Order Details:</strong></p>
      <ul>
        {{#each items}}
        <li>{{productName}} - Quantity: {{quantity}} - ${{total}}</li>
        {{/each}}
      </ul>
      <p><strong>Total: ${{orderTotal}}</strong></p>
      <p>Estimated delivery: {{estimatedDelivery}}</p>
      <p>For questions, contact us at ${process.env.EMAIL_SUPPORT || '<EMAIL>'}</p>
    `));

    this.templates.set('admin-notification', handlebars.compile(`
      <h2>New Order Received - {{orderNumber}}</h2>
      <p><strong>Customer:</strong> {{customerName}} ({{customerEmail}})</p>
      <p><strong>Order Total:</strong> ${{orderTotal}}</p>
      <p><strong>Items:</strong></p>
      <ul>
        {{#each items}}
        <li>{{productName}} ({{sku}}) - Qty: {{quantity}} - ${{total}}</li>
        {{/each}}
      </ul>
      <p><strong>Shipping Address:</strong><br>{{shippingAddress}}</p>
      <p><strong>Payment Status:</strong> {{paymentStatus}}</p>
    `));
  }

  /**
   * Send order confirmation email to customer
   */
  async sendOrderConfirmation(orderData) {
    try {
      const template = this.templates.get('order-confirmation');
      if (!template) {
        throw new Error('Order confirmation template not found');
      }

      const templateData = {
        orderNumber: orderData.orderNumber,
        customerName: orderData.customer.name,
        customerEmail: orderData.customer.email,
        items: orderData.items.map(item => ({
          productName: item.productName,
          sku: item.sku,
          quantity: item.quantity,
          price: parseFloat(item.price || 0).toFixed(2),
          total: (item.quantity * parseFloat(item.price || 0)).toFixed(2)
        })),
        orderTotal: parseFloat(orderData.pricing.total || 0).toFixed(2),
        subtotal: parseFloat(orderData.pricing.subtotal || 0).toFixed(2),
        tax: parseFloat(orderData.pricing.tax || 0).toFixed(2),
        shipping: parseFloat(orderData.pricing.shipping || 0).toFixed(2),
        discount: parseFloat(orderData.pricing.discount || 0).toFixed(2),
        estimatedDelivery: new Date(orderData.estimatedDelivery).toLocaleDateString(),
        shippingMethod: orderData.shipping.method,
        shippingAddress: this.formatAddress(orderData.shipping.address),
        supportEmail: '<EMAIL>',
        companyName: 'Nirvana Organics',
        orderDate: new Date(orderData.createdAt).toLocaleDateString()
      };

      const htmlContent = template(templateData);

      const emailOptions = {
        from: `"Nirvana Organics" <${process.env.SMTP_USER}>`,
        to: orderData.customer.email,
        subject: `Order Confirmation - ${orderData.orderNumber}`,
        html: htmlContent,
        text: this.generatePlainTextConfirmation(templateData)
      };

      await this.queueEmail(emailOptions, 'order-confirmation', orderData.id);
      
      // Log email activity
      await this.logEmailActivity({
        orderId: orderData.id,
        emailType: 'order-confirmation',
        recipient: orderData.customer.email,
        status: 'queued'
      });

      console.log(`Order confirmation email queued for order: ${orderData.orderNumber}`);
      
    } catch (error) {
      console.error('Error sending order confirmation:', error);
      throw error;
    }
  }

  /**
   * Send admin notification email for new orders
   */
  async sendAdminOrderNotification(orderData) {
    try {
      const template = this.templates.get('admin-notification');
      if (!template) {
        throw new Error('Admin notification template not found');
      }

      // Get admin notification emails
      const adminEmails = await this.getAdminNotificationEmails();

      const templateData = {
        orderNumber: orderData.orderNumber,
        customerName: orderData.customer.name,
        customerEmail: orderData.customer.email,
        customerMembership: orderData.customer.membershipType,
        items: orderData.items.map(item => ({
          productName: item.productName,
          sku: item.sku,
          quantity: item.quantity,
          price: parseFloat(item.price || 0).toFixed(2),
          total: (item.quantity * parseFloat(item.price || 0)).toFixed(2)
        })),
        orderTotal: parseFloat(orderData.pricing.total || 0).toFixed(2),
        subtotal: parseFloat(orderData.pricing.subtotal || 0).toFixed(2),
        tax: parseFloat(orderData.pricing.tax || 0).toFixed(2),
        shipping: parseFloat(orderData.pricing.shipping || 0).toFixed(2),
        discount: parseFloat(orderData.pricing.discount || 0).toFixed(2),
        shippingAddress: this.formatAddress(orderData.shipping.address),
        shippingMethod: orderData.shipping.method,
        paymentStatus: orderData.payment.status,
        paymentMethod: orderData.payment.method,
        transactionId: orderData.payment.transactionId,
        orderDate: new Date(orderData.createdAt).toLocaleString(),
        priority: orderData.priority,
        couponsUsed: orderData.coupons.map(coupon => `${coupon.code} (-$${parseFloat(coupon.discount || 0).toFixed(2)})`).join(', ') || 'None'
      };

      const htmlContent = template(templateData);

      // Send to all admin emails
      for (const adminEmail of adminEmails) {
        const emailOptions = {
          from: `"Nirvana Organics Orders" <${process.env.SMTP_USER}>`,
          to: adminEmail,
          subject: `🛒 New Order Alert - ${orderData.orderNumber} - $${parseFloat(orderData.pricing.total || 0).toFixed(2)}`,
          html: htmlContent,
          text: this.generatePlainTextAdminNotification(templateData),
          priority: orderData.priority === 'high' ? 'high' : 'normal'
        };

        await this.queueEmail(emailOptions, 'admin-notification', orderData.id);
      }

      // Log email activity
      await this.logEmailActivity({
        orderId: orderData.id,
        emailType: 'admin-notification',
        recipient: adminEmails.join(', '),
        status: 'queued'
      });

      console.log(`Admin notification emails queued for order: ${orderData.orderNumber}`);
      
    } catch (error) {
      console.error('Error sending admin notification:', error);
      throw error;
    }
  }

  /**
   * Send order status update email
   */
  async sendOrderStatusUpdate(orderData, newStatus, trackingInfo = null) {
    try {
      const template = this.templates.get('status-update');
      if (!template) {
        throw new Error('Status update template not found');
      }

      const statusMessages = {
        processing: 'Your order is being processed and will be shipped soon.',
        shipped: 'Your order has been shipped and is on its way to you.',
        delivered: 'Your order has been delivered. Thank you for your business!',
        cancelled: 'Your order has been cancelled. If you have questions, please contact us.'
      };

      const templateData = {
        orderNumber: orderData.orderNumber,
        customerName: orderData.customer.name,
        newStatus: newStatus,
        statusMessage: statusMessages[newStatus] || 'Your order status has been updated.',
        trackingNumber: trackingInfo?.trackingNumber || null,
        trackingUrl: trackingInfo?.trackingUrl || null,
        estimatedDelivery: orderData.estimatedDelivery ? new Date(orderData.estimatedDelivery).toLocaleDateString() : null,
        supportEmail: process.env.EMAIL_SUPPORT || '<EMAIL>',
        companyName: 'Nirvana Organics'
      };

      const htmlContent = template(templateData);

      const emailOptions = {
        from: `"Nirvana Organics" <${process.env.SMTP_USER}>`,
        to: orderData.customer.email,
        subject: `Order Update - ${orderData.orderNumber} - ${newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}`,
        html: htmlContent,
        text: this.generatePlainTextStatusUpdate(templateData)
      };

      await this.queueEmail(emailOptions, 'status-update', orderData.id);
      
      // Log email activity
      await this.logEmailActivity({
        orderId: orderData.id,
        emailType: 'status-update',
        recipient: orderData.customer.email,
        status: 'queued',
        metadata: { newStatus, trackingInfo }
      });

      console.log(`Status update email queued for order: ${orderData.orderNumber}`);
      
    } catch (error) {
      console.error('Error sending status update:', error);
      throw error;
    }
  }

  /**
   * Queue email for processing
   */
  async queueEmail(emailOptions, emailType, orderId) {
    const emailJob = {
      id: Date.now() + Math.random(),
      emailOptions,
      emailType,
      orderId,
      attempts: 0,
      maxAttempts: this.retryAttempts,
      createdAt: new Date(),
      status: 'queued'
    };

    this.emailQueue.push(emailJob);
    console.log(`Email queued: ${emailType} for order ${orderId}`);
  }

  /**
   * Process email queue
   */
  startEmailProcessor() {
    setInterval(async () => {
      if (this.emailQueue.length > 0) {
        const emailJob = this.emailQueue.shift();
        await this.processEmailJob(emailJob);
      }
    }, 1000); // Process every second

    console.log('Email processor started');
  }

  /**
   * Process individual email job
   */
  async processEmailJob(emailJob) {
    try {
      emailJob.attempts++;
      emailJob.status = 'sending';

      const info = await this.transporter.sendMail(emailJob.emailOptions);
      
      emailJob.status = 'sent';
      emailJob.messageId = info.messageId;
      emailJob.sentAt = new Date();

      // Update email log
      await this.updateEmailLog(emailJob.orderId, emailJob.emailType, 'sent', {
        messageId: info.messageId,
        response: info.response
      });

      console.log(`Email sent successfully: ${emailJob.emailType} for order ${emailJob.orderId}`);
      
    } catch (error) {
      console.error(`Email sending failed: ${emailJob.emailType} for order ${emailJob.orderId}`, error);
      
      emailJob.status = 'failed';
      emailJob.error = error.message;

      // Retry if attempts remaining
      if (emailJob.attempts < emailJob.maxAttempts) {
        emailJob.status = 'retry';
        setTimeout(() => {
          this.emailQueue.push(emailJob);
        }, this.retryDelay * emailJob.attempts); // Exponential backoff
        
        console.log(`Email retry scheduled: ${emailJob.emailType} for order ${emailJob.orderId} (attempt ${emailJob.attempts})`);
      } else {
        // Update email log with failure
        await this.updateEmailLog(emailJob.orderId, emailJob.emailType, 'failed', {
          error: error.message,
          attempts: emailJob.attempts
        });
        
        console.error(`Email failed permanently: ${emailJob.emailType} for order ${emailJob.orderId}`);
      }
    }
  }

  /**
   * Get admin notification email addresses
   */
  async getAdminNotificationEmails() {
    try {
      // Get from database settings or use default
      const settings = await models.SystemSetting?.findOne({
        where: { key: 'admin_notification_emails' }
      });

      if (settings && settings.value) {
        return JSON.parse(settings.value);
      }

      // Default admin emails
      return [
        '<EMAIL>',
        process.env.EMAIL_SUPPORT || '<EMAIL>',
        // Add more admin emails as needed
      ];
      
    } catch (error) {
      console.error('Error getting admin emails:', error);
      return ['<EMAIL>'];
    }
  }

  /**
   * Format address for display
   */
  formatAddress(address) {
    return `${address.street}\n${address.city}, ${address.state} ${address.zipCode}\n${address.country}`;
  }

  /**
   * Generate plain text confirmation email
   */
  generatePlainTextConfirmation(data) {
    return `
Order Confirmation - ${data.orderNumber}

Dear ${data.customerName},

Thank you for your order! Your order has been received and is being processed.

Order Details:
${data.items.map(item => `- ${item.productName} (${item.sku}) - Quantity: ${item.quantity} - $${item.total}`).join('\n')}

Subtotal: $${data.subtotal}
Tax: $${data.tax}
Shipping: $${data.shipping}
${data.discount > 0 ? `Discount: -$${data.discount}\n` : ''}Total: $${data.orderTotal}

Shipping Method: ${data.shippingMethod}
Estimated Delivery: ${data.estimatedDelivery}

Shipping Address:
${data.shippingAddress}

For questions about your order, please contact us at ${data.supportEmail}

Thank you for choosing ${data.companyName}!
    `.trim();
  }

  /**
   * Generate plain text admin notification
   */
  generatePlainTextAdminNotification(data) {
    return `
New Order Received - ${data.orderNumber}

Customer: ${data.customerName} (${data.customerEmail})
Membership: ${data.customerMembership}
Order Total: $${data.orderTotal}
Priority: ${data.priority}

Items:
${data.items.map(item => `- ${item.productName} (${item.sku}) - Qty: ${item.quantity} - $${item.total}`).join('\n')}

Pricing:
Subtotal: $${data.subtotal}
Tax: $${data.tax}
Shipping: $${data.shipping}
${data.discount > 0 ? `Discount: -$${data.discount}\n` : ''}Total: $${data.orderTotal}

Shipping:
Method: ${data.shippingMethod}
Address: ${data.shippingAddress}

Payment:
Status: ${data.paymentStatus}
Method: ${data.paymentMethod}
Transaction ID: ${data.transactionId}

Coupons Used: ${data.couponsUsed}

Order Date: ${data.orderDate}
    `.trim();
  }

  /**
   * Generate plain text status update
   */
  generatePlainTextStatusUpdate(data) {
    return `
Order Update - ${data.orderNumber}

Dear ${data.customerName},

${data.statusMessage}

Order Status: ${data.newStatus}
${data.trackingNumber ? `Tracking Number: ${data.trackingNumber}\n` : ''}${data.trackingUrl ? `Track Your Order: ${data.trackingUrl}\n` : ''}${data.estimatedDelivery ? `Estimated Delivery: ${data.estimatedDelivery}\n` : ''}
For questions, please contact us at ${data.supportEmail}

Thank you for choosing ${data.companyName}!
    `.trim();
  }

  /**
   * Log email activity
   */
  async logEmailActivity(logData) {
    try {
      if (models.EmailLog) {
        await models.EmailLog.create({
          orderId: logData.orderId,
          emailType: logData.emailType,
          recipient: logData.recipient,
          status: logData.status,
          metadata: logData.metadata ? JSON.stringify(logData.metadata) : null,
          createdAt: new Date()
        });
      }
    } catch (error) {
      console.error('Error logging email activity:', error);
    }
  }

  /**
   * Update email log status
   */
  async updateEmailLog(orderId, emailType, status, metadata = null) {
    try {
      if (models.EmailLog) {
        await models.EmailLog.update(
          { 
            status, 
            metadata: metadata ? JSON.stringify(metadata) : null,
            updatedAt: new Date()
          },
          { 
            where: { 
              orderId, 
              emailType,
              status: { [require('sequelize').Op.in]: ['queued', 'sending', 'retry'] }
            }
          }
        );
      }
    } catch (error) {
      console.error('Error updating email log:', error);
    }
  }

  /**
   * Get email delivery statistics
   */
  async getEmailStats(dateRange = {}) {
    try {
      if (!models.EmailLog) {
        return { sent: 0, failed: 0, pending: 0 };
      }

      const whereClause = {};
      if (dateRange.startDate) {
        whereClause.createdAt = { [models.Sequelize.Op.gte]: new Date(dateRange.startDate) };
      }
      if (dateRange.endDate) {
        whereClause.createdAt = { 
          ...whereClause.createdAt,
          [models.Sequelize.Op.lte]: new Date(dateRange.endDate) 
        };
      }

      const stats = await models.EmailLog.findAll({
        attributes: [
          'status',
          [models.Sequelize.fn('COUNT', models.Sequelize.col('id')), 'count']
        ],
        where: whereClause,
        group: ['status'],
        raw: true
      });

      const result = { sent: 0, failed: 0, pending: 0 };
      stats.forEach(stat => {
        if (stat.status === 'sent') result.sent = parseInt(stat.count);
        else if (stat.status === 'failed') result.failed = parseInt(stat.count);
        else result.pending += parseInt(stat.count);
      });

      return result;
    } catch (error) {
      console.error('Error getting email stats:', error);
      return { sent: 0, failed: 0, pending: 0 };
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfiguration() {
    try {
      const testEmail = {
        from: `"Nirvana Organics Test" <${process.env.SMTP_USER}>`,
        to: process.env.SMTP_USER,
        subject: 'Email Configuration Test',
        text: 'This is a test email to verify the email configuration is working correctly.',
        html: '<p>This is a test email to verify the email configuration is working correctly.</p>'
      };

      const info = await this.transporter.sendMail(testEmail);
      console.log('Test email sent successfully:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Test email failed:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = new EmailNotificationService();
