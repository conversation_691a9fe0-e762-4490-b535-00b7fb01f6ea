#!/usr/bin/env node

/**
 * Test Script for Unified Build Structure
 * 
 * This script validates that the unified build is properly structured
 * and both applications are accessible.
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(60));
    console.log(`🧪 ${msg}`);
    console.log('='.repeat(60));
  }
};

function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    log.success(`${description}: ${filePath}`);
    return true;
  } else {
    log.error(`${description} not found: ${filePath}`);
    return false;
  }
}

function checkDirectoryStructure() {
  log.header('Checking Unified Build Structure');
  
  const checks = [
    { path: 'dist', desc: 'Main dist directory' },
    { path: 'dist/index.html', desc: 'Main frontend entry point' },
    { path: 'dist/assets', desc: 'Main frontend assets directory' },
    { path: 'dist/admin', desc: 'Admin panel directory' },
    { path: 'dist/admin/admin.html', desc: 'Admin panel entry point' },
    { path: 'dist/admin/assets', desc: 'Admin panel assets directory' },
    { path: 'dist/routing-config.json', desc: 'Routing configuration' }
  ];
  
  let allPassed = true;
  
  checks.forEach(check => {
    if (!checkFileExists(check.path, check.desc)) {
      allPassed = false;
    }
  });
  
  return allPassed;
}

function analyzeAssets() {
  log.header('Analyzing Build Assets');
  
  // Check main frontend assets
  const mainAssetsDir = 'dist/assets';
  if (fs.existsSync(mainAssetsDir)) {
    const mainAssets = fs.readdirSync(mainAssetsDir);
    log.info(`Main frontend assets: ${mainAssets.length} files`);
    mainAssets.forEach(asset => {
      const size = fs.statSync(path.join(mainAssetsDir, asset)).size;
      log.info(`  - ${asset} (${(size / 1024).toFixed(2)} KB)`);
    });
  }
  
  // Check admin panel assets
  const adminAssetsDir = 'dist/admin/assets';
  if (fs.existsSync(adminAssetsDir)) {
    const adminAssets = fs.readdirSync(adminAssetsDir);
    log.info(`Admin panel assets: ${adminAssets.length} files`);
    adminAssets.forEach(asset => {
      const size = fs.statSync(path.join(adminAssetsDir, asset)).size;
      log.info(`  - ${asset} (${(size / 1024).toFixed(2)} KB)`);
    });
  }
}

function checkHtmlFiles() {
  log.header('Validating HTML Files');
  
  // Check main index.html
  const mainHtml = 'dist/index.html';
  if (fs.existsSync(mainHtml)) {
    const content = fs.readFileSync(mainHtml, 'utf8');
    if (content.includes('<div id="root">')) {
      log.success('Main frontend HTML has React root element');
    } else {
      log.error('Main frontend HTML missing React root element');
    }
    
    if (content.includes('/src/main.tsx')) {
      log.success('Main frontend HTML references correct entry point');
    } else {
      log.warning('Main frontend HTML may have incorrect entry point reference');
    }
  }
  
  // Check admin.html
  const adminHtml = 'dist/admin/admin.html';
  if (fs.existsSync(adminHtml)) {
    const content = fs.readFileSync(adminHtml, 'utf8');
    if (content.includes('<div id="root">')) {
      log.success('Admin panel HTML has React root element');
    } else {
      log.error('Admin panel HTML missing React root element');
    }
    
    if (content.includes('admin-main.tsx') || content.includes('admin')) {
      log.success('Admin panel HTML references admin-specific entry point');
    } else {
      log.warning('Admin panel HTML may have incorrect entry point reference');
    }
  }
}

function checkRoutingConfig() {
  log.header('Validating Routing Configuration');
  
  const routingConfigPath = 'dist/routing-config.json';
  if (fs.existsSync(routingConfigPath)) {
    try {
      const config = JSON.parse(fs.readFileSync(routingConfigPath, 'utf8'));
      
      if (config.main && config.main.path === '/' && config.main.entry === 'index.html') {
        log.success('Main frontend routing configuration is correct');
      } else {
        log.error('Main frontend routing configuration is incorrect');
      }
      
      if (config.admin && config.admin.path === '/admin' && config.admin.entry === 'admin/admin.html') {
        log.success('Admin panel routing configuration is correct');
      } else {
        log.error('Admin panel routing configuration is incorrect');
      }
      
    } catch (error) {
      log.error(`Failed to parse routing configuration: ${error.message}`);
    }
  }
}

function calculateBuildSize() {
  log.header('Build Size Analysis');
  
  function getDirSize(dirPath) {
    let size = 0;
    if (!fs.existsSync(dirPath)) return 0;
    
    const files = fs.readdirSync(dirPath);
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        size += getDirSize(filePath);
      } else {
        size += stats.size;
      }
    }
    return size;
  }
  
  const totalSize = getDirSize('dist');
  const mainSize = getDirSize('dist') - getDirSize('dist/admin');
  const adminSize = getDirSize('dist/admin');
  
  log.info(`Total build size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
  log.info(`Main frontend size: ${(mainSize / 1024 / 1024).toFixed(2)} MB`);
  log.info(`Admin panel size: ${(adminSize / 1024 / 1024).toFixed(2)} MB`);
}

async function runTests() {
  try {
    log.header('Starting Unified Build Tests');
    
    // Check if build exists
    if (!fs.existsSync('dist')) {
      log.error('Unified build not found. Run "npm run build:unified" first.');
      process.exit(1);
    }
    
    // Run all tests
    const structureValid = checkDirectoryStructure();
    analyzeAssets();
    checkHtmlFiles();
    checkRoutingConfig();
    calculateBuildSize();
    
    // Summary
    log.header('Test Summary');
    if (structureValid) {
      log.success('All unified build structure tests passed!');
      log.info('The build is ready for deployment.');
      log.info('Main frontend accessible at: /');
      log.info('Admin panel accessible at: /admin');
    } else {
      log.error('Some tests failed. Please check the build configuration.');
      process.exit(1);
    }
    
  } catch (error) {
    log.error(`Test failed: ${error.message}`);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
