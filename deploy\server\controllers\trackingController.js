const { Order, User } = require('../models');
const uspsService = require('../services/uspsService');
const whatsappService = require('../services/whatsappService');
const emailNotificationService = require('../services/emailNotificationService');
const realTimeService = require('../services/realTimeService');

/**
 * Add tracking number to order
 */
const addTrackingNumber = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { trackingNumber, carrier = 'USPS', notes } = req.body;

    // Validate tracking number format
    if (carrier === 'USPS' && !uspsService.validateTrackingNumber(trackingNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid USPS tracking number format'
      });
    }

    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Update order with tracking information
    await order.update({
      trackingNumber: trackingNumber,
      shippingCarrier: carrier,
      status: 'shipped',
      shippedAt: new Date(),
      trackingNotes: notes
    });

    // Get initial tracking information
    let trackingInfo = null;
    if (carrier === 'USPS') {
      trackingInfo = await uspsService.trackPackage(trackingNumber);
    }

    // Send notifications
    try {
      const orderData = await realTimeService.enrichOrderData(order);
      
      // Send email notification
      await emailNotificationService.sendStatusUpdate(orderData, 'shipped', { 
        trackingNumber,
        trackingUrl: `https://tools.usps.com/go/TrackConfirmAction?tLabels=${trackingNumber}`
      });
      
      // Send WhatsApp notification
      if (order.user?.phone) {
        await whatsappService.sendShippingNotification(order.user.phone, orderData, trackingNumber);
      }
      
      console.log(`Shipping notifications sent for order: ${order.orderNumber}`);
    } catch (notificationError) {
      console.error('Failed to send shipping notifications:', notificationError);
    }

    res.json({
      success: true,
      message: 'Tracking number added successfully',
      data: {
        order: order,
        trackingInfo: trackingInfo
      }
    });

  } catch (error) {
    console.error('Add tracking number error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add tracking number',
      error: error.message
    });
  }
};

/**
 * Update tracking status for order
 */
const updateTrackingStatus = async (req, res) => {
  try {
    const { orderId } = req.params;

    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (!order.trackingNumber) {
      return res.status(400).json({
        success: false,
        message: 'No tracking number found for this order'
      });
    }

    // Get updated tracking information
    let trackingInfo = null;
    if (order.shippingCarrier === 'USPS') {
      trackingInfo = await uspsService.trackPackage(order.trackingNumber);
    }

    if (trackingInfo && trackingInfo.success) {
      // Update order status based on tracking info
      let newStatus = order.status;
      
      switch (trackingInfo.status) {
        case 'delivered':
          newStatus = 'delivered';
          break;
        case 'out_for_delivery':
          newStatus = 'shipped'; // Keep as shipped, but we know it's out for delivery
          break;
        case 'exception':
          // Don't change status automatically for exceptions
          break;
      }

      // Update order if status changed
      if (newStatus !== order.status) {
        await order.update({
          status: newStatus,
          deliveredAt: trackingInfo.status === 'delivered' ? new Date() : null,
          trackingLastUpdated: new Date()
        });

        // Send delivery confirmation if delivered
        if (newStatus === 'delivered') {
          try {
            const orderData = await realTimeService.enrichOrderData(order);
            
            if (order.user?.phone) {
              await whatsappService.sendDeliveryConfirmation(order.user.phone, orderData);
            }
            
            await emailNotificationService.sendStatusUpdate(orderData, 'delivered');
            
            console.log(`Delivery confirmation sent for order: ${order.orderNumber}`);
          } catch (notificationError) {
            console.error('Failed to send delivery confirmation:', notificationError);
          }
        }
      } else {
        // Update last tracking check time
        await order.update({
          trackingLastUpdated: new Date()
        });
      }
    }

    res.json({
      success: true,
      message: 'Tracking status updated successfully',
      data: {
        order: order,
        trackingInfo: trackingInfo
      }
    });

  } catch (error) {
    console.error('Update tracking status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update tracking status',
      error: error.message
    });
  }
};

/**
 * Get tracking information for order
 */
const getTrackingInfo = async (req, res) => {
  try {
    const { orderId } = req.params;

    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (!order.trackingNumber) {
      return res.status(404).json({
        success: false,
        message: 'No tracking number found for this order'
      });
    }

    // Get tracking information
    let trackingInfo = null;
    if (order.shippingCarrier === 'USPS') {
      trackingInfo = await uspsService.trackPackage(order.trackingNumber);
    }

    res.json({
      success: true,
      data: {
        orderId: order.id,
        orderNumber: order.orderNumber,
        trackingNumber: order.trackingNumber,
        carrier: order.shippingCarrier,
        status: order.status,
        trackingInfo: trackingInfo
      }
    });

  } catch (error) {
    console.error('Get tracking info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tracking information',
      error: error.message
    });
  }
};

/**
 * Batch update tracking for multiple orders
 */
const batchUpdateTracking = async (req, res) => {
  try {
    const orders = await Order.findAll({
      where: {
        status: 'shipped',
        trackingNumber: { [require('sequelize').Op.ne]: null },
        shippingCarrier: 'USPS'
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ]
    });

    const trackingNumbers = orders.map(order => order.trackingNumber);
    const trackingResults = await uspsService.batchTrackPackages(trackingNumbers);

    const updates = [];
    const notifications = [];

    for (let i = 0; i < orders.length; i++) {
      const order = orders[i];
      const trackingResult = trackingResults[i];

      if (trackingResult && trackingResult.success) {
        let newStatus = order.status;
        
        if (trackingResult.status === 'delivered' && order.status !== 'delivered') {
          newStatus = 'delivered';
          
          // Queue delivery notification
          notifications.push({
            order: order,
            type: 'delivered'
          });
        }

        if (newStatus !== order.status) {
          updates.push({
            orderId: order.id,
            status: newStatus,
            deliveredAt: trackingResult.status === 'delivered' ? new Date() : null,
            trackingLastUpdated: new Date()
          });
        }
      }
    }

    // Perform batch updates
    for (const update of updates) {
      await Order.update(
        {
          status: update.status,
          deliveredAt: update.deliveredAt,
          trackingLastUpdated: update.trackingLastUpdated
        },
        { where: { id: update.orderId } }
      );
    }

    // Send notifications
    for (const notification of notifications) {
      try {
        const orderData = await realTimeService.enrichOrderData(notification.order);
        
        if (notification.order.user?.phone) {
          await whatsappService.sendDeliveryConfirmation(notification.order.user.phone, orderData);
        }
        
        await emailNotificationService.sendStatusUpdate(orderData, 'delivered');
      } catch (notificationError) {
        console.error(`Failed to send notification for order ${notification.order.orderNumber}:`, notificationError);
      }
    }

    res.json({
      success: true,
      message: `Batch tracking update completed. ${updates.length} orders updated, ${notifications.length} notifications sent.`,
      data: {
        updatedOrders: updates.length,
        notificationsSent: notifications.length
      }
    });

  } catch (error) {
    console.error('Batch update tracking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch update tracking',
      error: error.message
    });
  }
};

module.exports = {
  addTrackingNumber,
  updateTrackingStatus,
  getTrackingInfo,
  batchUpdateTracking
};
