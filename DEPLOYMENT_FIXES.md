# Nirvana Organics Deployment Issues - Critical Fixes

This document provides step-by-step solutions for the two critical errors preventing the Nirvana Organics application from starting.

## 🚨 Issue Analysis

### Issue 1: Missing `node-cron` Module
**Root Cause**: The `deploy/package.json` file is missing the `node-cron` dependency, but the application code requires it in `socialMediaScheduler.js`.

**Evidence**: 
- `server/package.json` includes `"node-cron": "^3.0.3"` ✅
- `deploy/package.json` is missing `node-cron` ❌
- `socialMediaScheduler.js` requires `node-cron` on line 1

### Issue 2: SSL Certificate Path Error
**Root Cause**: The application is trying to load SSL certificates before they've been created by Let's Encrypt.

**Evidence**: 
- `.env` file specifies SSL certificate paths
- Certificates don't exist yet (normal for initial deployment)
- Application fails to start due to missing certificate files

## 🔧 Solution 1: Fix Missing Dependencies

### Step 1.1: Navigate to Application Directory
```bash
# Connect to your VPS via Remote Desktop
# Open Terminal

# Navigate to the application directory
cd /var/www/nirvana-backend
```

### Step 1.2: Add Missing Dependencies
```bash
# Install the missing node-cron package
sudo -u nirvana npm install node-cron@^3.0.3

# Verify the installation
sudo -u nirvana npm list node-cron
```

**Expected Output**: `node-cron@3.0.3`

### Step 1.3: Install All Missing Dependencies (Comprehensive Fix)
The `deploy/package.json` is missing several dependencies that exist in the server code. Install them all:

```bash
# Install all missing dependencies at once
sudo -u nirvana npm install \
  node-cron@^3.0.3 \
  compression@^1.7.4 \
  morgan@^1.10.0 \
  winston-daily-rotate-file@^4.7.1 \
  sharp@^0.33.1 \
  axios@^1.6.2 \
  socket.io@^4.7.4 \
  uuid@^9.0.1 \
  joi@^17.11.0 \
  moment@^2.29.4 \
  lodash@^4.17.21 \
  fs-extra@^11.2.0 \
  archiver@^6.0.1 \
  csv-parser@^3.0.0 \
  csv-writer@^1.6.0 \
  pdf-lib@^1.17.1 \
  qrcode@^1.5.3
```

### Step 1.4: Verify Dependencies Installation
```bash
# Check that all dependencies are installed
sudo -u nirvana npm list --depth=0

# Look specifically for node-cron
sudo -u nirvana npm list | grep node-cron
```

## 🔒 Solution 2: Fix SSL Certificate Issue

### Option A: Temporary SSL Bypass (Recommended for Initial Testing)

### Step 2A.1: Modify Environment Configuration
```bash
# Edit the .env file to disable SSL temporarily
sudo -u nirvana nano /var/www/nirvana-backend/.env
```

**Add these lines to disable SSL temporarily:**
```bash
# Temporary SSL bypass for initial deployment
SSL_ENABLED=false
SSL_REDIRECT=false
HTTPS_REQUIRED=false
```

### Step 2A.2: Update Nginx Configuration for HTTP Testing
```bash
# Create a temporary HTTP-only nginx configuration
sudo nano /etc/nginx/sites-available/nirvana-organics-temp
```

**Add this temporary configuration:**
```nginx
server {
    listen 80;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;
    
    # Serve static files
    location / {
        root /var/www/nirvana-backend/dist;
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
    }
    
    # Admin panel
    location /admin {
        root /var/www/nirvana-backend/dist;
        try_files $uri $uri/ /admin/index.html;
    }
    
    # API routes
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Health check
    location /health {
        proxy_pass http://localhost:5000/health;
        access_log off;
    }
}
```

### Step 2A.3: Enable Temporary Configuration
```bash
# Disable current configuration
sudo rm /etc/nginx/sites-enabled/nirvana-organics

# Enable temporary configuration
sudo ln -s /etc/nginx/sites-available/nirvana-organics-temp /etc/nginx/sites-enabled/

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

### Option B: Proper SSL Setup (After Initial Testing)

### Step 2B.1: Install SSL Certificates
```bash
# Install Certbot (if not already installed)
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificates
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com
```

### Step 2B.2: Restore SSL Configuration
```bash
# Remove temporary configuration
sudo rm /etc/nginx/sites-enabled/nirvana-organics-temp

# Restore original SSL configuration
sudo ln -s /etc/nginx/sites-available/nirvana-organics /etc/nginx/sites-enabled/

# Update .env to enable SSL
sudo -u nirvana nano /var/www/nirvana-backend/.env
```

**Update these lines in .env:**
```bash
# Enable SSL after certificates are installed
SSL_ENABLED=true
SSL_REDIRECT=true
HTTPS_REQUIRED=true
```

## 🚀 Solution 3: Start the Application

### Step 3.1: Start Application with PM2
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Start the application
sudo -u nirvana pm2 start ecosystem.config.production.js

# Check status
sudo -u nirvana pm2 status

# View logs to verify startup
sudo -u nirvana pm2 logs
```

### Step 3.2: Verify Application is Running
```bash
# Check if the application is responding
curl -I http://localhost:5000/health

# Check if the website is accessible
curl -I http://shopnirvanaorganics.com
```

## 🔍 Troubleshooting Commands

### Check Dependencies
```bash
# List all installed packages
sudo -u nirvana npm list --depth=0

# Check for specific missing packages
sudo -u nirvana npm list | grep -E "(node-cron|sharp|socket.io|winston)"

# Check package.json vs installed packages
sudo -u nirvana npm outdated
```

### Check SSL Status
```bash
# Check if SSL certificates exist
sudo ls -la /etc/letsencrypt/live/shopnirvanaorganics.com/

# Check nginx SSL configuration
sudo nginx -t

# Check SSL certificate validity
sudo certbot certificates
```

### Check Application Logs
```bash
# View PM2 logs
sudo -u nirvana pm2 logs --lines 50

# View error logs specifically
sudo -u nirvana pm2 logs --err --lines 20

# Monitor logs in real-time
sudo -u nirvana pm2 logs --follow
```

### Check System Status
```bash
# Check if all services are running
sudo systemctl status nginx
sudo systemctl status mysql
sudo -u nirvana pm2 status

# Check port usage
sudo netstat -tlnp | grep :5000
sudo netstat -tlnp | grep :80
```

## ✅ Verification Steps

### Step 1: Verify Dependencies Fixed
```bash
# This should NOT show any errors
sudo -u nirvana node -e "console.log('Testing node-cron:', require('node-cron'))"
```

### Step 2: Verify Application Starts
```bash
# This should show the application as "online"
sudo -u nirvana pm2 status
```

### Step 3: Verify Website Access
```bash
# Test HTTP access (should work with temporary config)
curl -I http://shopnirvanaorganics.com

# Test API health endpoint
curl http://shopnirvanaorganics.com/api/health
```

### Step 4: Verify Admin Panel
Open browser and navigate to:
- `http://shopnirvanaorganics.com/admin` (temporary HTTP)
- `https://shopnirvanaorganics.com/admin` (after SSL setup)

## 🔄 Complete Fix Sequence

Execute these commands in order:

```bash
# 1. Fix dependencies
cd /var/www/nirvana-backend
sudo -u nirvana npm install node-cron@^3.0.3 compression@^1.7.4 morgan@^1.10.0 winston-daily-rotate-file@^4.7.1 sharp@^0.33.1 axios@^1.6.2 socket.io@^4.7.4 uuid@^9.0.1 joi@^17.11.0 moment@^2.29.4 lodash@^4.17.21 fs-extra@^11.2.0

# 2. Temporarily disable SSL
sudo -u nirvana nano /var/www/nirvana-backend/.env
# Add: SSL_ENABLED=false

# 3. Setup temporary HTTP nginx config
sudo nano /etc/nginx/sites-available/nirvana-organics-temp
# (Copy the HTTP config from above)

# 4. Enable temporary config
sudo rm /etc/nginx/sites-enabled/nirvana-organics
sudo ln -s /etc/nginx/sites-available/nirvana-organics-temp /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

# 5. Start application
sudo -u nirvana pm2 start ecosystem.config.production.js

# 6. Verify
sudo -u nirvana pm2 status
curl -I http://shopnirvanaorganics.com
```

## 📞 If Issues Persist

### Collect Debug Information
```bash
# Create debug log
mkdir ~/debug-logs

# Collect all relevant information
sudo -u nirvana pm2 logs --lines 100 > ~/debug-logs/pm2.log
sudo -u nirvana npm list --depth=0 > ~/debug-logs/packages.log
sudo nginx -t > ~/debug-logs/nginx.log 2>&1
sudo systemctl status nginx > ~/debug-logs/nginx-status.log
cat /var/www/nirvana-backend/.env | grep -v PASSWORD > ~/debug-logs/env-config.log

# Check the logs
cat ~/debug-logs/pm2.log
```

### Common Additional Issues
1. **Port 5000 in use**: `sudo lsof -i :5000` and kill conflicting processes
2. **Permission issues**: Ensure all files are owned by `nirvana` user
3. **Database connection**: Verify MySQL is running and credentials are correct
4. **Firewall blocking**: Check `sudo ufw status` and ensure ports 80/443 are open

---

**Next Steps**: After fixing these issues, the application should start successfully. Once confirmed working with HTTP, you can then set up SSL certificates and switch to HTTPS.
