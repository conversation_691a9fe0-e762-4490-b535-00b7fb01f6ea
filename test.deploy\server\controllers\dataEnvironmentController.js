const dataEnvironmentService = require('../services/dataEnvironmentService');
const { validationResult } = require('express-validator');

/**
 * Get current data environment mode
 */
const getCurrentMode = async (req, res) => {
  try {
    const userId = req.user.id;
    const sessionId = req.headers['x-session-id'] || null;
    
    const mode = await dataEnvironmentService.getCurrentMode(userId, sessionId);
    
    res.json({
      success: true,
      data: {
        mode,
        userId,
        sessionId
      }
    });
  } catch (error) {
    console.error('Error getting current data mode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get current data mode',
      error: error.message
    });
  }
};

/**
 * Set data environment mode
 */
const setDataMode = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { mode } = req.body;
    const sessionId = req.headers['x-session-id'] || null;
    
    const result = await dataEnvironmentService.setDataMode(userId, mode, sessionId);
    
    res.json({
      success: true,
      message: `Data mode switched to ${mode}`,
      data: result
    });
  } catch (error) {
    console.error('Error setting data mode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set data mode',
      error: error.message
    });
  }
};

/**
 * Toggle data environment mode
 */
const toggleDataMode = async (req, res) => {
  try {
    const userId = req.user.id;
    const sessionId = req.headers['x-session-id'] || null;
    
    const result = await dataEnvironmentService.toggleDataMode(userId, sessionId);
    
    res.json({
      success: true,
      message: `Data mode toggled to ${result.mode}`,
      data: result
    });
  } catch (error) {
    console.error('Error toggling data mode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle data mode',
      error: error.message
    });
  }
};

/**
 * Get data based on current mode
 */
const getData = async (req, res) => {
  try {
    const userId = req.user.id;
    const { entityType } = req.params;
    const sessionId = req.headers['x-session-id'] || null;
    const { limit, offset, order } = req.query;
    
    const options = {
      sessionId,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
      order: order ? JSON.parse(order) : undefined
    };
    
    const data = await dataEnvironmentService.getData(userId, entityType, options);
    const mode = await dataEnvironmentService.getCurrentMode(userId, sessionId);
    
    res.json({
      success: true,
      data: {
        items: data,
        mode,
        entityType,
        count: data.length
      }
    });
  } catch (error) {
    console.error('Error getting data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get data',
      error: error.message
    });
  }
};

/**
 * Create mock data entry
 */
const createMockData = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { entityType, data, description, tags, metadata } = req.body;
    
    const result = await dataEnvironmentService.createMockData(
      entityType, 
      data, 
      userId, 
      { description, tags, metadata }
    );
    
    res.status(201).json({
      success: true,
      message: 'Mock data created successfully',
      data: result
    });
  } catch (error) {
    console.error('Error creating mock data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create mock data',
      error: error.message
    });
  }
};

/**
 * Update mock data entry
 */
const updateMockData = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mockId } = req.params;
    const { data, description, tags, metadata } = req.body;
    
    const result = await dataEnvironmentService.updateMockData(
      mockId, 
      data, 
      { description, tags, metadata }
    );
    
    res.json({
      success: true,
      message: 'Mock data updated successfully',
      data: result
    });
  } catch (error) {
    console.error('Error updating mock data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mock data',
      error: error.message
    });
  }
};

/**
 * Delete mock data entry
 */
const deleteMockData = async (req, res) => {
  try {
    const { mockId } = req.params;
    
    const result = await dataEnvironmentService.deleteMockData(mockId);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Mock data deleted successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Mock data not found'
      });
    }
  } catch (error) {
    console.error('Error deleting mock data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete mock data',
      error: error.message
    });
  }
};

/**
 * Clear mock data for entity type
 */
const clearMockData = async (req, res) => {
  try {
    const userId = req.user.id;
    const { entityType } = req.params;
    const { clearAll } = req.query;
    
    let result;
    if (clearAll === 'true') {
      result = await dataEnvironmentService.clearAllMockData(userId);
    } else {
      result = await dataEnvironmentService.clearMockData(entityType, userId);
    }
    
    res.json({
      success: true,
      message: clearAll === 'true' ? 'All mock data cleared' : `Mock data cleared for ${entityType}`,
      data: result
    });
  } catch (error) {
    console.error('Error clearing mock data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear mock data',
      error: error.message
    });
  }
};

/**
 * Get mock data statistics
 */
const getMockDataStats = async (req, res) => {
  try {
    const stats = await dataEnvironmentService.getMockDataStats();
    
    res.json({
      success: true,
      data: {
        stats,
        totalEntities: stats.length,
        totalMockEntries: stats.reduce((sum, stat) => sum + parseInt(stat.count), 0),
        totalActiveEntries: stats.reduce((sum, stat) => sum + parseInt(stat.activeCount), 0)
      }
    });
  } catch (error) {
    console.error('Error getting mock data stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get mock data statistics',
      error: error.message
    });
  }
};

/**
 * Generate sample mock data
 */
const generateSampleData = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const result = await dataEnvironmentService.generateSampleMockData(userId);
    
    res.json({
      success: true,
      message: 'Sample mock data generated successfully',
      data: result
    });
  } catch (error) {
    console.error('Error generating sample data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate sample data',
      error: error.message
    });
  }
};

module.exports = {
  getCurrentMode,
  setDataMode,
  toggleDataMode,
  getData,
  createMockData,
  updateMockData,
  deleteMockData,
  clearMockData,
  getMockDataStats,
  generateSampleData
};
