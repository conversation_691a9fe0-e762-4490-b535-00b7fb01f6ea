#!/usr/bin/env node

// ============================================================================
// Nirvana Organics Backend - Webhook Deployment Handler
// ============================================================================

const express = require('express');
const crypto = require('crypto');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.WEBHOOK_PORT || 9000;
const SECRET = process.env.WEBHOOK_SECRET || 'your-webhook-secret';
const DEPLOYMENT_LOG = path.join(__dirname, '../logs/deployment.log');

// Middleware
app.use(express.json());

// Logging function
const log = (message, level = 'INFO') => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}\n`;
  
  console.log(logMessage.trim());
  
  // Write to log file
  fs.appendFileSync(DEPLOYMENT_LOG, logMessage, { flag: 'a' });
};

// Verify GitHub webhook signature
const verifySignature = (payload, signature) => {
  const hmac = crypto.createHmac('sha256', SECRET);
  hmac.update(payload);
  const calculatedSignature = `sha256=${hmac.digest('hex')}`;
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(calculatedSignature)
  );
};

// Execute deployment command
const executeDeployment = (environment, branch, callback) => {
  const deployScript = path.join(__dirname, 'git-deploy.sh');
  const command = `bash ${deployScript} --environment ${environment} --branch ${branch} --test`;
  
  log(`Executing deployment: ${command}`);
  
  exec(command, { cwd: path.dirname(__dirname) }, (error, stdout, stderr) => {
    if (error) {
      log(`Deployment failed: ${error.message}`, 'ERROR');
      log(`stderr: ${stderr}`, 'ERROR');
      callback(error, null);
      return;
    }
    
    log(`Deployment stdout: ${stdout}`);
    if (stderr) {
      log(`Deployment stderr: ${stderr}`, 'WARNING');
    }
    
    callback(null, stdout);
  });
};

// Webhook endpoint
app.post('/webhook', (req, res) => {
  const signature = req.headers['x-hub-signature-256'];
  const payload = JSON.stringify(req.body);
  
  // Verify signature
  if (!signature || !verifySignature(payload, signature)) {
    log('Invalid webhook signature', 'ERROR');
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  const { ref, repository, commits } = req.body;
  
  if (!ref || !repository) {
    log('Invalid webhook payload', 'ERROR');
    return res.status(400).json({ error: 'Invalid payload' });
  }
  
  const branch = ref.replace('refs/heads/', '');
  const repoName = repository.name;
  
  log(`Received webhook for ${repoName}, branch: ${branch}`);
  
  // Determine environment based on branch
  let environment;
  switch (branch) {
    case 'main':
    case 'master':
      environment = 'production';
      break;
    case 'staging':
      environment = 'staging';
      break;
    case 'develop':
    case 'development':
      environment = 'development';
      break;
    default:
      log(`Ignoring deployment for branch: ${branch}`, 'WARNING');
      return res.json({ message: `Branch ${branch} ignored` });
  }
  
  // Check if this is a push event
  if (req.headers['x-github-event'] !== 'push') {
    log(`Ignoring non-push event: ${req.headers['x-github-event']}`, 'WARNING');
    return res.json({ message: 'Non-push event ignored' });
  }
  
  // Log commit information
  if (commits && commits.length > 0) {
    log(`Commits in this push:`);
    commits.forEach(commit => {
      log(`  - ${commit.id.substring(0, 7)}: ${commit.message} (${commit.author.name})`);
    });
  }
  
  // Execute deployment
  executeDeployment(environment, branch, (error, output) => {
    if (error) {
      log(`Deployment to ${environment} failed`, 'ERROR');
      return res.status(500).json({ 
        error: 'Deployment failed', 
        details: error.message 
      });
    }
    
    log(`Deployment to ${environment} completed successfully`);
    res.json({ 
      message: `Deployment to ${environment} completed successfully`,
      branch,
      environment,
      output
    });
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Status endpoint
app.get('/status', (req, res) => {
  const logExists = fs.existsSync(DEPLOYMENT_LOG);
  let lastDeployment = null;
  
  if (logExists) {
    try {
      const logContent = fs.readFileSync(DEPLOYMENT_LOG, 'utf8');
      const lines = logContent.trim().split('\n');
      const lastLine = lines[lines.length - 1];
      
      if (lastLine) {
        const match = lastLine.match(/\[(.*?)\]/);
        if (match) {
          lastDeployment = match[1];
        }
      }
    } catch (error) {
      log(`Error reading deployment log: ${error.message}`, 'ERROR');
    }
  }
  
  res.json({
    status: 'running',
    port: PORT,
    lastDeployment,
    logFile: DEPLOYMENT_LOG,
    logExists
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  log(`Unhandled error: ${error.message}`, 'ERROR');
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  log(`Webhook deployment server started on port ${PORT}`);
  log(`Webhook endpoint: http://localhost:${PORT}/webhook`);
  log(`Health check: http://localhost:${PORT}/health`);
  log(`Status: http://localhost:${PORT}/status`);
  
  // Ensure log directory exists
  const logDir = path.dirname(DEPLOYMENT_LOG);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
    log(`Created log directory: ${logDir}`);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  log('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  log('Received SIGINT, shutting down gracefully');
  process.exit(0);
});

module.exports = app;
