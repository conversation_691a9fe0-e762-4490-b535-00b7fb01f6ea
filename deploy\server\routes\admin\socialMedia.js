const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../../middleware/auth');
const SocialMediaAccount = require('../../models/SocialMediaAccount');
const SocialMediaPost = require('../../models/SocialMediaPost');
const SocialMediaAnalytics = require('../../models/SocialMediaAnalytics');
const socialMediaService = require('../../services/socialMediaService');
const { body, validationResult, query, param } = require('express-validator');

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * GET /api/admin/social-media/accounts
 * Get all connected social media accounts
 */
router.get('/accounts', async (req, res) => {
  try {
    const accounts = await SocialMediaAccount.findActiveAccounts();
    
    // Add real-time status for each account
    const accountsWithStatus = await Promise.all(
      accounts.map(async (account) => {
        const accountData = account.toJSON();
        
        // Check if token needs refresh
        accountData.needsRefresh = account.needsRefresh();
        accountData.isTokenExpired = account.isTokenExpired();
        
        // Get recent analytics
        const recentAnalytics = await SocialMediaAnalytics.findOne({
          where: { accountId: account.id },
          order: [['date', 'DESC']]
        });
        
        if (recentAnalytics) {
          accountData.recentMetrics = {
            engagementRate: recentAnalytics.engagementRate,
            followersGrowth: recentAnalytics.followersGrowth,
            lastUpdated: recentAnalytics.lastUpdated
          };
        }
        
        return accountData;
      })
    );
    
    res.json({
      success: true,
      data: accountsWithStatus
    });
  } catch (error) {
    console.error('Error fetching social media accounts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch social media accounts',
      error: error.message
    });
  }
});

/**
 * POST /api/admin/social-media/accounts/:platform/connect
 * Initiate OAuth connection for a platform
 */
router.post('/accounts/:platform/connect', [
  param('platform').isIn(['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid platform',
        errors: errors.array()
      });
    }
    
    const { platform } = req.params;
    const authUrl = await socialMediaService.getAuthUrl(platform);
    
    res.json({
      success: true,
      data: {
        authUrl,
        platform
      }
    });
  } catch (error) {
    console.error('Error initiating OAuth connection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initiate OAuth connection',
      error: error.message
    });
  }
});

/**
 * POST /api/admin/social-media/accounts/:platform/callback
 * Handle OAuth callback and save account
 */
router.post('/accounts/:platform/callback', [
  param('platform').isIn(['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok']),
  body('code').notEmpty().withMessage('Authorization code is required'),
  body('state').optional()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const { platform } = req.params;
    const { code, state } = req.body;
    
    const accountData = await socialMediaService.handleCallback(platform, code, state);
    
    // Save or update account in database
    const [account, created] = await SocialMediaAccount.upsert({
      platform,
      accountId: accountData.id,
      accountName: accountData.name,
      accountHandle: accountData.handle,
      profilePicture: accountData.profilePicture,
      accessToken: accountData.accessToken,
      refreshToken: accountData.refreshToken,
      tokenExpiresAt: accountData.expiresAt,
      followersCount: accountData.followersCount || 0,
      followingCount: accountData.followingCount || 0,
      postsCount: accountData.postsCount || 0,
      platformData: accountData.platformData,
      permissions: accountData.permissions,
      isActive: true,
      lastSyncAt: new Date(),
      syncStatus: 'completed'
    }, {
      returning: true
    });
    
    res.json({
      success: true,
      message: created ? 'Account connected successfully' : 'Account updated successfully',
      data: account
    });
  } catch (error) {
    console.error('Error handling OAuth callback:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to connect account',
      error: error.message
    });
  }
});

/**
 * DELETE /api/admin/social-media/accounts/:id
 * Disconnect a social media account
 */
router.delete('/accounts/:id', [
  param('id').isUUID().withMessage('Invalid account ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid account ID',
        errors: errors.array()
      });
    }
    
    const { id } = req.params;
    
    const account = await SocialMediaAccount.findByPk(id);
    if (!account) {
      return res.status(404).json({
        success: false,
        message: 'Account not found'
      });
    }
    
    // Revoke access token if possible
    try {
      await socialMediaService.revokeToken(account.platform, account.accessToken);
    } catch (revokeError) {
      console.warn('Failed to revoke token:', revokeError.message);
    }
    
    // Soft delete by setting isActive to false
    await account.update({ isActive: false });
    
    res.json({
      success: true,
      message: 'Account disconnected successfully'
    });
  } catch (error) {
    console.error('Error disconnecting account:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to disconnect account',
      error: error.message
    });
  }
});

/**
 * POST /api/admin/social-media/accounts/:id/refresh
 * Refresh account token and sync data
 */
router.post('/accounts/:id/refresh', [
  param('id').isUUID().withMessage('Invalid account ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid account ID',
        errors: errors.array()
      });
    }
    
    const { id } = req.params;
    
    const account = await SocialMediaAccount.findByPk(id);
    if (!account) {
      return res.status(404).json({
        success: false,
        message: 'Account not found'
      });
    }
    
    // Refresh token if needed
    if (account.needsRefresh()) {
      const newTokenData = await socialMediaService.refreshToken(account.platform, account.refreshToken);
      await account.update({
        accessToken: newTokenData.accessToken,
        refreshToken: newTokenData.refreshToken || account.refreshToken,
        tokenExpiresAt: newTokenData.expiresAt
      });
    }
    
    // Sync account data
    const syncedData = await socialMediaService.syncAccountData(account.platform, account.accessToken);
    await account.updateMetrics(syncedData);
    
    res.json({
      success: true,
      message: 'Account refreshed successfully',
      data: account
    });
  } catch (error) {
    console.error('Error refreshing account:', error);
    await account?.recordError(error);
    res.status(500).json({
      success: false,
      message: 'Failed to refresh account',
      error: error.message
    });
  }
});

/**
 * GET /api/admin/social-media/posts
 * Get social media posts with filtering and pagination
 */
router.get('/posts', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['draft', 'scheduled', 'publishing', 'published', 'failed', 'cancelled']),
  query('platform').optional().isIn(['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok']),
  query('startDate').optional().isISO8601().withMessage('Invalid start date'),
  query('endDate').optional().isISO8601().withMessage('Invalid end date')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const {
      page = 1,
      limit = 20,
      status,
      platform,
      startDate,
      endDate
    } = req.query;
    
    const offset = (page - 1) * limit;
    const whereClause = {};
    
    if (status) {
      whereClause.status = status;
    }
    
    if (platform) {
      whereClause.platforms = {
        [SocialMediaPost.sequelize.Sequelize.Op.contains]: [platform]
      };
    }
    
    if (startDate && endDate) {
      whereClause.createdAt = {
        [SocialMediaPost.sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    }
    
    const { count, rows: posts } = await SocialMediaPost.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
      include: [
        {
          model: require('../../models/User'),
          as: 'creator',
          attributes: ['id', 'name', 'email']
        },
        {
          model: require('../../models/User'),
          as: 'approver',
          attributes: ['id', 'name', 'email'],
          required: false
        }
      ]
    });
    
    res.json({
      success: true,
      data: {
        posts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching posts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch posts',
      error: error.message
    });
  }
});

/**
 * POST /api/admin/social-media/posts
 * Create a new social media post
 */
router.post('/posts', [
  body('content').notEmpty().withMessage('Content is required'),
  body('platforms').isArray({ min: 1 }).withMessage('At least one platform is required'),
  body('platforms.*').isIn(['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok']),
  body('scheduledAt').optional().isISO8601().withMessage('Invalid scheduled date'),
  body('mediaUrls').optional().isArray(),
  body('mediaType').optional().isIn(['none', 'image', 'video', 'carousel', 'story']),
  body('hashtags').optional().isArray(),
  body('mentions').optional().isArray(),
  body('campaignId').optional().isString(),
  body('category').optional().isString(),
  body('requiresApproval').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const {
      content,
      platforms,
      scheduledAt,
      mediaUrls = [],
      mediaType = 'none',
      hashtags = [],
      mentions = [],
      campaignId,
      category,
      requiresApproval = false
    } = req.body;
    
    // Validate that we have connected accounts for the selected platforms
    const connectedAccounts = await SocialMediaAccount.findAll({
      where: {
        platform: platforms,
        isActive: true
      }
    });
    
    const connectedPlatforms = connectedAccounts.map(acc => acc.platform);
    const missingPlatforms = platforms.filter(p => !connectedPlatforms.includes(p));
    
    if (missingPlatforms.length > 0) {
      return res.status(400).json({
        success: false,
        message: `No connected accounts found for platforms: ${missingPlatforms.join(', ')}`
      });
    }
    
    const post = await SocialMediaPost.create({
      content,
      platforms,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
      mediaUrls,
      mediaType,
      hashtags,
      mentions,
      campaignId,
      category,
      requiresApproval,
      createdBy: req.user.id,
      status: scheduledAt ? 'scheduled' : 'draft'
    });
    
    // If post doesn't require approval and is scheduled, mark it as ready
    if (!requiresApproval && scheduledAt) {
      await post.update({ status: 'scheduled' });
    }
    
    res.status(201).json({
      success: true,
      message: 'Post created successfully',
      data: post
    });
  } catch (error) {
    console.error('Error creating post:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create post',
      error: error.message
    });
  }
});

/**
 * PUT /api/admin/social-media/posts/:id
 * Update a social media post
 */
router.put('/posts/:id', [
  param('id').isUUID().withMessage('Invalid post ID'),
  body('content').optional().notEmpty().withMessage('Content cannot be empty'),
  body('platforms').optional().isArray({ min: 1 }).withMessage('At least one platform is required'),
  body('scheduledAt').optional().isISO8601().withMessage('Invalid scheduled date')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const post = await SocialMediaPost.findByPk(id);

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    // Only allow editing of draft and scheduled posts
    if (!['draft', 'scheduled'].includes(post.status)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot edit published or failed posts'
      });
    }

    await post.update(req.body);

    res.json({
      success: true,
      message: 'Post updated successfully',
      data: post
    });
  } catch (error) {
    console.error('Error updating post:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update post',
      error: error.message
    });
  }
});

/**
 * DELETE /api/admin/social-media/posts/:id
 * Delete a social media post
 */
router.delete('/posts/:id', [
  param('id').isUUID().withMessage('Invalid post ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid post ID',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const post = await SocialMediaPost.findByPk(id);

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    // Only allow deletion of draft and scheduled posts
    if (!['draft', 'scheduled', 'failed'].includes(post.status)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete published posts'
      });
    }

    await post.destroy();

    res.json({
      success: true,
      message: 'Post deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting post:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete post',
      error: error.message
    });
  }
});

/**
 * POST /api/admin/social-media/posts/:id/publish
 * Publish a post immediately
 */
router.post('/posts/:id/publish', [
  param('id').isUUID().withMessage('Invalid post ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid post ID',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const post = await SocialMediaPost.findByPk(id);

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    if (!['draft', 'scheduled'].includes(post.status)) {
      return res.status(400).json({
        success: false,
        message: 'Post cannot be published'
      });
    }

    // Update status to publishing
    await post.update({ status: 'publishing' });

    // Publish to all platforms
    const results = await socialMediaService.publishPost(post);

    if (results.success) {
      await post.markAsPublished(results.platformPostIds);
      res.json({
        success: true,
        message: 'Post published successfully',
        data: { post, results }
      });
    } else {
      await post.markAsFailed(results.error);
      res.status(500).json({
        success: false,
        message: 'Failed to publish post',
        error: results.error
      });
    }
  } catch (error) {
    console.error('Error publishing post:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to publish post',
      error: error.message
    });
  }
});

/**
 * GET /api/admin/social-media/analytics
 * Get social media analytics dashboard data
 */
router.get('/analytics', [
  query('period').optional().isIn(['daily', 'weekly', 'monthly']),
  query('days').optional().isInt({ min: 1, max: 365 }),
  query('platform').optional().isIn(['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      period = 'daily',
      days = 30,
      platform
    } = req.query;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    // Get all active accounts or filter by platform
    const accountsWhere = { isActive: true };
    if (platform) {
      accountsWhere.platform = platform;
    }

    const accounts = await SocialMediaAccount.findAll({
      where: accountsWhere
    });

    const analyticsData = {};

    for (const account of accounts) {
      const analytics = await SocialMediaAnalytics.findByAccount(
        account.id,
        startDate,
        new Date(),
        period
      );

      const summary = await SocialMediaAnalytics.getAccountSummary(account.id, parseInt(days));

      analyticsData[account.platform] = {
        account: {
          id: account.id,
          name: account.accountName,
          handle: account.accountHandle,
          profilePicture: account.profilePicture,
          followersCount: account.followersCount
        },
        analytics,
        summary: summary[0] || {}
      };
    }

    res.json({
      success: true,
      data: analyticsData
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics',
      error: error.message
    });
  }
});

/**
 * GET /api/admin/social-media/analytics/:accountId
 * Get detailed analytics for a specific account
 */
router.get('/analytics/:accountId', [
  param('accountId').isUUID().withMessage('Invalid account ID'),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
  query('period').optional().isIn(['daily', 'weekly', 'monthly'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { accountId } = req.params;
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      period = 'daily'
    } = req.query;

    const account = await SocialMediaAccount.findByPk(accountId);
    if (!account) {
      return res.status(404).json({
        success: false,
        message: 'Account not found'
      });
    }

    const analytics = await SocialMediaAnalytics.findByAccount(
      accountId,
      startDate,
      endDate,
      period
    );

    const growthTrend = await SocialMediaAnalytics.getGrowthTrend(
      accountId,
      period,
      30
    );

    const topContent = await SocialMediaAnalytics.getTopPerformingContent(
      accountId,
      30,
      10
    );

    res.json({
      success: true,
      data: {
        account,
        analytics,
        growthTrend,
        topContent
      }
    });
  } catch (error) {
    console.error('Error fetching account analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch account analytics',
      error: error.message
    });
  }
});

module.exports = router;
