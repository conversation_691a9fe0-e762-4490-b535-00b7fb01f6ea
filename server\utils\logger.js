const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logDir = process.env.LOG_PATH || './logs';
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Custom format for logs
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta)}`;
    }
    
    return logMessage;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    // Error logs
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Combined logs
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Database logs
    new winston.transports.File({
      filename: path.join(logDir, 'database.log'),
      level: 'debug',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      tailable: true
    }),
    
    // Security logs
    new winston.transports.File({
      filename: path.join(logDir, 'security.log'),
      level: 'warn',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// Request logging middleware
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log request start
  logger.info('HTTP Request Started', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.id || 'unknown'
  });
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.id || 'unknown'
    };
    
    if (res.statusCode >= 500) {
      logger.error('HTTP Request Server Error', logData);
    } else if (res.statusCode >= 400) {
      logger.warn('HTTP Request Client Error', logData);
    } else {
      logger.info('HTTP Request Completed', logData);
    }
  });
  
  next();
};

// Security event logger
const securityLogger = {
  loginAttempt: (email, ip, success, reason = null) => {
    logger.info('Login Attempt', {
      email,
      ip,
      success,
      reason,
      timestamp: new Date().toISOString(),
      type: 'authentication'
    });
  },
  
  rateLimitExceeded: (ip, endpoint, limit) => {
    logger.warn('Rate Limit Exceeded', {
      ip,
      endpoint,
      limit,
      timestamp: new Date().toISOString(),
      type: 'rate_limit'
    });
  },
  
  suspiciousActivity: (ip, activity, details) => {
    logger.warn('Suspicious Activity Detected', {
      ip,
      activity,
      details,
      timestamp: new Date().toISOString(),
      type: 'security_alert'
    });
  },
  
  adminAccess: (userId, action, ip) => {
    logger.info('Admin Access', {
      userId,
      action,
      ip,
      timestamp: new Date().toISOString(),
      type: 'admin_activity'
    });
  },
  
  fileUpload: (userId, filename, size, ip) => {
    logger.info('File Upload', {
      userId,
      filename,
      size,
      ip,
      timestamp: new Date().toISOString(),
      type: 'file_operation'
    });
  }
};

// Database operation logger
const dbLogger = {
  query: (query, duration, error = null) => {
    if (error) {
      logger.error('Database Query Error', {
        query: query.substring(0, 200), // Truncate long queries
        duration: `${duration}ms`,
        error: error.message,
        type: 'database_error'
      });
    } else if (duration > 1000) { // Log slow queries
      logger.warn('Slow Database Query', {
        query: query.substring(0, 200),
        duration: `${duration}ms`,
        type: 'slow_query'
      });
    }
  },
  
  connection: (event, details) => {
    logger.info('Database Connection Event', {
      event,
      details,
      timestamp: new Date().toISOString(),
      type: 'database_connection'
    });
  }
};

// Application event logger
const appLogger = {
  startup: (port, environment) => {
    logger.info('Application Started', {
      port,
      environment,
      nodeVersion: process.version,
      timestamp: new Date().toISOString(),
      type: 'application_lifecycle'
    });
  },
  
  shutdown: (signal) => {
    logger.info('Application Shutdown', {
      signal,
      timestamp: new Date().toISOString(),
      type: 'application_lifecycle'
    });
  },
  
  error: (error, context = {}) => {
    logger.error('Application Error', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      type: 'application_error'
    });
  },
  
  payment: (orderId, amount, status, paymentMethod) => {
    logger.info('Payment Transaction', {
      orderId,
      amount,
      status,
      paymentMethod,
      timestamp: new Date().toISOString(),
      type: 'payment'
    });
  },
  
  order: (orderId, userId, action, details) => {
    logger.info('Order Event', {
      orderId,
      userId,
      action,
      details,
      timestamp: new Date().toISOString(),
      type: 'order'
    });
  }
};

// Performance monitoring logger
const performanceLogger = {
  apiResponse: (endpoint, method, duration, statusCode) => {
    const logData = {
      endpoint,
      method,
      duration: `${duration}ms`,
      statusCode,
      timestamp: new Date().toISOString(),
      type: 'performance'
    };
    
    if (duration > 5000) { // Log very slow responses
      logger.warn('Very Slow API Response', logData);
    } else if (duration > 2000) { // Log slow responses
      logger.info('Slow API Response', logData);
    }
  },
  
  memoryUsage: () => {
    const usage = process.memoryUsage();
    logger.info('Memory Usage', {
      rss: `${Math.round(usage.rss / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
      external: `${Math.round(usage.external / 1024 / 1024)}MB`,
      timestamp: new Date().toISOString(),
      type: 'system_metrics'
    });
  }
};

// Global error handler
const globalErrorHandler = (err, req, res, next) => {
  // Log the error
  logger.error('Unhandled Error', {
    message: err.message,
    stack: err.stack,
    url: req?.url,
    method: req?.method,
    ip: req?.ip,
    userAgent: req?.get('User-Agent'),
    timestamp: new Date().toISOString(),
    type: 'unhandled_error'
  });
  
  // Send appropriate response
  if (process.env.NODE_ENV === 'production') {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      requestId: req.id || 'unknown'
    });
  } else {
    res.status(500).json({
      success: false,
      error: err.message,
      stack: err.stack,
      requestId: req.id || 'unknown'
    });
  }
};

module.exports = {
  logger,
  requestLogger,
  securityLogger,
  dbLogger,
  appLogger,
  performanceLogger,
  globalErrorHandler
};
