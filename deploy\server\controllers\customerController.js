const { User, Order, Product, Newsletter } = require('../models');
const { Op } = require('sequelize');

// Get all customers with pagination and filtering
const getCustomers = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search = '', 
      segment = 'all',
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // Build where clause
    let whereClause = { role: 'customer' };
    
    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Get customers with order statistics
    const customers = await User.findAndCountAll({
      where: whereClause,
      attributes: [
        'id', 'firstName', 'lastName', 'email', 'phone', 
        'isActive', 'createdAt', 'lastLoginAt'
      ],
      include: [
        {
          model: Order,
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('Orders.id')), 'orderCount'],
            [sequelize.fn('SUM', sequelize.col('Orders.total')), 'totalSpent'],
            [sequelize.fn('MAX', sequelize.col('Orders.createdAt')), 'lastOrderDate']
          ],
          required: false
        },
        {
          model: Newsletter,
          attributes: ['isSubscribed', 'subscribedAt'],
          required: false
        }
      ],
      group: ['User.id', 'Newsletter.id'],
      order: [[sortBy, sortOrder]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Apply segment filtering
    let filteredCustomers = customers.rows;
    
    if (segment !== 'all') {
      filteredCustomers = customers.rows.filter(customer => {
        const orderCount = parseInt(customer.Orders?.[0]?.dataValues?.orderCount || 0);
        const totalSpent = parseFloat(customer.Orders?.[0]?.dataValues?.totalSpent || 0);
        
        switch (segment) {
          case 'new':
            return orderCount === 0;
          case 'returning':
            return orderCount > 0 && orderCount <= 5;
          case 'loyal':
            return orderCount > 5;
          case 'high_value':
            return totalSpent > 500;
          case 'newsletter':
            return customer.Newsletter?.isSubscribed;
          default:
            return true;
        }
      });
    }

    res.json({
      success: true,
      data: {
        customers: filteredCustomers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(customers.count / limit),
          totalCustomers: customers.count,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      error: error.message
    });
  }
};

// Get customer profile with detailed information
const getCustomerProfile = async (req, res) => {
  try {
    const { customerId } = req.params;

    const customer = await User.findByPk(customerId, {
      attributes: [
        'id', 'firstName', 'lastName', 'email', 'phone',
        'dateOfBirth', 'isActive', 'createdAt', 'lastLoginAt'
      ],
      include: [
        {
          model: Order,
          attributes: [
            'id', 'orderNumber', 'status', 'total', 'createdAt'
          ],
          include: [
            {
              model: Product,
              through: { attributes: ['quantity', 'price'] },
              attributes: ['id', 'name', 'images']
            }
          ],
          order: [['createdAt', 'DESC']],
          limit: 10
        },
        {
          model: Newsletter,
          attributes: ['isSubscribed', 'subscribedAt', 'preferences']
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Calculate customer statistics
    const orderStats = await Order.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalOrders'],
        [sequelize.fn('SUM', sequelize.col('total')), 'totalSpent'],
        [sequelize.fn('AVG', sequelize.col('total')), 'averageOrderValue'],
        [sequelize.fn('MAX', sequelize.col('createdAt')), 'lastOrderDate']
      ],
      where: { userId: customerId }
    });

    // Get favorite products
    const favoriteProducts = await Product.findAll({
      attributes: [
        'id', 'name', 'images', 'price',
        [sequelize.fn('COUNT', sequelize.col('OrderProducts.productId')), 'orderCount']
      ],
      include: [
        {
          model: Order,
          through: { attributes: [] },
          where: { userId: customerId },
          attributes: []
        }
      ],
      group: ['Product.id'],
      order: [[sequelize.fn('COUNT', sequelize.col('OrderProducts.productId')), 'DESC']],
      limit: 5
    });

    res.json({
      success: true,
      data: {
        customer,
        stats: orderStats?.dataValues || {},
        favoriteProducts
      }
    });

  } catch (error) {
    console.error('Get customer profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer profile',
      error: error.message
    });
  }
};

// Update customer information
const updateCustomer = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { firstName, lastName, email, phone, isActive, notes } = req.body;

    const customer = await User.findByPk(customerId);
    
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    await customer.update({
      firstName,
      lastName,
      email,
      phone,
      isActive,
      adminNotes: notes
    });

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: { customer }
    });

  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer',
      error: error.message
    });
  }
};

// Get customer segments for marketing
const getCustomerSegments = async (req, res) => {
  try {
    // Get all customers with order data
    const customers = await User.findAll({
      where: { role: 'customer' },
      attributes: ['id', 'firstName', 'lastName', 'email', 'createdAt'],
      include: [
        {
          model: Order,
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('Orders.id')), 'orderCount'],
            [sequelize.fn('SUM', sequelize.col('Orders.total')), 'totalSpent'],
            [sequelize.fn('MAX', sequelize.col('Orders.createdAt')), 'lastOrderDate']
          ],
          required: false
        },
        {
          model: Newsletter,
          attributes: ['isSubscribed'],
          required: false
        }
      ],
      group: ['User.id', 'Newsletter.id']
    });

    // Segment customers
    const segments = {
      new_customers: [],
      returning_customers: [],
      loyal_customers: [],
      high_value_customers: [],
      newsletter_subscribers: [],
      inactive_customers: []
    };

    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    customers.forEach(customer => {
      const orderCount = parseInt(customer.Orders?.[0]?.dataValues?.orderCount || 0);
      const totalSpent = parseFloat(customer.Orders?.[0]?.dataValues?.totalSpent || 0);
      const lastOrderDate = customer.Orders?.[0]?.dataValues?.lastOrderDate;
      const isSubscribed = customer.Newsletter?.isSubscribed;

      // Categorize customers
      if (orderCount === 0) {
        segments.new_customers.push(customer);
      } else if (orderCount > 0 && orderCount <= 5) {
        segments.returning_customers.push(customer);
      } else if (orderCount > 5) {
        segments.loyal_customers.push(customer);
      }

      if (totalSpent > 500) {
        segments.high_value_customers.push(customer);
      }

      if (isSubscribed) {
        segments.newsletter_subscribers.push(customer);
      }

      if (lastOrderDate && new Date(lastOrderDate) < thirtyDaysAgo) {
        segments.inactive_customers.push(customer);
      }
    });

    res.json({
      success: true,
      data: { segments }
    });

  } catch (error) {
    console.error('Get customer segments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer segments',
      error: error.message
    });
  }
};

module.exports = {
  getCustomers,
  getCustomerProfile,
  updateCustomer,
  getCustomerSegments
};
