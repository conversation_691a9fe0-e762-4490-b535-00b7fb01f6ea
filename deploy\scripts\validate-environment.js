#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates all required environment variables are set
 */

const requiredVars = [
  'NODE_ENV',
  'PORT',
  'DB_HOST',
  'DB_NAME',
  'DB_USER',
  'DB_PASSWORD',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'EMAIL_USER',
  'EMAIL_PASS',
  'SQUARE_ACCESS_TOKEN',
  'SQUARE_APPLICATION_ID'
];

function validateEnvironment() {
  console.log('🔍 Validating environment configuration...');

  let missing = [];
  let hasDefaults = [];

  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      missing.push(varName);
    } else if (value.includes('CHANGE_THIS') || value.includes('your-')) {
      hasDefaults.push(varName);
    }
  });

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => console.error(`   - ${varName}`));
  }

  if (hasDefaults.length > 0) {
    console.error('❌ Environment variables with default values (must be changed):');
    hasDefaults.forEach(varName => console.error(`   - ${varName}`));
  }

  if (missing.length === 0 && hasDefaults.length === 0) {
    console.log('✅ Environment validation passed');
    process.exit(0);
  } else {
    console.error('❌ Environment validation failed');
    process.exit(1);
  }
}

validateEnvironment();