<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Order Alert - {{orderNumber}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .alert-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .order-number {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        .priority-high {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        .priority-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 10px;
        }
        .priority-high-badge {
            background-color: #dc3545;
            color: white;
        }
        .priority-normal-badge {
            background-color: #28a745;
            color: white;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .customer-section {
            background-color: #e3f2fd;
        }
        .items-section {
            background-color: #f3e5f5;
        }
        .pricing-section {
            background-color: #e8f5e8;
        }
        .shipping-section {
            background-color: #fff3e0;
        }
        .payment-section {
            background-color: #fce4ec;
        }
        .section h3 {
            margin-top: 0;
            color: #007bff;
            font-size: 18px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .info-item {
            background-color: white;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 16px;
            color: #333;
        }
        .item-row {
            background-color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }
        .item-sku {
            color: #666;
            font-size: 12px;
            margin: 5px 0;
        }
        .item-quantity {
            color: #007bff;
            font-weight: bold;
        }
        .item-price {
            font-weight: bold;
            font-size: 18px;
            color: #28a745;
        }
        .pricing-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .pricing-row:last-child {
            border-bottom: none;
        }
        .pricing-row.total {
            border-top: 2px solid #28a745;
            font-weight: bold;
            font-size: 20px;
            color: #28a745;
            margin-top: 10px;
            padding-top: 15px;
        }
        .discount {
            color: #dc3545;
        }
        .address-box {
            background-color: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            white-space: pre-line;
        }
        .membership-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .membership-premium {
            background-color: #6f42c1;
            color: white;
        }
        .membership-regular {
            background-color: #007bff;
            color: white;
        }
        .membership-first-time {
            background-color: #28a745;
            color: white;
        }
        .membership-guest {
            background-color: #6c757d;
            color: white;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            color: #666;
            font-size: 12px;
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            .item-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .item-price {
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header {{#if (eq priority 'high')}}priority-high{{/if}}">
            <div class="alert-icon">🛒</div>
            <h1>New Order Alert</h1>
            <div class="order-number">
                #{{orderNumber}}
                <span class="priority-badge {{#if (eq priority 'high')}}priority-high-badge{{else}}priority-normal-badge{{/if}}">
                    {{priority}} Priority
                </span>
            </div>
            <p>Order Total: <strong>${{orderTotal}}</strong></p>
            <p>{{orderDate}}</p>
        </div>

        <!-- Customer Information -->
        <div class="section customer-section">
            <h3>👤 Customer Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Customer Name</div>
                    <div class="info-value">{{customerName}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Email Address</div>
                    <div class="info-value">{{customerEmail}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Membership Type</div>
                    <div class="info-value">
                        <span class="membership-badge membership-{{customerMembership}}">
                            {{customerMembership}}
                        </span>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">Order Priority</div>
                    <div class="info-value">{{priority}}</div>
                </div>
            </div>
        </div>

        <!-- Order Items -->
        <div class="section items-section">
            <h3>📦 Order Items</h3>
            {{#each items}}
            <div class="item-row">
                <div class="item-details">
                    <div class="item-name">{{productName}}</div>
                    <div class="item-sku">SKU: {{sku}}</div>
                    <div class="item-quantity">Quantity: {{quantity}}</div>
                </div>
                <div class="item-price">${{total}}</div>
            </div>
            {{/each}}
        </div>

        <!-- Pricing Summary -->
        <div class="section pricing-section">
            <h3>💰 Pricing Breakdown</h3>
            <div class="pricing-row">
                <span>Subtotal:</span>
                <span>${{subtotal}}</span>
            </div>
            {{#if discount}}
            <div class="pricing-row discount">
                <span>Discount:</span>
                <span>-${{discount}}</span>
            </div>
            {{/if}}
            <div class="pricing-row">
                <span>Tax:</span>
                <span>${{tax}}</span>
            </div>
            <div class="pricing-row">
                <span>Shipping:</span>
                <span>${{shipping}}</span>
            </div>
            <div class="pricing-row total">
                <span>Total:</span>
                <span>${{orderTotal}}</span>
            </div>
            {{#if couponsUsed}}
            <div style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border-radius: 4px;">
                <strong>Coupons Used:</strong> {{couponsUsed}}
            </div>
            {{/if}}
        </div>

        <!-- Shipping Information -->
        <div class="section shipping-section">
            <h3>🚚 Shipping Details</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Shipping Method</div>
                    <div class="info-value">{{shippingMethod}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Shipping Cost</div>
                    <div class="info-value">${{shipping}}</div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <div class="info-label">Shipping Address</div>
                <div class="address-box">{{shippingAddress}}</div>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="section payment-section">
            <h3>💳 Payment Details</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Payment Status</div>
                    <div class="info-value">{{paymentStatus}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Payment Method</div>
                    <div class="info-value">{{paymentMethod}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Transaction ID</div>
                    <div class="info-value">{{transactionId}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Amount Charged</div>
                    <div class="info-value">${{orderTotal}}</div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="#" class="btn btn-primary">View Full Order</a>
            <a href="#" class="btn btn-success">Process Order</a>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Nirvana Organics Admin System</strong></p>
            <p>This is an automated notification for order processing.</p>
            <p>Please process this order promptly to maintain customer satisfaction.</p>
        </div>
    </div>
</body>
</html>
