{"name": "nirvana-organics-server", "version": "1.0.0", "description": "Nirvana Organics E-commerce Backend Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "migrate": "node scripts/run-migrations.js", "migrate:test": "NODE_ENV=testing node scripts/run-migrations.js", "migrate:prod": "NODE_ENV=production node scripts/run-migrations.js", "seed": "node scripts/seed-database.js", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "sequelize": "^6.35.2", "mysql2": "^3.6.5", "redis": "^4.6.11", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "axios": "^1.6.2", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "uuid": "^9.0.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "moment": "^2.29.4", "lodash": "^4.17.21", "crypto": "^1.0.1", "fs-extra": "^11.2.0", "archiver": "^6.0.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "pdf-lib": "^1.17.1", "qrcode": "^1.5.3", "stripe": "^14.9.0", "squareup": "^29.0.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "express-slow-down": "^2.0.1", "express-fileupload": "^1.4.3", "image-size": "^1.0.2", "mime-types": "^2.1.35", "sanitize-html": "^2.11.0", "validator": "^13.11.0", "xss": "^1.0.14", "hpp": "^0.2.3", "express-mongo-sanitize": "^2.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ecommerce", "organic", "nodejs", "express", "mysql", "redis", "api"], "author": "Nirvana Organics", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/nirvana-organics.git"}, "bugs": {"url": "https://github.com/your-username/nirvana-organics/issues"}, "homepage": "https://shopnirvanaorganics.com"}