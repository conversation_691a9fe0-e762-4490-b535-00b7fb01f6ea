# 🚀 Nirvana Organics Complete VPS Deployment Guide

This comprehensive guide provides step-by-step instructions for deploying the Nirvana Organics e-commerce platform on a VPS from a completely fresh server to a fully functional website with 100% success rate.

## 📋 **Table of Contents**

1. [Prerequisites](#prerequisites)
2. [Server Preparation](#server-preparation)
3. [Domain and DNS Setup](#domain-and-dns-setup)
4. [Testing Environment Deployment](#testing-environment-deployment)
5. [Production Environment Deployment](#production-environment-deployment)
6. [Social Media Integration Setup](#social-media-integration-setup)
7. [Post-Deployment Verification](#post-deployment-verification)
8. [Maintenance and Updates](#maintenance-and-updates)
9. [Troubleshooting](#troubleshooting)
10. [Rollback Procedures](#rollback-procedures)

## 🔧 **Prerequisites**

### **VPS Requirements**
- **Operating System**: Ubuntu 20.04 LTS or Ubuntu 22.04 LTS
- **RAM**: Minimum 4GB (8GB recommended for production)
- **Storage**: Minimum 50GB SSD (100GB recommended)
- **CPU**: 2+ cores (4+ cores recommended for production)
- **Network**: Static IP address with full root access

### **Domain Requirements**
- Domain name registered and accessible
- Access to domain registrar's DNS management panel
- SSL certificate capability (Let's Encrypt will be used)

### **Required Credentials and API Keys**

#### **Email Service (Gmail App Passwords Recommended)**
```
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-16-character-app-password
```

#### **Square Payment Integration**
- **Sandbox (Testing)**:
  - Application ID: `sandbox-sq0idb-...`
  - Access Token: `EAAAl...` (sandbox)
  - Webhook Signature Key: `your-sandbox-webhook-key`

- **Production**:
  - Application ID: `sq0idp-...`
  - Access Token: `EAAAl...` (production)
  - Webhook Signature Key: `your-production-webhook-key`

#### **Google OAuth**
- Client ID: `123456789-...apps.googleusercontent.com`
- Client Secret: `GOCSPX-...`

#### **Social Media API Keys**
- **Facebook**: App ID, App Secret, Page ID
- **Twitter/X**: Client ID, Client Secret, Bearer Token
- **Instagram**: Client ID, Client Secret
- **LinkedIn**: Client ID, Client Secret
- **YouTube**: Client ID, Client Secret, API Key
- **Pinterest**: Client ID, Client Secret
- **TikTok**: Client ID, Client Secret

#### **Database Passwords**
Generate strong passwords (32+ characters) for:
- Production database user
- Testing database user
- Backup database user
- Redis password

## 🖥️ **Server Preparation**

### **Step 1: Initial Server Setup**

Connect to your VPS via SSH:
```bash
ssh root@your-server-ip
```

Update the system:
```bash
apt update && apt upgrade -y
```

Create a non-root user with sudo privileges:
```bash
adduser nirvana
usermod -aG sudo nirvana
```

### **Step 2: Configure SSH Security**

Edit SSH configuration:
```bash
nano /etc/ssh/sshd_config
```

Add these security settings:
```
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
Port 22
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
```

Restart SSH service:
```bash
systemctl restart ssh
```

### **Step 3: Configure Firewall**

Install and configure UFW:
```bash
ufw default deny incoming
ufw default allow outgoing
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

### **Step 4: Install Essential Dependencies**

Install Node.js 18:
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt install -y nodejs
```

Install other dependencies:
```bash
apt install -y \
  nginx \
  mysql-server \
  redis-server \
  git \
  curl \
  wget \
  unzip \
  build-essential \
  python3-dev \
  certbot \
  python3-certbot-nginx \
  fail2ban \
  htop \
  iotop \
  nethogs
```

Install PM2 globally:
```bash
npm install -g pm2@latest
```

## 🌐 **Domain and DNS Setup**

### **Step 1: Configure DNS Records**

In your domain registrar's DNS panel, create these A records:

| Type | Name | Value | TTL |
|------|------|-------|-----|
| A | @ | your-server-ip | 300 |
| A | www | your-server-ip | 300 |
| A | test | your-server-ip | 300 |

### **Step 2: Verify DNS Propagation**

Wait for DNS propagation (usually 5-30 minutes):
```bash
dig shopnirvanaorganics.com
dig www.shopnirvanaorganics.com
dig test.shopnirvanaorganics.com
```

All should return your server IP address.

## 🧪 **Testing Environment Deployment**

### **Step 1: Clone Repository**

```bash
cd /home/<USER>
git clone https://github.com/your-username/nirvana-organics.git
cd nirvana-organics
```

### **Step 2: Configure Testing Environment**

Copy and edit the testing environment file:
```bash
cp test.deploy/.env.testing .env.testing
nano .env.testing
```

**Critical Environment Variables to Update:**
```bash
# Database
DB_PASSWORD=your-super-secure-test-db-password

# JWT Secrets (generate 64-character random strings)
JWT_SECRET=your-ultra-secure-jwt-secret-testing-64-chars-minimum
JWT_REFRESH_SECRET=your-ultra-secure-refresh-secret-testing-64-chars-minimum
SESSION_SECRET=your-ultra-secure-session-secret-testing-64-chars-minimum

# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password

# Square (Sandbox)
SQUARE_ACCESS_TOKEN=your-sandbox-access-token
SQUARE_APPLICATION_ID=your-sandbox-application-id
SQUARE_WEBHOOK_SIGNATURE_KEY=your-sandbox-webhook-key

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Social Media APIs (Testing)
FACEBOOK_CLIENT_ID=your-facebook-test-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-test-app-secret
TWITTER_CLIENT_ID=your-twitter-test-client-id
TWITTER_CLIENT_SECRET=your-twitter-test-client-secret
# ... (add all other social media credentials)
```

### **Step 3: Run Testing Deployment Script**

Make the deployment script executable:
```bash
chmod +x test.deploy/deploy.sh
```

Run the deployment:
```bash
sudo ./test.deploy/deploy.sh
```

The script will:
1. Install system dependencies
2. Configure MySQL database
3. Setup SSL certificates
4. Configure Nginx
5. Install application dependencies
6. Run database migrations
7. Start PM2 processes
8. Configure monitoring and backups

### **Step 4: Verify Testing Deployment**

Check if all services are running:
```bash
sudo systemctl status nginx mysql redis-server
pm2 status
```

Test the website:
```bash
curl -I https://test.shopnirvanaorganics.com
curl https://test.shopnirvanaorganics.com/api/health
```

Both should return successful responses.

## 🏭 **Production Environment Deployment**

### **Step 1: Prepare Production Environment**

Copy and edit the production environment file:
```bash
cp deploy/.env.production .env.production
nano .env.production
```

**Critical Production Environment Variables:**
```bash
# Database (Use different passwords from testing)
DB_PASSWORD=your-super-secure-production-db-password

# JWT Secrets (Generate NEW 64-character random strings)
JWT_SECRET=your-ultra-secure-jwt-secret-production-64-chars-different-from-test
JWT_REFRESH_SECRET=your-ultra-secure-refresh-secret-production-64-chars-different-from-test
SESSION_SECRET=your-ultra-secure-session-secret-production-64-chars-different-from-test

# Email Configuration (Production)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-email-password

# Square (Production)
SQUARE_ACCESS_TOKEN=your-production-access-token
SQUARE_APPLICATION_ID=your-production-application-id
SQUARE_WEBHOOK_SIGNATURE_KEY=your-production-webhook-key
SQUARE_ENVIRONMENT=production

# Google OAuth (Production)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret

# Social Media APIs (Production)
FACEBOOK_CLIENT_ID=your-facebook-production-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-production-app-secret
TWITTER_CLIENT_ID=your-twitter-production-client-id
TWITTER_CLIENT_SECRET=your-twitter-production-client-secret
# ... (add all other production social media credentials)
```

### **Step 2: Run Production Deployment Script**

Make the deployment script executable:
```bash
chmod +x deploy/deploy.sh
```

Run the production deployment:
```bash
sudo ./deploy/deploy.sh
```

The production script includes additional security hardening:
1. Enhanced firewall configuration
2. Fail2ban setup for intrusion prevention
3. Automated security updates
4. Performance optimizations
5. Comprehensive monitoring setup
6. Automated backup configuration

### **Step 3: Verify Production Deployment**

Check all services:
```bash
sudo systemctl status nginx mysql redis-server fail2ban
pm2 status
```

Test the production website:
```bash
curl -I https://shopnirvanaorganics.com
curl https://shopnirvanaorganics.com/api/health
```

Test SSL rating:
```bash
curl -I https://www.ssllabs.com/ssltest/analyze.html?d=shopnirvanaorganics.com
```

## 📱 **Social Media Integration Setup**

### **Step 1: Facebook Integration**

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app for "Business"
3. Add "Facebook Login" and "Pages API" products
4. Configure OAuth redirect URI: `https://shopnirvanaorganics.com/api/admin/social-media/callback/facebook`
5. Get App ID and App Secret
6. Add to environment variables

### **Step 2: Twitter/X Integration**

1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Create a new project and app
3. Enable OAuth 2.0
4. Configure callback URL: `https://shopnirvanaorganics.com/api/admin/social-media/callback/twitter`
5. Get Client ID and Client Secret
6. Add to environment variables

### **Step 3: Instagram Integration**

1. Use Facebook Developer account
2. Add Instagram Basic Display product
3. Configure OAuth redirect URI: `https://shopnirvanaorganics.com/api/admin/social-media/callback/instagram`
4. Get Client ID and Client Secret

### **Step 4: LinkedIn Integration**

1. Go to [LinkedIn Developer Portal](https://www.linkedin.com/developers/)
2. Create a new app
3. Add "Sign In with LinkedIn" product
4. Configure redirect URL: `https://shopnirvanaorganics.com/api/admin/social-media/callback/linkedin`
5. Get Client ID and Client Secret

### **Step 5: YouTube Integration**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or use existing
3. Enable YouTube Data API v3
4. Create OAuth 2.0 credentials
5. Configure redirect URI: `https://shopnirvanaorganics.com/api/admin/social-media/callback/youtube`
6. Get Client ID and Client Secret

### **Step 6: Pinterest Integration**

1. Go to [Pinterest Developers](https://developers.pinterest.com/)
2. Create a new app
3. Configure redirect URI: `https://shopnirvanaorganics.com/api/admin/social-media/callback/pinterest`
4. Get Client ID and Client Secret

### **Step 7: Update Environment Variables**

Add all social media credentials to both `.env.testing` and `.env.production` files, then restart the applications:

```bash
# For testing
pm2 restart ecosystem.config.js --env testing

# For production
pm2 restart ecosystem.config.js --env production
```

## ✅ **Post-Deployment Verification**

### **Step 1: Comprehensive Testing Checklist**

#### **Website Functionality**
- [ ] Homepage loads correctly
- [ ] Product pages display properly
- [ ] Shopping cart functionality works
- [ ] Checkout process completes successfully
- [ ] User registration and login work
- [ ] Admin panel is accessible
- [ ] Search functionality works
- [ ] Contact forms submit successfully

#### **API Endpoints**
- [ ] `/api/health` returns 200 OK
- [ ] `/api/products` returns product data
- [ ] `/api/auth/login` accepts credentials
- [ ] `/api/orders` processes orders
- [ ] `/api/admin/*` requires authentication

#### **Security Features**
- [ ] HTTPS redirects work properly
- [ ] SSL certificate is valid (A+ rating)
- [ ] Admin panel requires authentication
- [ ] Rate limiting is active
- [ ] Firewall rules are applied
- [ ] Fail2ban is monitoring

#### **Performance Checks**
- [ ] Page load times < 3 seconds
- [ ] Images are optimized and loading
- [ ] CDN is working (if configured)
- [ ] Database queries are optimized
- [ ] Caching is active

#### **Social Media Integration**
- [ ] Social media icons display correctly
- [ ] Social sharing buttons work
- [ ] Admin can connect social accounts
- [ ] Posts can be scheduled and published
- [ ] Analytics data is collected

### **Step 2: Load Testing**

Install and run basic load testing:
```bash
npm install -g loadtest
loadtest -n 100 -c 10 https://shopnirvanaorganics.com
```

### **Step 3: Backup Verification**

Test backup functionality:
```bash
# Test database backup
sudo /usr/local/bin/nirvana-backup quick

# Test full backup
sudo /usr/local/bin/nirvana-backup full

# Verify backup files exist
ls -la /var/www/nirvana-backend/backups/
```

## 🔄 **Maintenance and Updates**

### **Daily Maintenance Tasks**

Automated via cron jobs:
- Database backups
- Log rotation
- SSL certificate checks
- System monitoring
- Social media analytics collection

### **Weekly Maintenance Tasks**

Run manually or via cron:
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Clean up logs and temporary files
sudo /usr/local/bin/nirvana-maintenance cleanup

# Generate maintenance report
sudo /usr/local/bin/nirvana-maintenance report
```

### **Monthly Maintenance Tasks**

```bash
# Full system maintenance
sudo /usr/local/bin/nirvana-maintenance full

# Review security logs
sudo fail2ban-client status
sudo grep "Failed password" /var/log/auth.log | tail -20

# Update SSL certificates if needed
sudo certbot renew --dry-run
```

### **Application Updates**

#### **Testing Environment Updates**
```bash
cd /var/www/nirvana-test
sudo -u nirvana git pull origin testing
sudo -u nirvana npm install --production
sudo -u nirvana npm run build:test
sudo -u nirvana npm run migrate:test
pm2 reload all
```

#### **Production Environment Updates**
```bash
# Create backup first
sudo /usr/local/bin/nirvana-backup full

cd /var/www/nirvana-backend
sudo -u nirvana git pull origin main
sudo -u nirvana npm ci --production
sudo -u nirvana npm run build:prod
sudo -u nirvana npm run migrate:prod
pm2 reload all --update-env

# Verify update
curl https://shopnirvanaorganics.com/api/health
```

## 🚨 **Troubleshooting**

### **Common Issues and Solutions**

#### **Website Not Loading**
```bash
# Check Nginx status
sudo systemctl status nginx

# Check Nginx configuration
sudo nginx -t

# Check SSL certificates
sudo certbot certificates

# Check DNS resolution
dig shopnirvanaorganics.com
```

#### **Database Connection Issues**
```bash
# Check MySQL status
sudo systemctl status mysql

# Test database connection
mysql -u nirvana_prod_user -p nirvana_organics_production

# Check database logs
sudo tail -f /var/log/mysql/error.log
```

#### **Application Errors**
```bash
# Check PM2 processes
pm2 status
pm2 logs

# Check application logs
tail -f /var/www/nirvana-backend/logs/error.log

# Restart application
pm2 restart all
```

#### **SSL Certificate Issues**
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates manually
sudo certbot renew

# Test SSL configuration
openssl s_client -connect shopnirvanaorganics.com:443
```

#### **High Resource Usage**
```bash
# Check system resources
htop
iotop
nethogs

# Check PM2 monitoring
pm2 monit

# Restart high-memory processes
pm2 restart <process-name>
```

### **Log File Locations**

- **Application Logs**: `/var/www/nirvana-backend/logs/`
- **Nginx Logs**: `/var/log/nginx/`
- **MySQL Logs**: `/var/log/mysql/`
- **System Logs**: `journalctl -u <service-name>`
- **PM2 Logs**: `pm2 logs`

## 🔄 **Rollback Procedures**

### **Emergency Rollback Steps**

#### **Step 1: Stop Current Application**
```bash
pm2 stop all
```

#### **Step 2: Restore from Backup**
```bash
# Restore database
sudo /usr/local/bin/nirvana-backup restore /path/to/backup.sql.gz

# Restore application files
cd /var/www/nirvana-backend
sudo -u nirvana git checkout <previous-commit-hash>
sudo -u nirvana npm ci --production
sudo -u nirvana npm run build:prod
```

#### **Step 3: Restart Services**
```bash
pm2 start ecosystem.config.js --env production
sudo systemctl restart nginx
```

#### **Step 4: Verify Rollback**
```bash
curl https://shopnirvanaorganics.com/api/health
```

### **Gradual Rollback (Recommended)**

1. **Switch to maintenance mode**
2. **Create current state backup**
3. **Restore previous version**
4. **Test functionality**
5. **Switch back to live mode**

## 📞 **Support and Monitoring**

### **Monitoring Setup**

The deployment includes:
- **System monitoring**: CPU, memory, disk usage
- **Application monitoring**: PM2 process monitoring
- **Security monitoring**: Fail2ban intrusion detection
- **SSL monitoring**: Certificate expiration alerts
- **Backup monitoring**: Automated backup verification

### **Alert Notifications**

Configure email alerts for:
- System resource thresholds exceeded
- Application crashes or restarts
- Security incidents detected
- SSL certificate expiration warnings
- Backup failures

### **Performance Monitoring**

Access monitoring dashboards:
- **PM2 Monitoring**: `pm2 monit`
- **System Resources**: `htop`, `iotop`, `nethogs`
- **Application Logs**: `tail -f /var/www/nirvana-backend/logs/combined.log`

## 🎯 **Success Verification**

Your deployment is successful when:

✅ **All services are running**: Nginx, MySQL, Redis, PM2 processes
✅ **Website loads correctly**: Both HTTP redirects to HTTPS
✅ **SSL certificate is valid**: A+ rating on SSL Labs
✅ **API endpoints respond**: Health check returns 200 OK
✅ **Admin panel is accessible**: Authentication works properly
✅ **Social media integration works**: Can connect accounts and post
✅ **Backups are functional**: Daily automated backups complete
✅ **Monitoring is active**: All monitoring scripts are running
✅ **Security is configured**: Firewall and fail2ban are active

## 📚 **Additional Resources**

- [PM2 Documentation](https://pm2.keymetrics.io/docs/)
- [Nginx Documentation](https://nginx.org/en/docs/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [MySQL Documentation](https://dev.mysql.com/doc/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)
- [Ubuntu Server Guide](https://ubuntu.com/server/docs)

---

**🎉 Congratulations! Your Nirvana Organics e-commerce platform is now fully deployed and ready for business!**

For ongoing support and updates, refer to the maintenance procedures above and monitor the system regularly using the provided tools and scripts.
