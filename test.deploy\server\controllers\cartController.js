const { Cart, CartItem, Product } = require('../models');
const { validationResult } = require('express-validator');

// Get user's cart with product details
const getCart = async (req, res) => {
  try {
    const cart = await Cart.findOrCreateForUser(req.user.id);

    // Get product details for cart items
    const cartWithProducts = await populateCartItems(cart);

    res.json({
      success: true,
      data: cartWithProducts
    });

  } catch (error) {
    console.error('Get cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cart',
      error: error.message
    });
  }
};

// Helper function to populate cart items with product details
const populateCartItems = async (cart) => {
  if (!cart.items || cart.items.length === 0) {
    return cart;
  }

  const productIds = cart.items.map(item => item.productId);
  const products = await Product.findAll({
    where: { id: productIds },
    attributes: ['id', 'name', 'slug', 'images', 'price', 'trackQuantity', 'quantity', 'isActive']
  });

  const productMap = {};
  products.forEach(product => {
    productMap[product.id] = product;
  });

  // Add product details to cart items
  const cartData = cart.toJSON();
  cartData.items = cartData.items.map(item => ({
    ...item,
    product: productMap[item.productId] || null
  }));

  return cartData;
};

// Add item to cart
const addToCart = async (req, res) => {
  console.log('🔍 DEBUG: addToCart method called');
  console.log('🔍 DEBUG: req.body:', req.body);
  console.log('🔍 DEBUG: req.user:', req.user);
  try {
    const { productId, variant, quantity = 1 } = req.body;

    // Validate product exists and is active
    const product = await Product.findOne({ where: { id: productId, isActive: true } });
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found or not available'
      });
    }

    // Check stock availability
    if (product.trackQuantity && product.quantity < quantity) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient stock available'
      });
    }

    // Get or create cart
    const cart = await Cart.findOrCreateForUser(req.user.id);

    // Determine price (variant price or product price)
    let price = product.price;
    if (variant && variant.price) {
      price = variant.price;
    }

    // Add item to cart
    await cart.addItem(productId, variant, quantity, price);

    // Get cart with product details
    const cartWithProducts = await populateCartItems(cart);

    res.json({
      success: true,
      message: 'Item added to cart successfully',
      data: cartWithProducts
    });

  } catch (error) {
    console.error('Add to cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add item to cart',
      error: error.message
    });
  }
};

// Update cart item quantity
const updateCartItem = async (req, res) => {
  try {
    const { itemId } = req.params;
    const { quantity } = req.body;

    if (quantity < 0) {
      return res.status(400).json({
        success: false,
        message: 'Quantity cannot be negative'
      });
    }

    const cart = await Cart.findOne({ where: { userId: req.user.id } });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    const itemIndex = parseInt(itemId);
    const items = cart.items || [];

    if (itemIndex < 0 || itemIndex >= items.length) {
      return res.status(404).json({
        success: false,
        message: 'Cart item not found'
      });
    }

    const cartItem = items[itemIndex];

    // Check stock availability if increasing quantity
    if (quantity > cartItem.quantity) {
      const product = await Product.findByPk(cartItem.productId);
      if (product && product.trackQuantity && product.quantity < quantity) {
        return res.status(400).json({
          success: false,
          message: 'Insufficient stock available'
        });
      }
    }

    // Update quantity
    await cart.updateItemQuantity(itemIndex, quantity);

    // Populate cart items with product details
    await cart.populate('items.product', 'name slug images price trackQuantity quantity');

    res.json({
      success: true,
      message: 'Cart item updated successfully',
      data: { cart }
    });

  } catch (error) {
    console.error('Update cart item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update cart item',
      error: error.message
    });
  }
};

// Remove item from cart
const removeFromCart = async (req, res) => {
  try {
    const { itemId } = req.params;

    const cart = await Cart.findOne({ where: { userId: req.user.id } });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    const itemIndex = parseInt(itemId);

    // Remove item
    await cart.removeItem(itemIndex);

    res.json({
      success: true,
      message: 'Item removed from cart successfully',
      data: { cart }
    });

  } catch (error) {
    console.error('Remove from cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove item from cart',
      error: error.message
    });
  }
};

// Clear entire cart
const clearCart = async (req, res) => {
  try {
    const cart = await Cart.findOne({ where: { userId: req.user.id } });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    await cart.clearCart();

    res.json({
      success: true,
      message: 'Cart cleared successfully',
      data: { cart }
    });

  } catch (error) {
    console.error('Clear cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear cart',
      error: error.message
    });
  }
};

// Apply coupon to cart
const applyCoupon = async (req, res) => {
  try {
    const { couponCode } = req.body;

    const cart = await Cart.findOne({ where: { userId: req.user.id } });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    // TODO: Implement coupon validation logic
    // For now, we'll apply a simple discount
    let discount = 0;
    if (couponCode === 'WELCOME10') {
      discount = cart.subtotal * 0.1; // 10% discount
    } else if (couponCode === 'SAVE20') {
      discount = cart.subtotal * 0.2; // 20% discount
    } else {
      return res.status(400).json({
        success: false,
        message: 'Invalid coupon code'
      });
    }

    cart.couponCode = couponCode;
    cart.discount = discount;
    await cart.save();

    res.json({
      success: true,
      message: 'Coupon applied successfully',
      data: { cart }
    });

  } catch (error) {
    console.error('Apply coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to apply coupon',
      error: error.message
    });
  }
};

// Remove coupon from cart
const removeCoupon = async (req, res) => {
  try {
    const cart = await Cart.findOne({ where: { userId: req.user.id } });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    cart.couponCode = null;
    cart.discount = 0;
    await cart.save();

    res.json({
      success: true,
      message: 'Coupon removed successfully',
      data: { cart }
    });

  } catch (error) {
    console.error('Remove coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove coupon',
      error: error.message
    });
  }
};

// Get cart summary (item count and total)
const getCartSummary = async (req, res) => {
  try {
    const cart = await Cart.findOne({ where: { userId: req.user.id } });

    const summary = {
      itemCount: cart ? cart.getItemCount() : 0,
      total: cart ? cart.total : 0
    };

    res.json({
      success: true,
      data: { summary }
    });

  } catch (error) {
    console.error('Get cart summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cart summary',
      error: error.message
    });
  }
};

// Update cart item by productId (alternative endpoint)
const updateCartItemByProduct = async (req, res) => {
  try {
    const { productId, quantity } = req.body;
    const userId = req.user.id;

    const cart = await Cart.findOrCreateForUser(userId);

    // Get cart items from database
    const cartItems = await CartItem.findAll({ where: { cartId: cart.id } });
    const cartItem = cartItems.find(item => item.productId === parseInt(productId));

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        message: 'Item not found in cart'
      });
    }

    // Update the cart item quantity
    cartItem.quantity = quantity;
    cartItem.total = cartItem.quantity * cartItem.price;
    await cartItem.save();

    // Fetch fresh cart items from database after update
    const freshCartItems = await CartItem.findAll({ where: { cartId: cart.id } });
    cart.dataValues.items = freshCartItems;

    const cartWithProducts = await populateCartItems(cart);

    res.json({
      success: true,
      message: 'Cart item updated successfully',
      data: cartWithProducts
    });
  } catch (error) {
    console.error('Update cart item by product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update cart item',
      error: error.message
    });
  }
};

// Remove item from cart by productId (alternative endpoint)
const removeFromCartByProduct = async (req, res) => {
  try {
    const { productId } = req.params;
    const userId = req.user.id;

    const cart = await Cart.findOrCreateForUser(userId);

    // Find and remove the cart item from database
    const cartItem = await CartItem.findOne({
      where: {
        cartId: cart.id,
        productId: parseInt(productId)
      }
    });

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        message: 'Item not found in cart'
      });
    }

    // Remove the cart item
    await cartItem.destroy();

    const cartWithProducts = await populateCartItems(cart);

    res.json({
      success: true,
      message: 'Item removed from cart successfully',
      data: cartWithProducts
    });
  } catch (error) {
    console.error('Remove from cart by product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove item from cart',
      error: error.message
    });
  }
};

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  updateCartItemByProduct,
  removeFromCart,
  removeFromCartByProduct,
  clearCart,
  applyCoupon,
  removeCoupon,
  getCartSummary
};
