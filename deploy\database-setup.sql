-- Database Setup Script for Production Environment
-- shopnirvanaorganics.com

-- Create production database with optimized settings
CREATE DATABASE IF NOT EXISTS nirvana_organics_production
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- Create production user with strong password
CREATE USER IF NOT EXISTS 'nirvana_prod_user'@'localhost' IDENTIFIED BY 'your-super-secure-production-db-password';

-- Grant necessary privileges to production user
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON nirvana_organics_production.* TO 'nirvana_prod_user'@'localhost';

-- <PERSON>reate backup user for automated backups
CREATE USER IF NOT EXISTS 'nirvana_backup_prod'@'localhost' IDENTIFIED BY 'your-super-secure-backup-password';
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON nirvana_organics_production.* TO 'nirvana_backup_prod'@'localhost';

-- <PERSON>reate read-only user for reporting/analytics (optional)
CREATE USER IF NOT EXISTS 'nirvana_readonly'@'localhost' IDENTIFIED BY 'your-readonly-user-password';
GRANT SELECT ON nirvana_organics_production.* TO 'nirvana_readonly'@'localhost';

-- Create monitoring user for health checks
CREATE USER IF NOT EXISTS 'nirvana_monitor'@'localhost' IDENTIFIED BY 'your-monitor-user-password';
GRANT SELECT ON nirvana_organics_production.* TO 'nirvana_monitor'@'localhost';
GRANT PROCESS ON *.* TO 'nirvana_monitor'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the production database
USE nirvana_organics_production;

-- Optimize database settings for production
SET GLOBAL innodb_buffer_pool_size = **********; -- 1GB, adjust based on available RAM
SET GLOBAL innodb_log_file_size = 268435456; -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 1; -- ACID compliance
SET GLOBAL innodb_file_per_table = 1;
SET GLOBAL innodb_flush_method = 'O_DIRECT';

-- Set query cache (if using MySQL 5.7 or earlier)
-- SET GLOBAL query_cache_type = 1;
-- SET GLOBAL query_cache_size = 67108864; -- 64MB

-- Set connection limits
SET GLOBAL max_connections = 200;
SET GLOBAL max_user_connections = 50;

-- Set timeout values
SET GLOBAL wait_timeout = 28800; -- 8 hours
SET GLOBAL interactive_timeout = 28800; -- 8 hours

-- Enable slow query log for monitoring
SET GLOBAL slow_query_log = 1;
SET GLOBAL long_query_time = 2; -- Log queries taking more than 2 seconds
SET GLOBAL log_queries_not_using_indexes = 1;

-- Create indexes for common queries (will be created by migrations, but listed here for reference)
-- These will be created by Sequelize migrations, but documented here for database optimization

-- Performance monitoring table (optional)
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at)
) ENGINE=InnoDB;

-- Database maintenance procedures
DELIMITER //

-- Procedure to optimize all tables
CREATE PROCEDURE OptimizeAllTables()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(255);
    DECLARE cur CURSOR FOR 
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = 'nirvana_organics_production' 
        AND TABLE_TYPE = 'BASE TABLE';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET @sql = CONCAT('OPTIMIZE TABLE ', table_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END LOOP;
    CLOSE cur;
END//

-- Procedure to analyze all tables
CREATE PROCEDURE AnalyzeAllTables()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(255);
    DECLARE cur CURSOR FOR 
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = 'nirvana_organics_production' 
        AND TABLE_TYPE = 'BASE TABLE';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET @sql = CONCAT('ANALYZE TABLE ', table_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END LOOP;
    CLOSE cur;
END//

-- Procedure to get database statistics
CREATE PROCEDURE GetDatabaseStats()
BEGIN
    SELECT 
        TABLE_NAME,
        TABLE_ROWS,
        ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
        ROUND((DATA_LENGTH / 1024 / 1024), 2) AS 'Data (MB)',
        ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index (MB)'
    FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = 'nirvana_organics_production'
    ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;
END//

DELIMITER ;

-- Grant execute permissions on procedures
GRANT EXECUTE ON PROCEDURE nirvana_organics_production.OptimizeAllTables TO 'nirvana_prod_user'@'localhost';
GRANT EXECUTE ON PROCEDURE nirvana_organics_production.AnalyzeAllTables TO 'nirvana_prod_user'@'localhost';
GRANT EXECUTE ON PROCEDURE nirvana_organics_production.GetDatabaseStats TO 'nirvana_prod_user'@'localhost';
GRANT EXECUTE ON PROCEDURE nirvana_organics_production.GetDatabaseStats TO 'nirvana_readonly'@'localhost';

-- Create event scheduler for automatic maintenance (runs weekly)
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS weekly_maintenance
ON SCHEDULE EVERY 1 WEEK
STARTS '2024-01-07 02:00:00' -- Sunday at 2 AM
DO
BEGIN
    CALL OptimizeAllTables();
    CALL AnalyzeAllTables();
END;

-- Show created database and users
SELECT 'Production database created successfully' as Status;
SHOW DATABASES LIKE 'nirvana_organics_production';

SELECT 'Users created successfully' as Status;
SELECT User, Host FROM mysql.user WHERE User LIKE 'nirvana_%';

-- Show granted privileges
SELECT 'Privileges granted successfully' as Status;
SHOW GRANTS FOR 'nirvana_prod_user'@'localhost';
SHOW GRANTS FOR 'nirvana_backup_prod'@'localhost';
SHOW GRANTS FOR 'nirvana_readonly'@'localhost';
SHOW GRANTS FOR 'nirvana_monitor'@'localhost';

-- Show database configuration
SELECT 'Database configuration completed' as Status;
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'max_connections';
SHOW VARIABLES LIKE 'slow_query_log';
