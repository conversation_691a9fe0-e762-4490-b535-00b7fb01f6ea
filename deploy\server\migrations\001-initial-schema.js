/**
 * Initial Database Schema Migration for Nirvana Organics
 * Creates all the basic tables required for the e-commerce platform
 */

const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('🔄 Running initial schema migration...');

      // Create roles table
      await queryInterface.createTable('roles', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        permissions: {
          type: DataTypes.JSON,
          allowNull: true
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      // Create Users table
      await queryInterface.createTable('Users', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        firstName: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'first_name'
        },
        lastName: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'last_name'
        },
        email: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true
        },
        password: {
          type: DataTypes.STRING,
          allowNull: true // Allow null for social authentication users
        },
        dateOfBirth: {
          type: DataTypes.DATEONLY,
          allowNull: true,
          field: 'date_of_birth'
        },
        phoneNumber: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'phone_number'
        },
        isVerified: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          field: 'is_verified'
        },
        verificationToken: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'verification_token'
        },
        resetPasswordToken: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'reset_password_token'
        },
        resetPasswordExpires: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'reset_password_expires'
        },
        lastLoginAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'last_login_at'
        },
        roleId: {
          type: DataTypes.INTEGER,
          allowNull: false,
          field: 'role_id',
          references: {
            model: 'roles',
            key: 'id'
          }
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      // Create Categories table
      await queryInterface.createTable('Categories', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false
        },
        slug: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        image: {
          type: DataTypes.STRING,
          allowNull: true
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          field: 'is_active'
        },
        sortOrder: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
          field: 'sort_order'
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      // Create Products table
      await queryInterface.createTable('Products', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false
        },
        slug: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        shortDescription: {
          type: DataTypes.TEXT,
          allowNull: true,
          field: 'short_description'
        },
        price: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: false
        },
        comparePrice: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: true,
          field: 'compare_price'
        },
        sku: {
          type: DataTypes.STRING,
          allowNull: true,
          unique: true
        },
        barcode: {
          type: DataTypes.STRING,
          allowNull: true
        },
        trackQuantity: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          field: 'track_quantity'
        },
        quantity: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        lowStockThreshold: {
          type: DataTypes.INTEGER,
          defaultValue: 10,
          field: 'low_stock_threshold'
        },
        weight: {
          type: DataTypes.DECIMAL(8, 2),
          allowNull: true
        },
        dimensions: {
          type: DataTypes.JSON,
          allowNull: true
        },
        images: {
          type: DataTypes.JSON,
          allowNull: true
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          field: 'is_active'
        },
        isFeatured: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          field: 'is_featured'
        },
        categoryId: {
          type: DataTypes.INTEGER,
          allowNull: false,
          field: 'category_id',
          references: {
            model: 'Categories',
            key: 'id'
          }
        },
        tags: {
          type: DataTypes.JSON,
          allowNull: true
        },
        seoTitle: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'seo_title'
        },
        seoDescription: {
          type: DataTypes.TEXT,
          allowNull: true,
          field: 'seo_description'
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      // Create Orders table
      await queryInterface.createTable('Orders', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        orderNumber: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          field: 'order_number'
        },
        userId: {
          type: DataTypes.INTEGER,
          allowNull: false,
          field: 'user_id',
          references: {
            model: 'Users',
            key: 'id'
          }
        },
        status: {
          type: DataTypes.ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled'),
          defaultValue: 'pending'
        },
        subtotal: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: false
        },
        taxAmount: {
          type: DataTypes.DECIMAL(10, 2),
          defaultValue: 0,
          field: 'tax_amount'
        },
        shippingAmount: {
          type: DataTypes.DECIMAL(10, 2),
          defaultValue: 0,
          field: 'shipping_amount'
        },
        discountAmount: {
          type: DataTypes.DECIMAL(10, 2),
          defaultValue: 0,
          field: 'discount_amount'
        },
        totalAmount: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: false,
          field: 'total_amount'
        },
        paymentStatus: {
          type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded'),
          defaultValue: 'pending',
          field: 'payment_status'
        },
        paymentMethod: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'payment_method'
        },
        shippingAddress: {
          type: DataTypes.JSON,
          allowNull: false,
          field: 'shipping_address'
        },
        billingAddress: {
          type: DataTypes.JSON,
          allowNull: false,
          field: 'billing_address'
        },
        notes: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      console.log('✅ Initial schema migration completed successfully');
    } catch (error) {
      console.error('❌ Error running initial schema migration:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      console.log('🔄 Reverting initial schema migration...');

      // Drop tables in reverse order to handle foreign key constraints
      await queryInterface.dropTable('Orders');
      await queryInterface.dropTable('Products');
      await queryInterface.dropTable('Categories');
      await queryInterface.dropTable('Users');
      await queryInterface.dropTable('roles');

      console.log('✅ Initial schema migration reverted successfully');
    } catch (error) {
      console.error('❌ Error reverting initial schema migration:', error);
      throw error;
    }
  }
};
