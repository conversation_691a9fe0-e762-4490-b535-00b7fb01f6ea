#!/bin/bash

# SSL Certificate Setup Script for Production Environment
# shopnirvanaorganics.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="shopnirvanaorganics.com"
WWW_DOMAIN="www.shopnirvanaorganics.com"
EMAIL="<EMAIL>"
WEBROOT="/var/www/nirvana-backend/dist"
SSL_DIR="/etc/ssl"
CERT_PATH="$SSL_DIR/certs"
KEY_PATH="$SSL_DIR/private"

echo -e "${BLUE}🔒 SSL Certificate Setup for Production${NC}"
echo "=================================================="
echo -e "Primary Domain: ${GREEN}$DOMAIN${NC}"
echo -e "WWW Domain: ${GREEN}$WWW_DOMAIN${NC}"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   exit 1
fi

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Install Certbot if not already installed
install_certbot() {
    log_info "Installing Certbot..."
    
    if command -v certbot &> /dev/null; then
        log_info "Certbot is already installed"
        return
    fi
    
    # Update package list
    apt update
    
    # Install snapd if not installed
    if ! command -v snap &> /dev/null; then
        apt install -y snapd
        systemctl enable snapd
        systemctl start snapd
        sleep 5
    fi
    
    # Install certbot via snap
    snap install core; snap refresh core
    snap install --classic certbot
    
    # Create symlink
    ln -sf /snap/bin/certbot /usr/bin/certbot
    
    log_info "Certbot installed successfully"
}

# Create SSL directories
create_ssl_directories() {
    log_info "Creating SSL directories..."
    
    mkdir -p "$CERT_PATH"
    mkdir -p "$KEY_PATH"
    
    # Set proper permissions
    chmod 755 "$CERT_PATH"
    chmod 700 "$KEY_PATH"
    
    log_info "SSL directories created"
}

# Create webroot directory
create_webroot() {
    log_info "Creating webroot directory..."
    
    mkdir -p "$WEBROOT"
    chown -R www-data:www-data "$WEBROOT"
    
    log_info "Webroot directory created: $WEBROOT"
}

# Obtain SSL certificate for both domains
obtain_certificate() {
    log_info "Obtaining SSL certificate for $DOMAIN and $WWW_DOMAIN..."
    
    # Stop nginx temporarily
    systemctl stop nginx || true
    
    # Obtain certificate using standalone mode for both domains
    certbot certonly \
        --standalone \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        --domains "$DOMAIN,$WWW_DOMAIN" \
        --non-interactive
    
    if [ $? -eq 0 ]; then
        log_info "SSL certificate obtained successfully"
    else
        log_error "Failed to obtain SSL certificate"
        exit 1
    fi
}

# Copy certificates to custom location
copy_certificates() {
    log_info "Copying certificates to custom location..."
    
    # Copy certificate files
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$CERT_PATH/$DOMAIN.crt"
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$KEY_PATH/$DOMAIN.key"
    
    # Set proper permissions
    chmod 644 "$CERT_PATH/$DOMAIN.crt"
    chmod 600 "$KEY_PATH/$DOMAIN.key"
    chown root:root "$CERT_PATH/$DOMAIN.crt"
    chown root:root "$KEY_PATH/$DOMAIN.key"
    
    log_info "Certificates copied to custom location"
}

# Setup auto-renewal with enhanced monitoring
setup_auto_renewal() {
    log_info "Setting up automatic certificate renewal..."
    
    # Create renewal script with logging
    cat > /usr/local/bin/renew-ssl-production.sh << 'EOF'
#!/bin/bash

# Production SSL Renewal Script
LOG_FILE="/var/log/ssl-renewal.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] Starting SSL certificate renewal check..." >> $LOG_FILE

# Renew certificates
if certbot renew --quiet --no-self-upgrade; then
    echo "[$DATE] Certificate renewal check completed successfully" >> $LOG_FILE
    
    # Copy renewed certificates if they exist
    if [ -f "/etc/letsencrypt/live/shopnirvanaorganics.com/fullchain.pem" ]; then
        cp "/etc/letsencrypt/live/shopnirvanaorganics.com/fullchain.pem" "/etc/ssl/certs/shopnirvanaorganics.com.crt"
        cp "/etc/letsencrypt/live/shopnirvanaorganics.com/privkey.pem" "/etc/ssl/private/shopnirvanaorganics.com.key"
        
        # Set permissions
        chmod 644 "/etc/ssl/certs/shopnirvanaorganics.com.crt"
        chmod 600 "/etc/ssl/private/shopnirvanaorganics.com.key"
        
        # Test nginx configuration
        if nginx -t; then
            # Reload nginx
            systemctl reload nginx
            echo "[$DATE] SSL certificates updated and nginx reloaded successfully" >> $LOG_FILE
        else
            echo "[$DATE] ERROR: Nginx configuration test failed after certificate renewal" >> $LOG_FILE
        fi
    fi
else
    echo "[$DATE] ERROR: Certificate renewal failed" >> $LOG_FILE
fi

# Clean up old log entries (keep last 30 days)
find /var/log -name "ssl-renewal.log" -mtime +30 -delete 2>/dev/null || true
EOF

    chmod +x /usr/local/bin/renew-ssl-production.sh
    
    # Add cron job for automatic renewal (twice daily for production)
    (crontab -l 2>/dev/null; echo "0 2,14 * * * /usr/local/bin/renew-ssl-production.sh") | crontab -
    
    # Create log rotation for SSL renewal logs
    cat > /etc/logrotate.d/ssl-renewal << 'EOF'
/var/log/ssl-renewal.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF
    
    log_info "Auto-renewal setup completed (runs twice daily)"
}

# Setup SSL monitoring
setup_ssl_monitoring() {
    log_info "Setting up SSL certificate monitoring..."
    
    # Create SSL monitoring script
    cat > /usr/local/bin/monitor-ssl.sh << 'EOF'
#!/bin/bash

# SSL Certificate Monitoring Script
DOMAIN="shopnirvanaorganics.com"
CERT_FILE="/etc/ssl/certs/$DOMAIN.crt"
LOG_FILE="/var/log/ssl-monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check certificate expiration
if [ -f "$CERT_FILE" ]; then
    EXPIRY_DATE=$(openssl x509 -in "$CERT_FILE" -noout -enddate | cut -d= -f2)
    EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s)
    CURRENT_TIMESTAMP=$(date +%s)
    DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_TIMESTAMP - $CURRENT_TIMESTAMP) / 86400 ))
    
    if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
        echo "[$DATE] WARNING: SSL certificate expires in $DAYS_UNTIL_EXPIRY days" >> $LOG_FILE
        # Send alert email (configure your email settings)
        # echo "SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days" | mail -s "SSL Certificate Expiry Warning" <EMAIL>
    elif [ $DAYS_UNTIL_EXPIRY -lt 7 ]; then
        echo "[$DATE] CRITICAL: SSL certificate expires in $DAYS_UNTIL_EXPIRY days" >> $LOG_FILE
        # Send critical alert
    else
        echo "[$DATE] SSL certificate is valid for $DAYS_UNTIL_EXPIRY more days" >> $LOG_FILE
    fi
else
    echo "[$DATE] ERROR: SSL certificate file not found" >> $LOG_FILE
fi
EOF

    chmod +x /usr/local/bin/monitor-ssl.sh
    
    # Add monitoring to cron (daily check)
    (crontab -l 2>/dev/null; echo "0 6 * * * /usr/local/bin/monitor-ssl.sh") | crontab -
    
    log_info "SSL monitoring setup completed"
}

# Verify certificate and setup
verify_certificate() {
    log_info "Verifying SSL certificate..."
    
    if [ -f "$CERT_PATH/$DOMAIN.crt" ] && [ -f "$KEY_PATH/$DOMAIN.key" ]; then
        # Check certificate validity and details
        echo -e "${BLUE}Certificate Details:${NC}"
        openssl x509 -in "$CERT_PATH/$DOMAIN.crt" -text -noout | grep -E "(Subject:|Issuer:|Not After|DNS:)"
        
        # Test certificate chain
        if openssl verify -CAfile "$CERT_PATH/$DOMAIN.crt" "$CERT_PATH/$DOMAIN.crt" &>/dev/null; then
            log_info "Certificate chain verification passed"
        else
            log_warning "Certificate chain verification failed (this may be normal for Let's Encrypt)"
        fi
        
        log_info "SSL certificate verification completed"
    else
        log_error "SSL certificate files not found"
        exit 1
    fi
}

# Main execution
main() {
    log_info "Starting SSL setup for production environment..."
    
    install_certbot
    create_ssl_directories
    create_webroot
    obtain_certificate
    copy_certificates
    setup_auto_renewal
    setup_ssl_monitoring
    verify_certificate
    
    echo ""
    echo -e "${GREEN}✅ Production SSL Certificate Setup Completed!${NC}"
    echo "=================================================="
    echo -e "Primary Domain: ${BLUE}$DOMAIN${NC}"
    echo -e "WWW Domain: ${BLUE}$WWW_DOMAIN${NC}"
    echo -e "Certificate: ${BLUE}$CERT_PATH/$DOMAIN.crt${NC}"
    echo -e "Private Key: ${BLUE}$KEY_PATH/$DOMAIN.key${NC}"
    echo -e "Auto-renewal: ${GREEN}Enabled${NC} (runs twice daily at 2 AM and 2 PM)"
    echo -e "Monitoring: ${GREEN}Enabled${NC} (daily check at 6 AM)"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Update your Nginx configuration to use the SSL certificates"
    echo "2. Start/reload Nginx: systemctl reload nginx"
    echo "3. Test HTTPS access: https://$DOMAIN"
    echo "4. Test WWW redirect: https://$WWW_DOMAIN"
    echo "5. Check SSL rating: https://www.ssllabs.com/ssltest/"
    echo ""
}

# Run main function
main "$@"
