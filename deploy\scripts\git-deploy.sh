#!/bin/bash

# ============================================================================
# Nirvana Organics Backend - Git-Based Deployment Script
# ============================================================================

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_CONFIG="$PROJECT_ROOT/deployment.config.js"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Nirvana Organics Backend - Git-Based Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV    Target environment (development|staging|production)
    -b, --branch BRANCH      Git branch to deploy (default: main)
    -h, --host HOST          Target server hostname or IP
    -u, --user USER          SSH username for deployment
    -p, --path PATH          Deployment path on server
    -t, --test               Run tests before deployment
    -s, --skip-backup        Skip creating backup before deployment
    -r, --rollback           Rollback to previous deployment
    -v, --verbose            Enable verbose output
    --help                   Show this help message

Examples:
    $0 --environment production --host server.com --user root
    $0 -e staging -b develop -t
    $0 --rollback --environment production

EOF
}

# Parse command line arguments
ENVIRONMENT=""
BRANCH="main"
HOST=""
USER=""
DEPLOYMENT_PATH=""
RUN_TESTS=false
SKIP_BACKUP=false
ROLLBACK=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -b|--branch)
            BRANCH="$2"
            shift 2
            ;;
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -u|--user)
            USER="$2"
            shift 2
            ;;
        -p|--path)
            DEPLOYMENT_PATH="$2"
            shift 2
            ;;
        -t|--test)
            RUN_TESTS=true
            shift
            ;;
        -s|--skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        -r|--rollback)
            ROLLBACK=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "Environment is required. Use -e or --environment"
    exit 1
fi

if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "Invalid environment. Must be: development, staging, or production"
    exit 1
fi

# Set default values based on environment
if [[ -z "$DEPLOYMENT_PATH" ]]; then
    case $ENVIRONMENT in
        development)
            DEPLOYMENT_PATH="/var/www/nirvana-backend-dev"
            ;;
        staging)
            DEPLOYMENT_PATH="/var/www/nirvana-backend-staging"
            ;;
        production)
            DEPLOYMENT_PATH="/var/www/nirvana-backend"
            ;;
    esac
fi

log_info "Starting deployment to $ENVIRONMENT environment"
log_info "Branch: $BRANCH"
log_info "Host: $HOST"
log_info "User: $USER"
log_info "Path: $DEPLOYMENT_PATH"

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check if git is clean
if [[ $(git status --porcelain) ]]; then
    log_warning "Working directory is not clean. Uncommitted changes detected."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "Deployment cancelled"
        exit 1
    fi
fi

# Run tests if requested
if [[ "$RUN_TESTS" == true ]]; then
    log_info "Running tests..."
    npm test || {
        log_error "Tests failed. Deployment cancelled."
        exit 1
    }
    log_success "All tests passed"
fi

# Rollback function
rollback_deployment() {
    log_info "Rolling back deployment..."
    
    ssh "$USER@$HOST" << EOF
        cd $DEPLOYMENT_PATH
        if [ -d "backup" ]; then
            rm -rf current
            mv backup current
            pm2 restart nirvana-backend
            echo "✅ Rollback completed successfully"
        else
            echo "❌ No backup found for rollback"
            exit 1
        fi
EOF
}

# Handle rollback request
if [[ "$ROLLBACK" == true ]]; then
    rollback_deployment
    exit 0
fi

# Main deployment function
deploy() {
    log_info "Starting deployment process..."
    
    # Create deployment timestamp
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    RELEASE_DIR="releases/$TIMESTAMP"
    
    ssh "$USER@$HOST" << EOF
        set -e
        
        # Create directory structure
        mkdir -p $DEPLOYMENT_PATH/{releases,shared/{logs,uploads,config}}
        
        # Clone repository
        cd $DEPLOYMENT_PATH
        if [ ! -d "repo" ]; then
            git clone https://github.com/your-username/nirvana-organics-backend.git repo
        fi
        
        # Update repository
        cd repo
        git fetch origin
        git checkout $BRANCH
        git pull origin $BRANCH
        
        # Create new release
        cd $DEPLOYMENT_PATH
        cp -r repo $RELEASE_DIR
        cd $RELEASE_DIR
        
        # Install dependencies
        npm ci --production
        
        # Link shared directories
        ln -sfn $DEPLOYMENT_PATH/shared/uploads uploads
        ln -sfn $DEPLOYMENT_PATH/shared/logs server/logs
        ln -sfn $DEPLOYMENT_PATH/shared/config/.env.production .env
        
        # Run database migrations
        npm run migrate:prod
        
        # Create backup of current deployment
        if [ -d "$DEPLOYMENT_PATH/current" ] && [ "$SKIP_BACKUP" != true ]; then
            cp -r $DEPLOYMENT_PATH/current $DEPLOYMENT_PATH/backup
        fi
        
        # Update current symlink
        ln -sfn $DEPLOYMENT_PATH/$RELEASE_DIR $DEPLOYMENT_PATH/current
        
        # Restart application
        cd $DEPLOYMENT_PATH/current
        pm2 restart ecosystem.config.js --env $ENVIRONMENT || pm2 start ecosystem.config.js --env $ENVIRONMENT
        
        # Cleanup old releases (keep last 5)
        cd $DEPLOYMENT_PATH/releases
        ls -t | tail -n +6 | xargs rm -rf
        
        echo "✅ Deployment completed successfully"
EOF
    
    # Health check
    log_info "Performing health check..."
    sleep 10
    
    if curl -f "http://$HOST:5000/health" > /dev/null 2>&1; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        log_warning "Consider rolling back the deployment"
        exit 1
    fi
    
    log_success "Deployment to $ENVIRONMENT completed successfully!"
}

# Execute deployment
deploy
