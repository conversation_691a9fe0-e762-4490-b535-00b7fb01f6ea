#!/usr/bin/env node

/**
 * Complete Production Deployment Package Creator
 * 
 * This script creates a comprehensive, production-ready deployment package
 * with all necessary files, configurations, and automated setup scripts.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(70));
    console.log(`🚀 ${msg}`);
    console.log('='.repeat(70));
  }
};

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log.info(`Created directory: ${dirPath}`);
  }
}

function copyDirectory(src, dest) {
  ensureDirectoryExists(dest);
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    log.info(`Removed directory: ${dirPath}`);
  }
}

async function createCompleteDeployment() {
  try {
    log.header('Creating Complete Production Deployment Package');
    
    // Clean up existing deployment
    log.info('Cleaning up existing deployment directory...');
    removeDirectory('deployment');
    
    // Create deployment directory structure
    log.header('Creating Deployment Directory Structure');
    const deploymentDir = 'deployment';
    const requiredDirs = [
      'config',
      'scripts',
      'nginx',
      'systemd',
      'ssl',
      'backups',
      'logs',
      'docs'
    ];
    
    ensureDirectoryExists(deploymentDir);
    requiredDirs.forEach(dir => {
      ensureDirectoryExists(path.join(deploymentDir, dir));
    });
    
    // Step 1: Build unified frontend
    log.header('Building Unified Frontend');
    if (!fs.existsSync('dist')) {
      log.info('Building unified frontend...');
      execSync('npm run build:unified', { stdio: 'inherit' });
    }
    
    // Step 2: Copy unified frontend
    log.header('Copying Unified Frontend Build');
    copyDirectory('dist', path.join(deploymentDir, 'frontend'));
    log.success('Copied unified frontend build');
    
    // Step 3: Copy backend server
    log.header('Copying Backend Server');
    copyDirectory('server', path.join(deploymentDir, 'server'));
    log.success('Copied backend server');
    
    // Step 4: Copy and enhance scripts
    log.header('Creating Enhanced Scripts');
    copyDirectory('scripts', path.join(deploymentDir, 'scripts'));

    // Copy PM2 ecosystem configuration
    if (fs.existsSync('ecosystem.config.js')) {
      fs.copyFileSync('ecosystem.config.js', path.join(deploymentDir, 'ecosystem.config.js'));
    }

    log.success('Copied deployment scripts and PM2 configuration');
    
    // Step 5: Create configuration files
    log.header('Creating Configuration Files');
    await createConfigurationFiles(deploymentDir);
    
    // Step 6: Create deployment scripts
    log.header('Creating Deployment Scripts');
    await createDeploymentScripts(deploymentDir);

    // Step 7: Create Nginx and systemd configurations
    log.header('Creating Service Configurations');
    await createServiceConfigurations(deploymentDir);

    // Step 8: Create documentation
    log.header('Creating Documentation');
    await createDocumentation(deploymentDir);

    // Step 9: Create package files
    log.header('Creating Package Files');
    await createPackageFiles(deploymentDir);
    
    // Step 9: Set permissions
    log.header('Setting File Permissions');
    setFilePermissions(deploymentDir);
    
    // Step 10: Validate deployment package
    log.header('Validating Deployment Package');
    await validateDeploymentPackage(deploymentDir);
    
    log.header('Deployment Package Complete');
    log.success('Complete production deployment package created successfully!');
    log.info(`Package location: ${path.resolve(deploymentDir)}`);
    log.info('Package size: ' + calculatePackageSize(deploymentDir));
    
    // Display next steps
    log.header('Next Steps');
    log.info('1. Upload the deployment package to your VPS server');
    log.info('2. Run: chmod +x deploy.sh && ./deploy.sh');
    log.info('3. Follow the interactive setup prompts');
    log.info('4. Access your application at your domain');
    
  } catch (error) {
    log.error(`Deployment package creation failed: ${error.message}`);
    process.exit(1);
  }
}

function calculatePackageSize(dirPath) {
  let size = 0;
  
  function getDirSize(dir) {
    const files = fs.readdirSync(dir);
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        getDirSize(filePath);
      } else {
        size += stats.size;
      }
    }
  }
  
  getDirSize(dirPath);
  return `${(size / 1024 / 1024).toFixed(2)} MB`;
}

function setFilePermissions(deploymentDir) {
  try {
    // Make scripts executable (Unix-like systems)
    const scriptFiles = [
      'deploy.sh',
      'scripts/setup-server.sh',
      'scripts/install-dependencies.sh',
      'scripts/configure-nginx.sh'
    ];
    
    scriptFiles.forEach(script => {
      const scriptPath = path.join(deploymentDir, script);
      if (fs.existsSync(scriptPath)) {
        try {
          fs.chmodSync(scriptPath, '755');
          log.success(`Set executable permissions: ${script}`);
        } catch (error) {
          log.warning(`Could not set permissions for ${script}: ${error.message}`);
        }
      }
    });
  } catch (error) {
    log.warning(`Permission setting failed: ${error.message}`);
  }
}

async function validateDeploymentPackage(deploymentDir) {
  const requiredFiles = [
    'frontend/index.html',
    'frontend/admin/admin.html',
    'server/index.js',
    'nginx/nirvana-organics.conf',
    'systemd/nirvana-backend.service',
    'config/.env.production.template',
    'deploy.sh',
    'package.json'
  ];
  
  let allValid = true;
  
  requiredFiles.forEach(file => {
    const filePath = path.join(deploymentDir, file);
    if (fs.existsSync(filePath)) {
      log.success(`✓ ${file}`);
    } else {
      log.error(`✗ Missing: ${file}`);
      allValid = false;
    }
  });
  
  if (allValid) {
    log.success('All required files present in deployment package');
  } else {
    throw new Error('Deployment package validation failed - missing required files');
  }
}

async function createConfigurationFiles(deploymentDir) {
  // Create environment configuration template
  const envTemplate = `# Nirvana Organics E-commerce - Production Environment Configuration
# Copy this file to .env and update with your actual values

# Application Configuration
NODE_ENV=production
PORT=5000
APP_NAME="Nirvana Organics E-commerce"
APP_URL=https://yourdomain.com

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_production
DB_USER=nirvana_user
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# JWT Configuration (Generate secure random strings)
JWT_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_REFRESH_SECRET=CHANGE_THIS_TO_ANOTHER_VERY_SECURE_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
EMAIL_FROM="Nirvana Organics <<EMAIL>>"

# Square Payment Integration
SQUARE_ACCESS_TOKEN=your-square-access-token
SQUARE_APPLICATION_ID=your-square-application-id
SQUARE_ENVIRONMENT=sandbox
SQUARE_WEBHOOK_SIGNATURE_KEY=your-webhook-signature-key

# File Upload Configuration
UPLOAD_PATH=/var/www/nirvana-backend/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=CHANGE_THIS_TO_A_SECURE_SESSION_SECRET
CORS_ORIGIN=https://yourdomain.com

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/yourdomain.com.crt
SSL_KEY_PATH=/etc/ssl/private/yourdomain.com.key

# Backup Configuration
BACKUP_PATH=/var/www/nirvana-backend/backups
BACKUP_RETENTION_DAYS=30

# Logging Configuration
LOG_LEVEL=info
LOG_PATH=/var/www/nirvana-backend/logs

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_TTL=3600`;

  fs.writeFileSync(path.join(deploymentDir, 'config', '.env.production.template'), envTemplate);
  log.success('Created environment configuration template');

  // Create database configuration
  const dbConfig = `module.exports = {
  development: {
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'nirvana_organics_dev',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: console.log
  },
  production: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
};`;

  fs.writeFileSync(path.join(deploymentDir, 'config', 'database.js'), dbConfig);
  log.success('Created database configuration');
}

async function createDeploymentScripts(deploymentDir) {
  // Create main deployment script
  const deployScript = `#!/bin/bash

# Nirvana Organics E-commerce - Complete Production Deployment Script
# This script automates the entire deployment process with minimal manual intervention

set -e  # Exit on any error

# Color codes for output
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
BLUE='\\033[0;34m'
NC='\\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "\${BLUE}[INFO]\${NC} \$1"
}

log_success() {
    echo -e "\${GREEN}[SUCCESS]\${NC} \$1"
}

log_warning() {
    echo -e "\${YELLOW}[WARNING]\${NC} \$1"
}

log_error() {
    echo -e "\${RED}[ERROR]\${NC} \$1"
}

log_header() {
    echo -e "\\n\${BLUE}======================================\${NC}"
    echo -e "\${BLUE}\$1\${NC}"
    echo -e "\${BLUE}======================================\${NC}"
}

# Configuration
APP_NAME="nirvana-organics"
APP_USER="nirvana"
APP_DIR="/var/www/nirvana-backend"
NGINX_CONF="/etc/nginx/sites-available/nirvana-organics"
SYSTEMD_SERVICE="/etc/systemd/system/nirvana-backend.service"

# Check if running as root
if [[ \$EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

log_header "🚀 Starting Nirvana Organics E-commerce Deployment"

# Step 1: System preparation
log_header "📋 System Preparation"
./scripts/setup-server.sh

# Step 2: Install dependencies
log_header "📦 Installing Dependencies"
./scripts/install-dependencies.sh

# Step 3: Create application user and directories
log_header "👤 Setting Up Application User"
if ! id "\$APP_USER" &>/dev/null; then
    log_info "Creating application user: \$APP_USER"
    useradd -m -s /bin/bash \$APP_USER
    usermod -aG sudo \$APP_USER
    log_success "Created application user: \$APP_USER"
else
    log_info "Application user already exists: \$APP_USER"
fi

# Step 4: Create directory structure
log_header "📁 Creating Directory Structure"
log_info "Creating application directories..."
mkdir -p \$APP_DIR/{logs,uploads,backups,config}
mkdir -p \$APP_DIR/uploads/{products,categories,banners,documents}

# Step 5: Copy application files
log_header "📋 Copying Application Files"
log_info "Copying server files..."
cp -r server/ \$APP_DIR/
log_info "Copying frontend files..."
cp -r frontend/ \$APP_DIR/
log_info "Copying scripts..."
cp -r scripts/ \$APP_DIR/
log_info "Copying configuration files..."
cp -r config/ \$APP_DIR/

# Copy package files
cp package.json \$APP_DIR/
cp server-package.json \$APP_DIR/package.json

# Step 6: Set permissions
log_header "🔒 Setting File Permissions"
chown -R \$APP_USER:\$APP_USER \$APP_DIR
chmod -R 755 \$APP_DIR
chmod 700 \$APP_DIR/config
find \$APP_DIR -name "*.js" -exec chmod 644 {} \\;
find \$APP_DIR/scripts -name "*.js" -exec chmod 755 {} \\;

# Step 7: Install Node.js dependencies
log_header "📦 Installing Node.js Dependencies"
cd \$APP_DIR
log_info "Installing production dependencies..."
sudo -u \$APP_USER npm ci --production --silent
log_success "Node.js dependencies installed"

# Step 8: Configure environment
log_header "⚙️ Environment Configuration"
if [ ! -f "\$APP_DIR/.env" ]; then
    log_info "Creating environment file from template..."
    cp config/.env.production.template \$APP_DIR/.env
    chown \$APP_USER:\$APP_USER \$APP_DIR/.env
    chmod 600 \$APP_DIR/.env
    log_warning "Please edit \$APP_DIR/.env with your actual configuration values"
    log_warning "The deployment will pause here for you to configure the environment"
    read -p "Press Enter after you have configured the .env file..."
else
    log_info "Environment file already exists"
fi

# Step 9: Configure Nginx
log_header "🌐 Configuring Nginx"
./scripts/configure-nginx.sh

# Step 10: Setup database
log_header "🗄️ Database Setup"
log_info "Setting up database..."
sudo -u \$APP_USER node scripts/setup-database.js
log_success "Database setup completed"

# Step 11: Install systemd service
log_header "⚙️ Installing System Service"
log_info "Installing systemd service..."
cp systemd/nirvana-backend.service \$SYSTEMD_SERVICE
systemctl daemon-reload
systemctl enable nirvana-backend
log_success "System service installed and enabled"

# Step 12: Start services
log_header "🚀 Starting Services"
log_info "Starting Nginx..."
systemctl start nginx
systemctl enable nginx

log_info "Starting application..."
systemctl start nirvana-backend

# Step 13: Verify deployment
log_header "✅ Verifying Deployment"
sleep 5

if systemctl is-active --quiet nirvana-backend; then
    log_success "Application service is running"
else
    log_error "Application service failed to start"
    log_info "Checking logs..."
    journalctl -u nirvana-backend --no-pager -n 20
    exit 1
fi

if systemctl is-active --quiet nginx; then
    log_success "Nginx service is running"
else
    log_error "Nginx service failed to start"
    exit 1
fi

# Step 14: Display completion information
log_header "🎉 Deployment Complete!"
log_success "Nirvana Organics E-commerce has been successfully deployed!"
echo ""
log_info "Application Details:"
log_info "  • Application Directory: \$APP_DIR"
log_info "  • Application User: \$APP_USER"
log_info "  • Service Name: nirvana-backend"
log_info "  • Nginx Configuration: \$NGINX_CONF"
echo ""
log_info "Useful Commands:"
log_info "  • Check application status: systemctl status nirvana-backend"
log_info "  • View application logs: journalctl -u nirvana-backend -f"
log_info "  • Restart application: systemctl restart nirvana-backend"
log_info "  • Check Nginx status: systemctl status nginx"
echo ""
log_warning "Next Steps:"
log_warning "1. Configure your domain DNS to point to this server"
log_warning "2. Set up SSL certificates (recommended: Let's Encrypt)"
log_warning "3. Update the .env file with your actual domain and credentials"
log_warning "4. Test your application by visiting your domain"
echo ""
log_success "Deployment completed successfully! 🚀"`;

  fs.writeFileSync(path.join(deploymentDir, 'deploy.sh'), deployScript);
  log.success('Created main deployment script');
}

async function createServiceConfigurations(deploymentDir) {
  // Create Nginx configuration
  const nginxConfig = `# Nirvana Organics E-commerce Nginx Configuration - Unified Deployment
# Replace yourdomain.com with your actual domain

# Redirect HTTP to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com www.yourdomain.com;

    # Let's Encrypt challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://\\$server_name\\$request_uri;
    }
}

# Main server block - serves both main frontend and admin panel
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration (will be configured by Certbot)
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers (General)
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Root directory for unified frontend (contains both main app and admin)
    root /var/www/nirvana-backend/frontend;
    index index.html;

    # Rate limiting for API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;

        # API routes - proxy to Node.js backend
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \\$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;
        proxy_cache_bypass \\$http_upgrade;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Admin panel routes with enhanced security headers
    location /admin {
        # Admin-specific security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Robots-Tag "noindex, nofollow" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';" always;

        # Handle admin panel routing
        alias /var/www/nirvana-backend/frontend/admin;
        try_files \\$uri \\$uri/ /admin/admin.html;
    }

    # Admin assets and static files
    location /admin/ {
        # Admin-specific security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Robots-Tag "noindex, nofollow" always;

        alias /var/www/nirvana-backend/frontend/admin/;
        try_files \\$uri \\$uri/ /admin/admin.html;

        # Cache static assets
        location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
        }
    }

    # Static files and uploads
    location /uploads/ {
        alias /var/www/nirvana-backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";

        # Security for uploaded files
        location ~* \\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)\\$ {
            deny all;
        }
    }

    # Main frontend routes with general security headers
    location / {
        # Main frontend security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        try_files \\$uri \\$uri/ /index.html;

        # Cache static assets
        location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
        }
    }

    # Security: Block access to sensitive files
    location ~ /\\.(ht|git|env) {
        deny all;
    }

    location ~ /\\.(log|conf)\\$ {
        deny all;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;

    # Security and performance optimizations
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;

    # Buffer sizes
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
}`;

  fs.writeFileSync(path.join(deploymentDir, 'nginx', 'nirvana-organics.conf'), nginxConfig);
  log.success('Created Nginx configuration');

  // Create systemd service
  const systemdService = `[Unit]
Description=Nirvana Organics E-commerce Backend
Documentation=https://github.com/your-username/nirvana-organics-backend
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=nirvana
Group=nirvana
WorkingDirectory=/var/www/nirvana-backend
Environment=NODE_ENV=production
EnvironmentFile=/var/www/nirvana-backend/.env
ExecStart=/usr/bin/node server/index.js
ExecReload=/bin/kill -HUP \\$MAINPID
Restart=on-failure
RestartSec=10
KillMode=mixed
KillSignal=SIGINT
TimeoutStopSec=5
SyslogIdentifier=nirvana-backend
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/var/www/nirvana-backend/uploads /var/www/nirvana-backend/logs
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes
RestrictRealtime=yes
RestrictNamespaces=yes

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target`;

  fs.writeFileSync(path.join(deploymentDir, 'systemd', 'nirvana-backend.service'), systemdService);
  log.success('Created systemd service configuration');
}

async function createDocumentation(deploymentDir) {
  const readmeContent = `# Nirvana Organics E-commerce - Production Deployment

This package contains everything needed to deploy the Nirvana Organics E-commerce platform to a production server with minimal manual configuration.

## 🚀 Quick Start

1. **Upload this package to your VPS server**
2. **Extract and run the deployment script:**
   \`\`\`bash
   tar -xzf nirvana-organics-deployment.tar.gz
   cd deployment
   chmod +x deploy.sh
   sudo ./deploy.sh
   \`\`\`
3. **Follow the interactive prompts**
4. **Configure your domain and SSL certificates**

## 📋 Prerequisites

- Ubuntu 20.04+ or CentOS 8+ VPS server
- Root or sudo access
- Minimum 2GB RAM, 20GB storage
- Domain name pointing to your server IP

## 🏗️ What's Included

### Frontend Applications
- **Main E-commerce Store**: Accessible at \`/\` (root path)
- **Admin Panel**: Accessible at \`/admin\` path
- **Unified Build**: Both applications served from single domain

### Backend Services
- **Node.js API Server**: Complete REST API with authentication
- **Database Integration**: MySQL with automated setup
- **Square Payment Integration**: Ready for payment processing
- **Email System**: SMTP configuration for notifications

### Infrastructure
- **Nginx Configuration**: Optimized for production with security headers
- **SSL Support**: Ready for Let's Encrypt or commercial certificates
- **Systemd Service**: Automatic startup and process management
- **Backup Scripts**: Automated database and file backups

## ⚙️ Configuration

### Environment Variables
Edit \`/var/www/nirvana-backend/.env\` with your actual values:

- **Database**: MySQL connection details
- **JWT Secrets**: Secure authentication tokens
- **Email**: SMTP configuration for notifications
- **Square**: Payment processing credentials
- **Domain**: Your actual domain name

### SSL Certificates
For Let's Encrypt (recommended):
\`\`\`bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
\`\`\`

## 🔧 Management Commands

### Application Management
\`\`\`bash
# Check application status
sudo systemctl status nirvana-backend

# View application logs
sudo journalctl -u nirvana-backend -f

# Restart application
sudo systemctl restart nirvana-backend

# Stop application
sudo systemctl stop nirvana-backend
\`\`\`

### Database Management
\`\`\`bash
# Create database backup
cd /var/www/nirvana-backend
node scripts/backup-database.js

# Run database migrations
node scripts/run-migrations.js

# Seed sample data (development only)
node scripts/seed-database.js
\`\`\`

### Nginx Management
\`\`\`bash
# Check Nginx status
sudo systemctl status nginx

# Test Nginx configuration
sudo nginx -t

# Reload Nginx configuration
sudo systemctl reload nginx
\`\`\`

## 🔒 Security Features

- **Security Headers**: Comprehensive HTTP security headers
- **HTTPS Enforcement**: Automatic HTTP to HTTPS redirects
- **Admin Protection**: Enhanced security for admin panel
- **Rate Limiting**: Protection against abuse
- **File Upload Security**: Restricted file types and sizes

## 📊 Monitoring

### Log Files
- **Application Logs**: \`/var/www/nirvana-backend/logs/\`
- **System Logs**: \`journalctl -u nirvana-backend\`
- **Nginx Logs**: \`/var/log/nginx/\`

### Health Checks
\`\`\`bash
# Check all services
cd /var/www/nirvana-backend
node scripts/system-status.js
\`\`\`

## 🆘 Troubleshooting

### Common Issues

**Application won't start:**
\`\`\`bash
# Check logs
sudo journalctl -u nirvana-backend -n 50

# Verify environment file
cat /var/www/nirvana-backend/.env

# Test database connection
cd /var/www/nirvana-backend
node scripts/test-database-connection.js
\`\`\`

**Nginx 502 Bad Gateway:**
\`\`\`bash
# Check if Node.js app is running
sudo systemctl status nirvana-backend

# Check Nginx configuration
sudo nginx -t

# Restart services
sudo systemctl restart nirvana-backend nginx
\`\`\`

**Database connection issues:**
\`\`\`bash
# Test MySQL connection
mysql -h localhost -u nirvana_user -p nirvana_organics_production

# Check MySQL service
sudo systemctl status mysql
\`\`\`

## 📞 Support

For deployment support:
1. Check the troubleshooting section above
2. Review application logs: \`journalctl -u nirvana-backend -f\`
3. Verify environment configuration
4. Ensure all required services are running

## 🔄 Updates

To update the application:
1. Upload new deployment package
2. Stop the application: \`sudo systemctl stop nirvana-backend\`
3. Backup current installation
4. Replace application files
5. Run migrations if needed
6. Start the application: \`sudo systemctl start nirvana-backend\`

---

**Deployment Package Version**: 1.0.0
**Last Updated**: ${new Date().toISOString().split('T')[0]}
**Support**: Nirvana Organics Development Team`;

  fs.writeFileSync(path.join(deploymentDir, 'README.md'), readmeContent);
  log.success('Created deployment documentation');
}

async function createPackageFiles(deploymentDir) {
  // Create package.json for deployment
  const packageJson = {
    name: "nirvana-organics-deployment",
    version: "1.0.0",
    description: "Nirvana Organics E-commerce Production Deployment Package",
    main: "server/index.js",
    scripts: {
      start: "node server/index.js",
      "start:prod": "NODE_ENV=production node server/index.js",
      "setup:database": "node scripts/setup-database.js",
      "create:tables": "node scripts/create-database-tables.js",
      "seed:database": "node scripts/seed-database.js",
      "test:database": "node scripts/test-database-connection.js",
      "backup:database": "node scripts/backup-database.js",
      "migrate": "node scripts/run-migrations.js",
      "status": "node scripts/system-status.js",
      "deploy": "chmod +x deploy.sh && ./deploy.sh"
    },
    dependencies: {
      express: "^4.18.2",
      mysql2: "^3.6.0",
      sequelize: "^6.32.1",
      bcryptjs: "^2.4.3",
      jsonwebtoken: "^9.0.2",
      cors: "^2.8.5",
      helmet: "^7.0.0",
      "express-rate-limit": "^6.8.1",
      multer: "^1.4.5-lts.1",
      nodemailer: "^6.9.4",
      square: "^30.0.0",
      dotenv: "^16.3.1",
      "express-validator": "^7.0.1",
      compression: "^1.7.4",
      morgan: "^1.10.0"
    },
    engines: {
      node: ">=18.0.0",
      npm: ">=8.0.0"
    },
    author: "Nirvana Organics Team",
    license: "MIT"
  };

  fs.writeFileSync(
    path.join(deploymentDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );
  log.success('Created deployment package.json');
}

// Run the deployment creation if this script is executed directly
if (require.main === module) {
  createCompleteDeployment();
}

module.exports = { createCompleteDeployment };
