const dataEnvironmentService = require('./dataEnvironmentService');
const { Product, Order, Category, User } = require('../models');

/**
 * Service to provide data context-aware queries
 * This service wraps database queries to automatically switch between
 * mock and real data based on the current data environment mode
 */
class DataContextService {
  /**
   * Get products with data context
   */
  async getProducts(req, options = {}) {
    try {
      if (req.dataEnvironment && req.dataEnvironment.isMockMode) {
        return await this.getMockProducts(options);
      } else {
        return await this.getRealProducts(options);
      }
    } catch (error) {
      console.error('Error getting products with context:', error);
      throw error;
    }
  }

  /**
   * Get orders with data context
   */
  async getOrders(req, options = {}) {
    try {
      if (req.dataEnvironment && req.dataEnvironment.isMockMode) {
        return await this.getMockOrders(options);
      } else {
        return await this.getRealOrders(options);
      }
    } catch (error) {
      console.error('Error getting orders with context:', error);
      throw error;
    }
  }

  /**
   * Get categories with data context
   */
  async getCategories(req, options = {}) {
    try {
      if (req.dataEnvironment && req.dataEnvironment.isMockMode) {
        return await this.getMockCategories(options);
      } else {
        return await this.getRealCategories(options);
      }
    } catch (error) {
      console.error('Error getting categories with context:', error);
      throw error;
    }
  }

  /**
   * Get users with data context
   */
  async getUsers(req, options = {}) {
    try {
      if (req.dataEnvironment && req.dataEnvironment.isMockMode) {
        return await this.getMockUsers(options);
      } else {
        return await this.getRealUsers(options);
      }
    } catch (error) {
      console.error('Error getting users with context:', error);
      throw error;
    }
  }

  // Mock data methods
  async getMockProducts(options = {}) {
    const mockData = await dataEnvironmentService.getMockData('product', options);
    return {
      products: mockData,
      total: mockData.length,
      isMockData: true
    };
  }

  async getMockOrders(options = {}) {
    const mockData = await dataEnvironmentService.getMockData('order', options);
    return {
      orders: mockData,
      total: mockData.length,
      isMockData: true
    };
  }

  async getMockCategories(options = {}) {
    const mockData = await dataEnvironmentService.getMockData('category', options);
    return {
      categories: mockData,
      total: mockData.length,
      isMockData: true
    };
  }

  async getMockUsers(options = {}) {
    const mockData = await dataEnvironmentService.getMockData('user', options);
    return {
      users: mockData,
      total: mockData.length,
      isMockData: true
    };
  }

  // Real data methods
  async getRealProducts(options = {}) {
    const { limit = 50, offset = 0, where = {}, include = [], order = [['createdAt', 'DESC']] } = options;
    
    const { count, rows } = await Product.findAndCountAll({
      where,
      include,
      limit,
      offset,
      order,
      distinct: true
    });

    return {
      products: rows.map(product => ({
        ...product.toJSON(),
        _isMockData: false
      })),
      total: count,
      isMockData: false
    };
  }

  async getRealOrders(options = {}) {
    const { limit = 50, offset = 0, where = {}, include = [], order = [['createdAt', 'DESC']] } = options;
    
    const { count, rows } = await Order.findAndCountAll({
      where,
      include,
      limit,
      offset,
      order,
      distinct: true
    });

    return {
      orders: rows.map(order => ({
        ...order.toJSON(),
        _isMockData: false
      })),
      total: count,
      isMockData: false
    };
  }

  async getRealCategories(options = {}) {
    const { limit = 50, offset = 0, where = {}, include = [], order = [['sortOrder', 'ASC']] } = options;
    
    const { count, rows } = await Category.findAndCountAll({
      where,
      include,
      limit,
      offset,
      order,
      distinct: true
    });

    return {
      categories: rows.map(category => ({
        ...category.toJSON(),
        _isMockData: false
      })),
      total: count,
      isMockData: false
    };
  }

  async getRealUsers(options = {}) {
    const { limit = 50, offset = 0, where = {}, include = [], order = [['createdAt', 'DESC']] } = options;
    
    const { count, rows } = await User.findAndCountAll({
      where,
      include,
      limit,
      offset,
      order,
      distinct: true
    });

    return {
      users: rows.map(user => ({
        ...user.toJSON(),
        _isMockData: false
      })),
      total: count,
      isMockData: false
    };
  }

  /**
   * Get dashboard statistics with data context
   */
  async getDashboardStats(req) {
    try {
      if (req.dataEnvironment && req.dataEnvironment.isMockMode) {
        return await this.getMockDashboardStats();
      } else {
        return await this.getRealDashboardStats();
      }
    } catch (error) {
      console.error('Error getting dashboard stats with context:', error);
      throw error;
    }
  }

  async getMockDashboardStats() {
    const mockProducts = await dataEnvironmentService.getMockData('product');
    const mockOrders = await dataEnvironmentService.getMockData('order');
    const mockUsers = await dataEnvironmentService.getMockData('user');

    // Calculate mock statistics
    const totalRevenue = mockOrders.reduce((sum, order) => sum + (order.total || 0), 0);
    const totalOrders = mockOrders.length;
    const totalProducts = mockProducts.length;
    const totalUsers = mockUsers.length;

    return {
      totalRevenue,
      totalOrders,
      totalProducts,
      totalUsers,
      pendingOrders: mockOrders.filter(order => order.status === 'pending').length,
      completedOrders: mockOrders.filter(order => order.status === 'completed').length,
      isMockData: true,
      dataMode: 'mock'
    };
  }

  async getRealDashboardStats() {
    // Get real statistics from database
    const [
      totalProducts,
      totalOrders,
      totalUsers,
      orderStats,
      revenueStats
    ] = await Promise.all([
      Product.count({ where: { isActive: true } }),
      Order.count(),
      User.count(),
      Order.findAll({
        attributes: [
          'status',
          [Product.sequelize.fn('COUNT', Product.sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      }),
      Order.findAll({
        attributes: [
          [Product.sequelize.fn('SUM', Product.sequelize.col('total')), 'totalRevenue']
        ],
        where: { status: 'completed' },
        raw: true
      })
    ]);

    const totalRevenue = revenueStats[0]?.totalRevenue || 0;
    const pendingOrders = orderStats.find(stat => stat.status === 'pending')?.count || 0;
    const completedOrders = orderStats.find(stat => stat.status === 'completed')?.count || 0;

    return {
      totalRevenue: parseFloat(totalRevenue),
      totalOrders,
      totalProducts,
      totalUsers,
      pendingOrders: parseInt(pendingOrders),
      completedOrders: parseInt(completedOrders),
      isMockData: false,
      dataMode: 'real'
    };
  }

  /**
   * Check if current request is in mock mode
   */
  isMockMode(req) {
    return req.dataEnvironment && req.dataEnvironment.isMockMode;
  }

  /**
   * Get data mode indicator for responses
   */
  getDataModeIndicator(req) {
    if (!req.dataEnvironment) {
      return { mode: 'real', isMockData: false };
    }

    return {
      mode: req.dataEnvironment.mode,
      isMockData: req.dataEnvironment.isMockMode,
      sessionId: req.dataEnvironment.sessionId
    };
  }
}

module.exports = new DataContextService();
