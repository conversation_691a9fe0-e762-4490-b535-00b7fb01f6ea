const { Server } = require('socket.io');
const models = require('../models');
const { Op } = require('sequelize');

/**
 * Real-Time Service
 * Handles WebSocket connections and real-time order monitoring
 */
class RealTimeService {
  constructor() {
    this.io = null;
    this.connectedAdmins = new Map();
    this.orderQueue = [];
    this.metrics = {
      totalOrders: 0,
      totalRevenue: 0,
      activeCustomers: 0,
      averageOrderValue: 0
    };
  }

  /**
   * Initialize WebSocket server
   */
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventHandlers();
    this.startMetricsUpdater();
    
    console.log('Real-time service initialized');
  }

  /**
   * Setup WebSocket event handlers
   */
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`Client connected: ${socket.id}`);

      // Handle admin authentication
      socket.on('authenticate', async (data) => {
        try {
          const { token, userRole } = data;
          
          // Verify admin token (simplified - implement proper JWT verification)
          if (userRole === 'admin') {
            this.connectedAdmins.set(socket.id, {
              socketId: socket.id,
              connectedAt: new Date(),
              lastActivity: new Date()
            });

            socket.join('admin-room');
            
            // Send initial data to newly connected admin
            await this.sendInitialData(socket);
            
            console.log(`Admin authenticated: ${socket.id}`);
          }
        } catch (error) {
          console.error('Authentication error:', error);
          socket.emit('auth-error', { message: 'Authentication failed' });
        }
      });

      // Handle admin requesting order history
      socket.on('request-order-history', async (data) => {
        try {
          const { limit = 50, offset = 0 } = data;
          const orderHistory = await this.getOrderHistory(limit, offset);
          socket.emit('order-history', orderHistory);
        } catch (error) {
          console.error('Error fetching order history:', error);
          socket.emit('error', { message: 'Failed to fetch order history' });
        }
      });

      // Handle real-time metrics request
      socket.on('request-metrics', async () => {
        try {
          const metrics = await this.calculateRealTimeMetrics();
          socket.emit('metrics-update', metrics);
        } catch (error) {
          console.error('Error fetching metrics:', error);
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.connectedAdmins.delete(socket.id);
        console.log(`Client disconnected: ${socket.id}`);
      });

      // Handle ping for connection health
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });
  }

  /**
   * Send initial data to newly connected admin
   */
  async sendInitialData(socket) {
    try {
      // Send recent orders
      const recentOrders = await this.getOrderHistory(20, 0);
      socket.emit('initial-orders', recentOrders);

      // Send current metrics
      const metrics = await this.calculateRealTimeMetrics();
      socket.emit('initial-metrics', metrics);

      // Send today's activity summary
      const todayActivity = await this.getTodayActivity();
      socket.emit('today-activity', todayActivity);

    } catch (error) {
      console.error('Error sending initial data:', error);
    }
  }

  /**
   * Broadcast new order to all connected admins
   */
  async broadcastNewOrder(orderData) {
    try {
      const enrichedOrderData = await this.enrichOrderData(orderData);
      
      // Add to order queue for processing
      this.orderQueue.push(enrichedOrderData);
      
      // Broadcast to all connected admins
      this.io.to('admin-room').emit('new-order', enrichedOrderData);
      
      // Update and broadcast metrics
      const updatedMetrics = await this.calculateRealTimeMetrics();
      this.io.to('admin-room').emit('metrics-update', updatedMetrics);
      
      // Broadcast order activity update
      const activityUpdate = await this.getOrderActivity();
      this.io.to('admin-room').emit('activity-update', activityUpdate);

      console.log(`Broadcasted new order: ${orderData.orderNumber}`);
      
    } catch (error) {
      console.error('Error broadcasting new order:', error);
    }
  }

  /**
   * Enrich order data with additional information
   */
  async enrichOrderData(orderData) {
    try {
      const order = await models.Order.findByPk(orderData.id, {
        include: [
          {
            model: models.User,
            as: 'user',
            attributes: ['firstName', 'lastName', 'email', 'membershipType']
          },
          {
            model: models.OrderItem,
            as: 'items',
            include: [
              {
                model: models.Product,
                as: 'product',
                attributes: ['name', 'sku', 'categoryId']
              }
            ]
          },
          {
            model: models.CouponUsage,
            as: 'couponUsages',
            include: [
              {
                model: models.Coupon,
                as: 'coupon',
                attributes: ['code', 'name']
              }
            ]
          }
        ]
      });

      if (!order) {
        // For test data or cases where order doesn't exist in DB, return the original data
        console.log('Order not found in database, using provided data for real-time broadcast');
        return orderData;
      }

      return {
        id: order.id,
        orderNumber: order.orderNumber,
        customer: {
          name: order.user ? `${order.user.firstName} ${order.user.lastName}` : 'Guest Customer',
          email: order.user?.email || order.guestEmail,
          membershipType: order.user?.membershipType || 'guest'
        },
        items: order.items.map(item => ({
          productName: item.product.name,
          sku: item.product.sku,
          quantity: item.quantity,
          price: item.price,
          total: item.quantity * item.price
        })),
        pricing: {
          subtotal: order.subtotal,
          tax: order.tax,
          shipping: order.shipping,
          discount: order.discount,
          total: order.total
        },
        coupons: order.couponUsages.map(usage => ({
          code: usage.coupon.code,
          name: usage.coupon.name,
          discount: usage.discountAmount
        })),
        shipping: {
          method: order.shippingMethod,
          address: {
            street: order.shippingAddress,
            city: order.shippingCity,
            state: order.shippingState,
            zipCode: order.shippingZipCode,
            country: order.shippingCountry
          }
        },
        payment: {
          status: order.paymentStatus,
          method: order.paymentMethod,
          transactionId: order.transactionId
        },
        status: order.status,
        createdAt: order.createdAt,
        estimatedDelivery: this.calculateEstimatedDelivery(order.shippingMethod),
        priority: this.calculateOrderPriority(order)
      };

    } catch (error) {
      console.error('Error enriching order data:', error);
      return orderData;
    }
  }

  /**
   * Calculate estimated delivery date
   */
  calculateEstimatedDelivery(shippingMethod) {
    const now = new Date();
    let deliveryDays = 7; // Default

    switch (shippingMethod) {
      case 'express':
        deliveryDays = 2;
        break;
      case 'regular':
        deliveryDays = 5;
        break;
      case 'free':
        deliveryDays = 7;
        break;
    }

    const deliveryDate = new Date(now.getTime() + (deliveryDays * 24 * 60 * 60 * 1000));
    return deliveryDate.toISOString();
  }

  /**
   * Calculate order priority based on various factors
   */
  calculateOrderPriority(order) {
    let priority = 'normal';

    // High value orders
    if (order.total > 200) {
      priority = 'high';
    }

    // Premium customers
    if (order.user?.membershipType === 'premium') {
      priority = 'high';
    }

    // Express shipping
    if (order.shippingMethod === 'express') {
      priority = 'high';
    }

    return priority;
  }

  /**
   * Get order history for admin dashboard
   */
  async getOrderHistory(limit = 50, offset = 0) {
    try {
      const orders = await models.Order.findAll({
        include: [
          {
            model: models.User,
            as: 'user',
            attributes: ['firstName', 'lastName', 'email', 'membershipType']
          },
          {
            model: models.OrderItem,
            as: 'items',
            include: [
              {
                model: models.Product,
                as: 'product',
                attributes: ['name', 'sku']
              }
            ]
          }
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });

      return orders.map(order => ({
        id: order.id,
        orderNumber: order.orderNumber,
        customer: {
          name: order.user ? `${order.user.firstName} ${order.user.lastName}` : 'Guest Customer',
          email: order.user?.email || order.guestEmail,
          membershipType: order.user?.membershipType || 'guest'
        },
        itemCount: order.items.length,
        total: order.total,
        status: order.status,
        paymentStatus: order.paymentStatus,
        createdAt: order.createdAt,
        priority: this.calculateOrderPriority(order)
      }));

    } catch (error) {
      console.error('Error fetching order history:', error);
      return [];
    }
  }

  /**
   * Calculate real-time metrics
   */
  async calculateRealTimeMetrics() {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const startOfWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

      // Today's metrics
      const todayOrders = await models.Order.count({
        where: {
          createdAt: { [Op.gte]: startOfDay },
          status: { [Op.in]: ['pending', 'processing', 'completed', 'delivered'] }
        }
      });

      const todayRevenue = await models.Order.sum('total', {
        where: {
          createdAt: { [Op.gte]: startOfDay },
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      }) || 0;

      // Week metrics
      const weekOrders = await models.Order.count({
        where: {
          createdAt: { [Op.gte]: startOfWeek },
          status: { [Op.in]: ['pending', 'processing', 'completed', 'delivered'] }
        }
      });

      const weekRevenue = await models.Order.sum('total', {
        where: {
          createdAt: { [Op.gte]: startOfWeek },
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      }) || 0;

      // Month metrics
      const monthOrders = await models.Order.count({
        where: {
          createdAt: { [Op.gte]: startOfMonth },
          status: { [Op.in]: ['pending', 'processing', 'completed', 'delivered'] }
        }
      });

      const monthRevenue = await models.Order.sum('total', {
        where: {
          createdAt: { [Op.gte]: startOfMonth },
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      }) || 0;

      // Active customers (users who logged in today)
      const activeCustomers = await models.User.count({
        where: {
          lastLoginAt: { [Op.gte]: startOfDay },
          role: 'customer'
        }
      });

      // Average order value
      const averageOrderValue = todayOrders > 0 ? todayRevenue / todayOrders : 0;

      // Pending orders
      const pendingOrders = await models.Order.count({
        where: { status: 'pending' }
      });

      return {
        today: {
          orders: todayOrders,
          revenue: todayRevenue,
          averageOrderValue
        },
        week: {
          orders: weekOrders,
          revenue: weekRevenue
        },
        month: {
          orders: monthOrders,
          revenue: monthRevenue
        },
        activeCustomers,
        pendingOrders,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error calculating real-time metrics:', error);
      return {
        today: { orders: 0, revenue: 0, averageOrderValue: 0 },
        week: { orders: 0, revenue: 0 },
        month: { orders: 0, revenue: 0 },
        activeCustomers: 0,
        pendingOrders: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get today's activity summary
   */
  async getTodayActivity() {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

      const hourlyActivity = await models.Order.findAll({
        attributes: [
          [models.sequelize.fn('EXTRACT', models.sequelize.literal('HOUR FROM "createdAt"')), 'hour'],
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'orderCount'],
          [models.sequelize.fn('SUM', models.sequelize.col('total')), 'revenue']
        ],
        where: {
          createdAt: { [Op.gte]: startOfDay }
        },
        group: [models.sequelize.fn('EXTRACT', models.sequelize.literal('HOUR FROM "createdAt"'))],
        order: [[models.sequelize.fn('EXTRACT', models.sequelize.literal('HOUR FROM "createdAt"')), 'ASC']],
        raw: true
      });

      return {
        hourlyActivity,
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error getting today activity:', error);
      return { hourlyActivity: [], lastUpdated: new Date().toISOString() };
    }
  }

  /**
   * Get recent order activity for live feed
   */
  async getOrderActivity() {
    try {
      const recentActivity = await models.Order.findAll({
        attributes: ['id', 'orderNumber', 'total', 'status', 'createdAt'],
        include: [
          {
            model: models.User,
            as: 'user',
            attributes: ['firstName', 'lastName']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: 10
      });

      return recentActivity.map(order => ({
        id: order.id,
        orderNumber: order.orderNumber,
        customerName: order.user ? `${order.user.firstName} ${order.user.lastName}` : 'Guest',
        total: order.total,
        status: order.status,
        createdAt: order.createdAt
      }));

    } catch (error) {
      console.error('Error getting order activity:', error);
      return [];
    }
  }

  /**
   * Start periodic metrics updater
   */
  startMetricsUpdater() {
    // Update metrics every 30 seconds
    setInterval(async () => {
      if (this.connectedAdmins.size > 0) {
        try {
          const metrics = await this.calculateRealTimeMetrics();
          this.io.to('admin-room').emit('metrics-update', metrics);
        } catch (error) {
          console.error('Error in metrics updater:', error);
        }
      }
    }, 30000);

    console.log('Metrics updater started');
  }

  /**
   * Broadcast order status update
   */
  async broadcastOrderStatusUpdate(orderId, newStatus) {
    try {
      const order = await models.Order.findByPk(orderId, {
        include: [
          {
            model: models.User,
            as: 'user',
            attributes: ['firstName', 'lastName', 'email']
          }
        ]
      });

      if (order) {
        const updateData = {
          orderId: order.id,
          orderNumber: order.orderNumber,
          newStatus,
          customerName: order.user ? `${order.user.firstName} ${order.user.lastName}` : 'Guest',
          total: order.total,
          updatedAt: new Date().toISOString()
        };

        this.io.to('admin-room').emit('order-status-update', updateData);
        console.log(`Broadcasted status update for order: ${order.orderNumber}`);
      }

    } catch (error) {
      console.error('Error broadcasting order status update:', error);
    }
  }

  /**
   * Get connected admins count
   */
  getConnectedAdminsCount() {
    return this.connectedAdmins.size;
  }

  /**
   * Broadcast system alert to admins
   */
  broadcastSystemAlert(alertData) {
    this.io.to('admin-room').emit('system-alert', {
      ...alertData,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = new RealTimeService();
