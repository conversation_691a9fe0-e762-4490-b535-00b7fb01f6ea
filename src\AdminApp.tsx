import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { HelmetProvider } from 'react-helmet-async';
import { store } from './store';
import { useAppDispatch, useAppSelector } from './hooks/redux';
import { getProfile } from './store/slices/authSlice';

// Admin Components Only
import AdminLogin from './pages/admin/AdminLogin';
import AdminDashboard from './pages/admin/Dashboard';
import AdminProducts from './pages/admin/Products';
import ProductNew from './pages/admin/ProductNew';
import ProductEdit from './pages/admin/ProductEdit';
import CategoryManagement from './pages/admin/CategoryManagement';
import AdminOrders from './pages/admin/Orders';
import OrderManagement from './pages/admin/OrderManagement';
import UserManagement from './pages/admin/UserManagement';
import SimpleAnalytics from './pages/admin/SimpleAnalytics';
import AuditLogs from './pages/admin/AuditLogs';
import AuthCallback from './pages/AuthCallback';

// Common Components
import ProtectedRoute from './components/common/ProtectedRoute';
import LoadingSpinner from './components/common/LoadingSpinner';
import Toast from './components/common/Toast';
import ErrorBoundary from './components/common/ErrorBoundary';

// Data Environment Context
import { DataEnvironmentProvider } from './contexts/DataEnvironmentContext';

function AdminAppContent() {
  const dispatch = useAppDispatch();
  const { token, loading, isAuthenticated, user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (token) {
      // Add timeout to prevent infinite loading
      const profileTimeout = setTimeout(() => {
        dispatch(getProfile()).catch(() => {
          console.warn('Profile fetch failed, continuing...');
        });
      }, 100);

      return () => clearTimeout(profileTimeout);
    }
  }, [dispatch, token]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ErrorBoundary>
        <Routes>
          {/* Admin Login Route */}
          <Route
            path="/login"
            element={
              isAuthenticated && user?.role === 'admin' ?
                <Navigate to="/dashboard" replace /> :
                <AdminLogin />
            }
          />

          {/* Auth Callback Route for OAuth */}
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Admin Dashboard Routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute requireAdmin>
              <AdminDashboard />
            </ProtectedRoute>
          } />

          {/* Product Management Routes */}
          <Route path="/products" element={
            <ProtectedRoute requireAdmin>
              <AdminProducts />
            </ProtectedRoute>
          } />
          <Route path="/products/new" element={
            <ProtectedRoute requireAdmin>
              <ProductNew />
            </ProtectedRoute>
          } />
          <Route path="/products/edit/:id" element={
            <ProtectedRoute requireAdmin>
              <ProductEdit />
            </ProtectedRoute>
          } />

          {/* Category Management */}
          <Route path="/categories" element={
            <ProtectedRoute requireAdmin>
              <CategoryManagement />
            </ProtectedRoute>
          } />

          {/* Order Management Routes */}
          <Route path="/orders" element={
            <ProtectedRoute requireAdmin>
              <AdminOrders />
            </ProtectedRoute>
          } />
          <Route path="/order-management" element={
            <ProtectedRoute requireAdmin>
              <OrderManagement />
            </ProtectedRoute>
          } />

          {/* User Management */}
          <Route path="/users" element={
            <ProtectedRoute requireAdmin>
              <UserManagement />
            </ProtectedRoute>
          } />

          {/* Analytics */}
          <Route path="/analytics" element={
            <ProtectedRoute requireAdmin>
              <SimpleAnalytics />
            </ProtectedRoute>
          } />

          {/* Audit Logs */}
          <Route path="/audit-logs" element={
            <ProtectedRoute requireAdmin>
              <AuditLogs />
            </ProtectedRoute>
          } />

          {/* Default Redirects */}
          <Route path="/" element={
            isAuthenticated && user?.role === 'admin' ? 
              <Navigate to="/dashboard" replace /> : 
              <Navigate to="/login" replace />
          } />

          {/* Catch all - redirect to dashboard or login */}
          <Route path="*" element={
            isAuthenticated && user?.role === 'admin' ? 
              <Navigate to="/dashboard" replace /> : 
              <Navigate to="/login" replace />
          } />
        </Routes>

        <Toast />
      </ErrorBoundary>
    </div>
  );
}

function AdminApp() {
  return (
    <HelmetProvider>
      <Provider store={store}>
        <Router>
          <DataEnvironmentProvider>
            <AdminAppContent />
          </DataEnvironmentProvider>
        </Router>
      </Provider>
    </HelmetProvider>
  );
}

export default AdminApp;
