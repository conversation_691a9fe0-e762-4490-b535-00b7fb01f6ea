#!/bin/bash

# ============================================================================
# Nirvana Organics Backend - Server Setup Script
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="nirvana-backend"
PROJECT_PATH="/var/www/$PROJECT_NAME"
NGINX_AVAILABLE="/etc/nginx/sites-available"
NGINX_ENABLED="/etc/nginx/sites-enabled"

log_info "Setting up server for $ENVIRONMENT environment"

# Update system packages
log_info "Updating system packages..."
apt update && apt upgrade -y

# Install required packages
log_info "Installing required packages..."
apt install -y curl wget git nginx mysql-server certbot python3-certbot-nginx ufw fail2ban

# Install Node.js 18.x
log_info "Installing Node.js 18.x..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# Install PM2 globally
log_info "Installing PM2 process manager..."
npm install -g pm2

# Create project user (if not exists)
if ! id "nirvana" &>/dev/null; then
    log_info "Creating project user..."
    useradd -m -s /bin/bash nirvana
    usermod -aG sudo nirvana
fi

# Create project directory structure
log_info "Creating project directory structure..."
mkdir -p $PROJECT_PATH/{releases,shared/{logs,uploads,config}}
chown -R nirvana:nirvana $PROJECT_PATH

# Setup MySQL database
log_info "Configuring MySQL database..."
mysql -e "CREATE DATABASE IF NOT EXISTS nirvana_organics_${ENVIRONMENT};"
mysql -e "CREATE USER IF NOT EXISTS 'nirvana'@'localhost' IDENTIFIED BY 'secure_password_here';"
mysql -e "GRANT ALL PRIVILEGES ON nirvana_organics_${ENVIRONMENT}.* TO 'nirvana'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# Configure Nginx
log_info "Configuring Nginx..."
cat > $NGINX_AVAILABLE/$PROJECT_NAME << EOF
# Nirvana Organics Backend - Nginx Configuration
server {
    listen 80;
    server_name api.nirvanaorganics.com;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    # Main API
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files
    location /uploads/ {
        alias $PROJECT_PATH/shared/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://localhost:5000/health;
    }
}

# Admin Panel
server {
    listen 80;
    server_name admin.nirvanaorganics.com;
    
    # Security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Admin API
    location / {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable Nginx site
ln -sf $NGINX_AVAILABLE/$PROJECT_NAME $NGINX_ENABLED/
nginx -t && systemctl reload nginx

# Configure firewall
log_info "Configuring firewall..."
ufw --force enable
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 3306  # MySQL (restrict this in production)

# Configure fail2ban
log_info "Configuring fail2ban..."
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 3
EOF

systemctl restart fail2ban

# Setup SSL certificates (Let's Encrypt)
if [[ "$ENVIRONMENT" == "production" ]]; then
    log_info "Setting up SSL certificates..."
    certbot --nginx -d api.nirvanaorganics.com -d admin.nirvanaorganics.com --non-interactive --agree-tos --email <EMAIL>
    
    # Setup auto-renewal
    echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
fi

# Setup log rotation
log_info "Configuring log rotation..."
cat > /etc/logrotate.d/nirvana-backend << EOF
$PROJECT_PATH/shared/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 nirvana nirvana
    postrotate
        pm2 reloadLogs
    endscript
}
EOF

# Setup PM2 startup
log_info "Configuring PM2 startup..."
sudo -u nirvana pm2 startup
pm2 save

# Create environment file template
log_info "Creating environment file template..."
cat > $PROJECT_PATH/shared/config/.env.production.template << EOF
# Production Environment Configuration
NODE_ENV=production
PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_production
DB_USER=nirvana
DB_PASSWORD=secure_password_here

# JWT Configuration
JWT_SECRET=CHANGE_THIS_TO_A_SECURE_SECRET
JWT_REFRESH_SECRET=CHANGE_THIS_TO_ANOTHER_SECURE_SECRET

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Other configurations...
EOF

# Setup webhook deployment service
log_info "Setting up webhook deployment service..."
cat > /etc/systemd/system/nirvana-webhook.service << EOF
[Unit]
Description=Nirvana Organics Webhook Deployment Service
After=network.target

[Service]
Type=simple
User=nirvana
WorkingDirectory=$PROJECT_PATH/current
ExecStart=/usr/bin/node scripts/webhook-deploy.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=WEBHOOK_PORT=9000
Environment=WEBHOOK_SECRET=your-webhook-secret-here

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable nirvana-webhook

# Create deployment directories
log_info "Creating deployment directories..."
mkdir -p $PROJECT_PATH/shared/{logs,uploads,config,backups}
chown -R nirvana:nirvana $PROJECT_PATH

# Setup backup script
log_info "Setting up backup script..."
cat > /usr/local/bin/backup-nirvana.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/www/nirvana-backend/shared/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Database backup
mysqldump nirvana_organics_production > "$BACKUP_DIR/db_backup_$DATE.sql"

# Files backup
tar -czf "$BACKUP_DIR/files_backup_$DATE.tar.gz" /var/www/nirvana-backend/shared/uploads

# Cleanup old backups (keep last 7 days)
find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /usr/local/bin/backup-nirvana.sh

# Setup daily backup cron job
echo "0 2 * * * /usr/local/bin/backup-nirvana.sh" | crontab -

log_success "Server setup completed successfully!"
log_info "Next steps:"
log_info "1. Configure your environment variables in $PROJECT_PATH/shared/config/.env.production"
log_info "2. Set up your Git repository and deploy your application"
log_info "3. Configure your domain DNS to point to this server"
log_info "4. Test the deployment process"

log_warning "Security reminders:"
log_warning "1. Change default passwords"
log_warning "2. Configure proper JWT secrets"
log_warning "3. Restrict MySQL access"
log_warning "4. Review firewall rules"
