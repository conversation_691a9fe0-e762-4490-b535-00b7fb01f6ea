const { query } = require('express-validator');

// Validation for dashboard overview query parameters
const validateDashboardQuery = [
  query('period')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Period must be between 1 and 365 days')
];

// Validation for sales analytics query parameters
const validateSalesAnalyticsQuery = [
  query('period')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Period must be between 1 and 365 days'),
    
  query('groupBy')
    .optional()
    .isIn(['hour', 'day', 'week', 'month'])
    .withMessage('Group by must be hour, day, week, or month'),
    
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
    
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      if (value && req.query.startDate) {
        const startDate = new Date(req.query.startDate);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('End date must be after start date');
        }
        
        // Check if date range is not too large (e.g., more than 2 years)
        const daysDiff = (endDate - startDate) / (1000 * 60 * 60 * 24);
        if (daysDiff > 730) {
          throw new Error('Date range cannot exceed 2 years');
        }
      }
      return true;
    })
];

// Validation for customer analytics query parameters
const validateCustomerAnalyticsQuery = [
  query('period')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Period must be between 1 and 365 days')
];

// Validation for product analytics query parameters
const validateProductAnalyticsQuery = [
  query('period')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Period must be between 1 and 365 days')
];

// Validation for system metrics query parameters
const validateSystemMetricsQuery = [
  query('period')
    .optional()
    .isInt({ min: 1, max: 30 })
    .withMessage('Period must be between 1 and 30 days')
];

// Validation for data export query parameters
const validateExportQuery = [
  query('type')
    .isIn(['sales', 'customers', 'products', 'audit'])
    .withMessage('Export type must be sales, customers, products, or audit'),
    
  query('format')
    .optional()
    .isIn(['json', 'csv'])
    .withMessage('Format must be json or csv'),
    
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
    
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      if (value && req.query.startDate) {
        const startDate = new Date(req.query.startDate);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('End date must be after start date');
        }
        
        // Check if date range is not too large for exports
        const daysDiff = (endDate - startDate) / (1000 * 60 * 60 * 24);
        if (daysDiff > 365) {
          throw new Error('Export date range cannot exceed 1 year');
        }
      }
      return true;
    })
];

// Role-based access control for analytics
const requireAnalyticsAccess = (req, res, next) => {
  if (!['admin', 'manager', 'super_admin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Analytics access required'
    });
  }
  next();
};

// Middleware to check export permissions
const requireExportAccess = (req, res, next) => {
  const { type } = req.query;
  
  // Only super admins can export audit logs
  if (type === 'audit' && req.user.role !== 'super_admin') {
    return res.status(403).json({
      success: false,
      message: 'Super admin access required for audit log exports'
    });
  }
  
  // Managers and above can export other data
  if (!['admin', 'manager', 'super_admin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Manager access or higher required for data exports'
    });
  }
  
  next();
};

// Rate limiting for analytics endpoints
const analyticsRateLimit = (req, res, next) => {
  // This would typically use a rate limiting library like express-rate-limit
  // For now, we'll just add a simple check
  const userAgent = req.get('User-Agent');
  const isBot = /bot|crawler|spider/i.test(userAgent);
  
  if (isBot) {
    return res.status(429).json({
      success: false,
      message: 'Rate limit exceeded'
    });
  }
  
  next();
};

// Middleware to validate date ranges
const validateDateRange = (req, res, next) => {
  const { startDate, endDate, period } = req.query;
  
  // If both custom dates and period are provided, prioritize custom dates
  if (startDate && endDate && period) {
    delete req.query.period;
  }
  
  // Validate that start date is not in the future
  if (startDate) {
    const start = new Date(startDate);
    const now = new Date();
    if (start > now) {
      return res.status(400).json({
        success: false,
        message: 'Start date cannot be in the future'
      });
    }
  }
  
  // Validate that end date is not in the future
  if (endDate) {
    const end = new Date(endDate);
    const now = new Date();
    if (end > now) {
      return res.status(400).json({
        success: false,
        message: 'End date cannot be in the future'
      });
    }
  }
  
  next();
};

// Middleware to sanitize analytics queries
const sanitizeAnalyticsQuery = (req, res, next) => {
  // Convert string numbers to integers
  if (req.query.period) {
    req.query.period = parseInt(req.query.period);
  }
  
  // Ensure groupBy is lowercase
  if (req.query.groupBy) {
    req.query.groupBy = req.query.groupBy.toLowerCase();
  }
  
  // Ensure format is lowercase
  if (req.query.format) {
    req.query.format = req.query.format.toLowerCase();
  }
  
  // Ensure type is lowercase
  if (req.query.type) {
    req.query.type = req.query.type.toLowerCase();
  }
  
  next();
};

// Middleware to log analytics access
const logAnalyticsAccess = async (req, res, next) => {
  try {
    const { AuditLog } = require('../models');
    
    // Log the analytics access
    await AuditLog.logUserAction(req.user.id, 'ACCESS_ANALYTICS', 'ANALYTICS', null, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: {
        endpoint: req.path,
        query: req.query
      },
      severity: 'low'
    });
    
    next();
  } catch (error) {
    // Don't fail the request if logging fails
    console.error('Analytics access logging error:', error);
    next();
  }
};

// Middleware to check system load before heavy analytics queries
const checkSystemLoad = (req, res, next) => {
  // This would typically check actual system metrics
  // For now, we'll implement a simple check based on query complexity
  const { period, groupBy, startDate, endDate } = req.query;
  
  let complexity = 0;
  
  // Add complexity based on period
  if (period && period > 90) complexity += 2;
  else if (period && period > 30) complexity += 1;
  
  // Add complexity based on groupBy
  if (groupBy === 'hour') complexity += 3;
  else if (groupBy === 'day') complexity += 1;
  
  // Add complexity based on custom date range
  if (startDate && endDate) {
    const daysDiff = (new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24);
    if (daysDiff > 180) complexity += 3;
    else if (daysDiff > 90) complexity += 2;
    else if (daysDiff > 30) complexity += 1;
  }
  
  // If complexity is too high, suggest optimization
  if (complexity > 5) {
    return res.status(400).json({
      success: false,
      message: 'Query too complex. Please reduce the date range or change grouping.',
      suggestion: 'Try using a shorter period or group by day instead of hour'
    });
  }
  
  next();
};

module.exports = {
  validateDashboardQuery,
  validateSalesAnalyticsQuery,
  validateCustomerAnalyticsQuery,
  validateProductAnalyticsQuery,
  validateSystemMetricsQuery,
  validateExportQuery,
  requireAnalyticsAccess,
  requireExportAccess,
  analyticsRateLimit,
  validateDateRange,
  sanitizeAnalyticsQuery,
  logAnalyticsAccess,
  checkSystemLoad
};
