/**
 * PM2 Ecosystem Configuration for Production Environment
 * shopnirvanaorganics.com
 */

module.exports = {
  apps: [
    {
      // Main Application Server
      name: 'nirvana-main',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend',
      instances: 4, // More instances for production
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
        HTTPS_PORT: 443,
        PM2_SERVE_PATH: './dist',
        PM2_SERVE_PORT: 3000,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/index.html'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000,
        HTTPS_PORT: 443
      },
      // Logging
      log_file: '/var/www/nirvana-backend/logs/combined.log',
      out_file: '/var/www/nirvana-backend/logs/out.log',
      error_file: '/var/www/nirvana-backend/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads', '.git'],
      max_memory_restart: '1G', // Higher memory limit for production
      restart_delay: 4000,
      max_restarts: 15,
      min_uptime: '10s',
      
      // Advanced Options
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000,
      
      // Health Monitoring
      health_check_url: 'http://localhost:5000/api/health',
      health_check_grace_period: 3000,
      
      // Production optimizations
      node_args: '--max-old-space-size=2048'
    },
    
    {
      // Admin Panel Server
      name: 'nirvana-admin',
      script: './server/admin-server.js',
      cwd: '/var/www/nirvana-backend',
      instances: 2, // Multiple instances for admin panel
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 5001,
        ADMIN_MODE: 'true'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5001,
        ADMIN_MODE: 'true'
      },
      // Logging
      log_file: '/var/www/nirvana-backend/logs/admin-combined.log',
      out_file: '/var/www/nirvana-backend/logs/admin-out.log',
      error_file: '/var/www/nirvana-backend/logs/admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management
      watch: false,
      max_memory_restart: '512M',
      restart_delay: 4000,
      max_restarts: 15,
      min_uptime: '10s',
      
      // Health Monitoring
      health_check_url: 'http://localhost:5001/api/health',
      health_check_grace_period: 3000,
      
      // Production optimizations
      node_args: '--max-old-space-size=1024'
    }
  ],

  // Deployment Configuration
  deploy: {
    production: {
      user: 'nirvana',
      host: ['shopnirvanaorganics.com'],
      ref: 'origin/main',
      repo: 'https://github.com/your-username/nirvana-organics.git',
      path: '/var/www/nirvana-backend',
      'pre-deploy-local': '',
      'post-deploy': 'npm install --production && npm run build:prod && npm run migrate:prod && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'mkdir -p /var/www/nirvana-backend/logs /var/www/nirvana-backend/uploads /var/www/nirvana-backend/backups',
      'post-setup': 'ls -la /var/www/nirvana-backend',
      env: {
        NODE_ENV: 'production'
      }
    }
  }
};
