# Nirvana Organics E-commerce - Manual VPS Deployment Guide

This comprehensive guide provides step-by-step instructions for manually deploying the Nirvana Organics Node.js e-commerce website to a VPS server. All steps are designed to be executed manually via command line without relying on automation scripts.

## 📋 Prerequisites

### VPS Server Requirements
- **Operating System**: Ubuntu 20.04 LTS or newer
- **RAM**: Minimum 2GB (4GB recommended for production)
- **Storage**: Minimum 20GB available space
- **Access**: Root or sudo privileges
- **Network**: Public IP address with ports 80, 443, and 22 open

### Domain Requirements
- **Testing Environment**: `test.shopnirvanaorganics.com`
- **Production Environment**: `shopnirvanaorganics.com` and `www.shopnirvanaorganics.com`
- **DNS Configuration**: A records pointing to your VPS IP address

### Required Information
Before starting, gather the following information:
- VPS IP address and SSH credentials
- Domain names and DNS access
- Gmail account for SMTP (with app-specific password)
- Square payment account credentials
- Strong passwords for database and admin accounts

## 🚀 Step 1: Initial VPS Setup

### 1.1 Connect to Your VPS
```bash
# Connect via SSH (replace with your VPS IP and username)
ssh root@your-vps-ip-address
# or
ssh username@your-vps-ip-address
```

**Why this step**: Establishes secure connection to your server for all subsequent operations.

### 1.2 Update System Packages
```bash
# Update package lists
sudo apt update

# Upgrade installed packages
sudo apt upgrade -y

# Install essential tools
sudo apt install -y curl wget unzip git htop nano
```

**Why this step**: Ensures your server has the latest security updates and essential tools for deployment.

### 1.3 Create Application User
```bash
# Create a dedicated user for the application
sudo adduser nirvana

# Add user to sudo group
sudo usermod -aG sudo nirvana

# Switch to the new user
su - nirvana
```

**Why this step**: Running applications as a dedicated user improves security by limiting privileges.

## 🔧 Step 2: Install Required Software

### 2.1 Install Node.js
```bash
# Download and install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version
npm --version
```

**Why this step**: Node.js is required to run the backend application server.

### 2.2 Install MySQL Database
```bash
# Install MySQL server
sudo apt install -y mysql-server

# Start and enable MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql

# Secure MySQL installation
sudo mysql_secure_installation
```

**Why this step**: MySQL will store all application data including products, orders, and user accounts.

**Important**: During `mysql_secure_installation`:
- Set a strong root password
- Remove anonymous users: Yes
- Disallow root login remotely: Yes
- Remove test database: Yes
- Reload privilege tables: Yes

### 2.3 Install Nginx Web Server
```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx service
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

**Why this step**: Nginx will serve static files and proxy API requests to the Node.js application.

### 2.4 Install PM2 Process Manager
```bash
# Install PM2 globally
sudo npm install -g pm2

# Verify installation
pm2 --version
```

**Why this step**: PM2 manages the Node.js application process, ensuring it stays running and restarts if it crashes.

## 📦 Step 3: Upload and Extract Application Files

### 3.1 Upload Deployment Package
From your local machine, upload the appropriate deployment package:

```bash
# For testing environment
scp test.deploy.tar.gz nirvana@your-vps-ip:/home/<USER>/

# For production environment
scp deploy.tar.gz nirvana@your-vps-ip:/home/<USER>/
```

**Why this step**: Transfers your application files from development environment to the server.

### 3.2 Extract and Organize Files
```bash
# On the VPS, extract the package
cd /home/<USER>

# For testing environment
tar -xzf test.deploy.tar.gz
mv test.deploy nirvana-app

# For production environment
tar -xzf deploy.tar.gz
mv deploy nirvana-app

# Create application directory
sudo mkdir -p /var/www/nirvana-backend

# Copy files to application directory
sudo cp -r nirvana-app/* /var/www/nirvana-backend/

# Set ownership
sudo chown -R nirvana:nirvana /var/www/nirvana-backend

# Set permissions
sudo chmod -R 755 /var/www/nirvana-backend
```

**Why this step**: Places application files in the standard web server directory with proper permissions.

## 🗄️ Step 4: Database Setup

### 4.1 Create Database and User
```bash
# Connect to MySQL as root
sudo mysql -u root -p

# Create database (choose appropriate name for environment)
CREATE DATABASE nirvana_organics_production;
# or for testing: CREATE DATABASE nirvana_organics_testing;

# Create database user with strong password
CREATE USER 'nirvana_user'@'localhost' IDENTIFIED BY 'your-very-secure-password';

# Grant privileges
GRANT ALL PRIVILEGES ON nirvana_organics_production.* TO 'nirvana_user'@'localhost';
# or for testing: GRANT ALL PRIVILEGES ON nirvana_organics_testing.* TO 'nirvana_user'@'localhost';

# Apply changes
FLUSH PRIVILEGES;

# Exit MySQL
EXIT;
```

**Why this step**: Creates isolated database and user for the application with minimal required privileges.

### 4.2 Initialize Database Schema
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Run database setup script
node scripts/setup-database.js

# Create database tables
node scripts/create-database-tables.js

# Run any pending migrations
node scripts/run-migrations.js
```

**Why this step**: Creates the database structure required for the application to function.

### 4.3 Create Default Admin User
```bash
# Create the default admin user
node scripts/create-default-admin.js
```

**Why this step**: Creates an admin account so you can access the admin panel after deployment.

**Note**: The default admin credentials will be displayed. **Change the password immediately** after first login.

## ⚙️ Step 5: Environment Configuration

### 5.1 Create Environment File
```bash
# Copy environment template
cd /var/www/nirvana-backend

# For production
cp config/.env.production.template .env

# For testing
cp config/.env.testing.template .env

# Edit the environment file
nano .env
```

**Why this step**: Configures the application with your specific settings and credentials.

### 5.2 Configure Critical Environment Variables
Edit the `.env` file and update these critical values:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_production  # or nirvana_organics_testing
DB_USER=nirvana_user
DB_PASSWORD=your-very-secure-password

# JWT Secrets (generate strong random strings)
JWT_SECRET=your-64-character-jwt-secret-key-here
JWT_REFRESH_SECRET=your-64-character-refresh-secret-key-here

# Application URLs
APP_URL=https://shopnirvanaorganics.com  # or https://test.shopnirvanaorganics.com
CORS_ORIGIN=https://shopnirvanaorganics.com  # or https://test.shopnirvanaorganics.com

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-specific-password
EMAIL_FROM="Nirvana Organics <<EMAIL>>"

# Square Payment Configuration
SQUARE_ACCESS_TOKEN=your-square-access-token
SQUARE_APPLICATION_ID=your-square-application-id
SQUARE_ENVIRONMENT=production  # or sandbox for testing

# Security
SESSION_SECRET=your-64-character-session-secret-key
BCRYPT_ROUNDS=12
```

**Why this step**: Provides the application with necessary credentials and configuration to connect to external services.

**Important Security Notes**:
- Use strong, unique passwords and secrets
- Never use default or example values in production
- Keep the `.env` file secure and never commit it to version control

### 5.3 Set Environment File Permissions
```bash
# Secure the environment file
chmod 600 .env
chown nirvana:nirvana .env
```

**Why this step**: Protects sensitive configuration data from unauthorized access.

## 📦 Step 6: Install Application Dependencies

### 6.1 Install Node.js Dependencies
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Install production dependencies
npm ci --production --silent

# Verify installation
npm list --depth=0
```

**Why this step**: Installs all required Node.js packages for the application to run.

**Note**: The `--production` flag ensures only production dependencies are installed, reducing security surface and disk usage.

## 🌐 Step 7: Configure Nginx Web Server

### 7.1 Create Nginx Configuration
```bash
# Create Nginx site configuration
sudo nano /etc/nginx/sites-available/nirvana-organics
```

**Why this step**: Configures the web server to serve your application and handle SSL termination.

### 7.2 Add Nginx Configuration Content
Copy the appropriate nginx configuration from your deployment package:

```bash
# Copy the nginx configuration
sudo cp /var/www/nirvana-backend/nginx/nirvana-production.conf /etc/nginx/sites-available/nirvana-organics
# or for testing: sudo cp /var/www/nirvana-backend/nginx/nirvana-testing.conf /etc/nginx/sites-available/nirvana-organics

# Enable the site
sudo ln -s /etc/nginx/sites-available/nirvana-organics /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

**Why this step**: Activates your site configuration and ensures Nginx can properly route requests.

## 🔒 Step 8: SSL Certificate Setup

### 8.1 Install Certbot
```bash
# Install Certbot for Let's Encrypt
sudo apt install -y certbot python3-certbot-nginx
```

**Why this step**: Certbot automates SSL certificate installation and renewal for secure HTTPS connections.

### 8.2 Obtain SSL Certificate
```bash
# For production (multiple domains)
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com

# For testing (single domain)
sudo certbot --nginx -d test.shopnirvanaorganics.com
```

**Why this step**: Enables HTTPS encryption for secure communication between browsers and your server.

**Important**: Ensure your domain DNS is properly configured before running this command.

### 8.3 Test SSL Renewal
```bash
# Test automatic renewal
sudo certbot renew --dry-run
```

**Why this step**: Verifies that SSL certificates will automatically renew before expiration.

## 🚀 Step 9: Start the Application

### 9.1 Start Application with PM2
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Start the application
pm2 start ecosystem.config.testing.js  # for testing
# or
pm2 start ecosystem.config.production.js  # for production

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
# Follow the instructions provided by the command above
```

**Why this step**: Starts your Node.js application and ensures it automatically restarts if the server reboots.

### 9.2 Verify Application Status
```bash
# Check PM2 status
pm2 status

# View application logs
pm2 logs

# Monitor application
pm2 monit
```

**Why this step**: Confirms the application is running properly and allows you to monitor its health.

## ✅ Step 10: Verify Deployment

### 10.1 Test Website Access
```bash
# Test HTTP redirect (should redirect to HTTPS)
curl -I http://shopnirvanaorganics.com

# Test HTTPS access
curl -I https://shopnirvanaorganics.com

# Test API health endpoint
curl https://shopnirvanaorganics.com/api/health
```

**Why this step**: Verifies that your website is accessible and the API is responding.

### 10.2 Test Admin Panel Access
1. Open your browser and navigate to:
   - Production: `https://shopnirvanaorganics.com/admin`
   - Testing: `https://test.shopnirvanaorganics.com/admin`

2. Log in with the default admin credentials created earlier

3. **Immediately change the admin password** for security

**Why this step**: Confirms the admin panel is accessible and secure.

### 10.3 Run System Status Check
```bash
# Check overall system status
cd /var/www/nirvana-backend
node scripts/system-status.js
```

**Why this step**: Provides a comprehensive health check of all system components.

## 🔧 Step 11: Post-Deployment Configuration

### 11.1 Configure Firewall
```bash
# Install and configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Check firewall status
sudo ufw status
```

**Why this step**: Secures your server by blocking unnecessary network access.

### 11.2 Set Up Database Backups
```bash
# Create backup directory
sudo mkdir -p /var/backups/nirvana

# Set permissions
sudo chown nirvana:nirvana /var/backups/nirvana

# Test backup script
cd /var/www/nirvana-backend
node scripts/backup-database.js

# Set up daily backup cron job
crontab -e
# Add this line for daily backups at 2 AM:
# 0 2 * * * cd /var/www/nirvana-backend && node scripts/backup-database.js
```

**Why this step**: Protects your data with regular automated backups.

### 11.3 Configure Log Rotation
```bash
# Create log rotation configuration
sudo nano /etc/logrotate.d/nirvana-organics
```

Add this content:
```
/var/www/nirvana-backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
```

**Why this step**: Prevents log files from consuming excessive disk space.

## 🔍 Monitoring and Maintenance

### Daily Monitoring Commands
```bash
# Check application status
pm2 status

# View recent logs
pm2 logs --lines 50

# Check system resources
htop

# Check disk space
df -h

# Check database status
sudo systemctl status mysql
```

### Weekly Maintenance Tasks
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Check SSL certificate status
sudo certbot certificates

# Review application logs for errors
pm2 logs --err --lines 100

# Check backup integrity
ls -la /var/backups/nirvana/
```

## 🆘 Troubleshooting Common Issues

### Application Won't Start
**Symptoms**: PM2 shows application as "errored" or "stopped"

**Diagnosis**:
```bash
# Check PM2 logs for errors
pm2 logs --err

# Verify environment file exists and is readable
ls -la /var/www/nirvana-backend/.env

# Test database connection manually
cd /var/www/nirvana-backend
mysql -u nirvana_user -p nirvana_organics_production
```

**Solutions**:
1. Check environment file configuration
2. Verify database credentials
3. Ensure all required environment variables are set
4. Check file permissions

### Website Not Accessible
**Symptoms**: Browser shows "This site can't be reached" or similar errors

**Diagnosis**:
```bash
# Check Nginx status
sudo systemctl status nginx

# Test Nginx configuration
sudo nginx -t

# Check if ports are open
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# Check DNS resolution
nslookup shopnirvanaorganics.com
```

**Solutions**:
1. Restart Nginx: `sudo systemctl restart nginx`
2. Check firewall settings: `sudo ufw status`
3. Verify DNS configuration
4. Check SSL certificate status: `sudo certbot certificates`

### Database Connection Issues
**Symptoms**: Application logs show database connection errors

**Diagnosis**:
```bash
# Check MySQL status
sudo systemctl status mysql

# Test database connection
mysql -u nirvana_user -p nirvana_organics_production

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log
```

**Solutions**:
1. Restart MySQL: `sudo systemctl restart mysql`
2. Verify database user permissions
3. Check environment file database configuration
4. Ensure database exists and is accessible

### Admin Panel Not Loading
**Symptoms**: Admin panel shows 404 error or blank page

**Diagnosis**:
```bash
# Check if admin files exist
ls -la /var/www/nirvana-backend/dist/admin/

# Check Nginx configuration for admin routing
sudo grep -n "location /admin" /etc/nginx/sites-available/nirvana-organics

# Check Nginx access logs
sudo tail -f /var/log/nginx/access.log | grep admin
```

**Solutions**:
1. Verify admin build files are present
2. Check Nginx configuration for admin routing
3. Restart Nginx: `sudo systemctl reload nginx`
4. Clear browser cache

### SSL Certificate Issues
**Symptoms**: Browser shows "Not secure" or certificate errors

**Diagnosis**:
```bash
# Check certificate status
sudo certbot certificates

# Test SSL configuration
openssl s_client -connect shopnirvanaorganics.com:443

# Check Nginx SSL configuration
sudo grep -n ssl_certificate /etc/nginx/sites-available/nirvana-organics
```

**Solutions**:
1. Renew certificate: `sudo certbot renew`
2. Restart Nginx: `sudo systemctl restart nginx`
3. Check domain DNS configuration
4. Verify certificate files exist and are readable

## 📞 Getting Additional Help

### Log Collection for Support
If you need to contact support, collect these logs:

```bash
# Create log collection directory
mkdir ~/support-logs

# Collect application logs
pm2 logs --lines 200 > ~/support-logs/application.log

# Collect Nginx logs
sudo cp /var/log/nginx/access.log ~/support-logs/
sudo cp /var/log/nginx/error.log ~/support-logs/

# Collect system information
uname -a > ~/support-logs/system-info.txt
df -h >> ~/support-logs/system-info.txt
free -h >> ~/support-logs/system-info.txt

# Create archive
tar -czf support-logs.tar.gz ~/support-logs/
```

### System Information Commands
```bash
# Check versions
node --version
npm --version
mysql --version
nginx -v
pm2 --version

# Check service status
sudo systemctl status nginx
sudo systemctl status mysql
pm2 status

# Check network configuration
ip addr show
sudo netstat -tlnp
```

---

**Last Updated**: ${new Date().toISOString().split('T')[0]}
**Guide Version**: 1.0.0
**Deployment Type**: Manual VPS Deployment
