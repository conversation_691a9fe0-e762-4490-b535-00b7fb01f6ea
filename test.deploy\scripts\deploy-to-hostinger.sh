#!/bin/bash

# ============================================================================
# Nirvana Backend Deployment to Hostinger Ubuntu 24.04 LTS
# ============================================================================

set -e  # Exit on any error

# Configuration
HOSTINGER_SERVER="root@srv928821"  # Update with your actual Hostinger server details
PROJECT_NAME="nirvana-backend"
REMOTE_PATH="/var/www/$PROJECT_NAME"
LOCAL_PATH="."

echo "🚀 Deploying Nirvana Backend to Hostinger Ubuntu 24.04 LTS"
echo "📡 Target Server: $HOSTINGER_SERVER"
echo "📁 Remote Path: $REMOTE_PATH"
echo ""

# Step 1: Test connection
echo "1️⃣ Testing connection to Hostinger server..."
if ssh -o ConnectTimeout=10 $HOSTINGER_SERVER "echo 'Connection successful'"; then
    echo "✅ SSH connection established"
else
    echo "❌ Failed to connect to $HOSTINGER_SERVER"
    echo "💡 Please check:"
    echo "   - Server IP/hostname is correct"
    echo "   - SSH key is configured"
    echo "   - Server is running"
    exit 1
fi

# Step 2: Create project directory
echo ""
echo "2️⃣ Creating project directory on Hostinger..."
ssh $HOSTINGER_SERVER << EOF
    # Create main project directory
    sudo mkdir -p $REMOTE_PATH
    
    # Set ownership to current user
    sudo chown -R \$USER:\$USER $REMOTE_PATH
    
    # Create subdirectories
    mkdir -p $REMOTE_PATH/{logs,uploads,public/uploads,backups}
    
    echo "✅ Directory structure created at $REMOTE_PATH"
    ls -la $REMOTE_PATH
EOF

# Step 3: Copy files to server
echo ""
echo "3️⃣ Copying files to Hostinger server..."

# Copy main application files
echo "📦 Copying server files..."
rsync -avz --progress \
    --exclude='node_modules' \
    --exclude='.git' \
    --exclude='dist' \
    --exclude='dist-admin' \
    --exclude='logs' \
    --exclude='*.log' \
    --exclude='.DS_Store' \
    --exclude='Thumbs.db' \
    server/ $HOSTINGER_SERVER:$REMOTE_PATH/

# Copy configuration files
echo "📦 Copying configuration files..."
scp package.json $HOSTINGER_SERVER:$REMOTE_PATH/
scp package-lock.json $HOSTINGER_SERVER:$REMOTE_PATH/
scp .env.production $HOSTINGER_SERVER:$REMOTE_PATH/

# Copy scripts
echo "📦 Copying deployment scripts..."
scp -r scripts/ $HOSTINGER_SERVER:$REMOTE_PATH/

# Copy uploads if they exist
if [ -d "uploads" ]; then
    echo "📦 Copying uploads directory..."
    rsync -avz uploads/ $HOSTINGER_SERVER:$REMOTE_PATH/uploads/
fi

# Copy public files if they exist
if [ -d "public" ]; then
    echo "📦 Copying public files..."
    rsync -avz public/ $HOSTINGER_SERVER:$REMOTE_PATH/public/
fi

echo "✅ Files copied successfully"

# Step 4: Setup Node.js and dependencies
echo ""
echo "4️⃣ Setting up Node.js and dependencies on Ubuntu 24.04..."
ssh $HOSTINGER_SERVER << 'EOF'
cd /var/www/nirvana-backend

# Update system packages
sudo apt update

# Install Node.js 20.x (LTS) if not present
if ! command -v node &> /dev/null; then
    echo "Installing Node.js 20.x LTS..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
else
    echo "Node.js already installed: $(node --version)"
fi

# Install build essentials for native modules
sudo apt-get install -y build-essential python3

# Install dependencies
echo "Installing npm dependencies..."
npm install --production

# Copy production environment
if [ -f .env.production ]; then
    cp .env.production .env
    echo "✅ Production environment configured"
else
    echo "⚠️ .env.production not found, using default .env"
fi

# Set proper permissions
chmod +x scripts/*.js 2>/dev/null || true
chmod 755 uploads logs public/uploads 2>/dev/null || true

echo "✅ Dependencies installed successfully"
EOF

# Step 5: Install and configure PM2
echo ""
echo "5️⃣ Installing and configuring PM2 process manager..."
ssh $HOSTINGER_SERVER << 'EOF'
cd /var/www/nirvana-backend

# Install PM2 globally
sudo npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << 'PM2_EOF'
module.exports = {
  apps: [{
    name: 'nirvana-backend',
    script: 'index.js',
    cwd: '/var/www/nirvana-backend',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
PM2_EOF

# Stop existing process if running
pm2 stop nirvana-backend 2>/dev/null || echo "No existing process to stop"
pm2 delete nirvana-backend 2>/dev/null || echo "No existing process to delete"

# Start the application
pm2 start ecosystem.config.js

# Setup PM2 to start on boot
sudo pm2 startup systemd -u $USER --hp $HOME
pm2 save

echo "✅ PM2 configured and application started"
EOF

# Step 6: Configure Nginx reverse proxy
echo ""
echo "6️⃣ Configuring Nginx reverse proxy..."
ssh $HOSTINGER_SERVER << 'EOF'
# Install Nginx if not present
if ! command -v nginx &> /dev/null; then
    echo "Installing Nginx..."
    sudo apt update
    sudo apt install -y nginx
fi

# Create Nginx configuration for the backend
sudo tee /etc/nginx/sites-available/nirvana-backend > /dev/null << 'NGINX_EOF'
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;  # Update with your domain

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # API routes
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Static files (uploads, images, etc.)
    location /uploads {
        alias /var/www/nirvana-backend/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://localhost:5000/health;
        access_log off;
    }

    # Default location for frontend (if needed)
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
NGINX_EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/nirvana-backend /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx

# Enable Nginx to start on boot
sudo systemctl enable nginx

echo "✅ Nginx configured successfully"
EOF

# Step 7: Setup firewall
echo ""
echo "7️⃣ Configuring UFW firewall..."
ssh $HOSTINGER_SERVER << 'EOF'
# Enable UFW if not already enabled
sudo ufw --force enable

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow Node.js port (for direct access if needed)
sudo ufw allow 5000

echo "✅ Firewall configured"
sudo ufw status
EOF

# Step 8: Final checks and status
echo ""
echo "8️⃣ Running final checks..."
ssh $HOSTINGER_SERVER << 'EOF'
cd /var/www/nirvana-backend

echo "📊 PM2 Status:"
pm2 status

echo ""
echo "🌐 Server Status:"
curl -s http://localhost:5000/api/health || echo "⚠️ Health check failed - server may still be starting"

echo ""
echo "📁 Directory Structure:"
ls -la /var/www/nirvana-backend

echo ""
echo "🔧 System Information:"
echo "Ubuntu version: $(lsb_release -d | cut -f2)"
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"
echo "PM2 version: $(pm2 --version)"

echo ""
echo "💾 Disk Usage:"
df -h /var/www/nirvana-backend
EOF

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Update your domain in /etc/nginx/sites-available/nirvana-backend"
echo "2. Update .env file with production database credentials"
echo "3. Setup SSL certificate with Let's Encrypt"
echo "4. Test your API endpoints"
echo ""
echo "🔧 Useful Commands:"
echo "ssh $HOSTINGER_SERVER 'pm2 status'"
echo "ssh $HOSTINGER_SERVER 'pm2 logs nirvana-backend'"
echo "ssh $HOSTINGER_SERVER 'pm2 restart nirvana-backend'"
echo "ssh $HOSTINGER_SERVER 'sudo systemctl status nginx'"
echo ""
echo "🌐 Your backend should be accessible at:"
echo "http://your-server-ip:5000/api/health"
echo "http://your-domain.com/api/health (after domain configuration)"
