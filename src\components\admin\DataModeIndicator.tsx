import React, { useState } from 'react';
import { useDataEnvironment } from '../../contexts/DataEnvironmentContext';
import {
  CircleStackIcon,
  BeakerIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface DataModeIndicatorProps {
  showToggle?: boolean;
  showDetails?: boolean;
  className?: string;
}

const DataModeIndicator: React.FC<DataModeIndicatorProps> = ({ 
  showToggle = true, 
  showDetails = false,
  className = ''
}) => {
  const { 
    mode, 
    isMockMode, 
    isLoading, 
    error, 
    toggleDataMode, 
    clearError 
  } = useDataEnvironment();
  
  const [isToggling, setIsToggling] = useState(false);

  const handleToggle = async () => {
    if (isLoading || isToggling) return;
    
    setIsToggling(true);
    try {
      await toggleDataMode();
    } catch (error) {
      console.error('Failed to toggle data mode:', error);
    } finally {
      setIsToggling(false);
    }
  };

  const getModeIcon = () => {
    if (isMockMode) {
      return <BeakerIcon className="h-5 w-5" />;
    }
    return <CircleStackIcon className="h-5 w-5" />;
  };

  const getModeColor = () => {
    if (isMockMode) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
    return 'bg-green-100 text-green-800 border-green-200';
  };

  const getModeText = () => {
    if (isMockMode) {
      return 'Mock Data Mode';
    }
    return 'Real Data Mode';
  };

  const getDescription = () => {
    if (isMockMode) {
      return 'Currently viewing test/mock data. Changes will not affect production data.';
    }
    return 'Currently viewing real production data. Changes will affect live data.';
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Mode Indicator Badge */}
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getModeColor()}`}>
        {isLoading || isToggling ? (
          <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <span className="mr-2">{getModeIcon()}</span>
        )}
        {getModeText()}
      </div>

      {/* Toggle Button */}
      {showToggle && (
        <button
          onClick={handleToggle}
          disabled={isLoading || isToggling}
          className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          title={`Switch to ${isMockMode ? 'real' : 'mock'} data mode`}
        >
          {isToggling ? (
            <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <ArrowPathIcon className="h-4 w-4 mr-2" />
          )}
          Switch to {isMockMode ? 'Real' : 'Mock'} Data
        </button>
      )}

      {/* Error Display */}
      {error && (
        <div className="inline-flex items-center px-3 py-1 rounded-md bg-red-100 text-red-800 text-sm">
          <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
          <span className="mr-2">{error}</span>
          <button
            onClick={clearError}
            className="text-red-600 hover:text-red-800"
            title="Clear error"
          >
            ×
          </button>
        </div>
      )}

      {/* Detailed Information */}
      {showDetails && (
        <div className="flex-1">
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                {isMockMode ? (
                  <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />
                ) : (
                  <CheckCircleIcon className="h-6 w-6 text-green-500" />
                )}
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900 mb-1">
                  Data Environment: {getModeText()}
                </h4>
                <p className="text-sm text-gray-600">
                  {getDescription()}
                </p>
                {isMockMode && (
                  <div className="mt-2 text-xs text-yellow-700 bg-yellow-50 rounded px-2 py-1">
                    <strong>Note:</strong> Mock data is for testing purposes only. 
                    Switch to Real Data mode to work with production data.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataModeIndicator;
