const express = require('express');
const router = express.Router();
const userManagementController = require('../controllers/userManagementController');
const { authenticate, requireAdmin } = require('../middleware/auth');
const {
  validateCreateUser,
  validateUpdateUser,
  validateUserId,
  validateBulkOperation,
  validatePasswordReset,
  validateUserQuery,
  requireSuperAdmin,
  requireAdminOrManager,
  preventSelfModification
} = require('../middleware/userValidation');

// All routes require authentication and admin privileges
router.use(authenticate);
router.use(requireAdmin);

// @route   GET /api/admin/users
// @desc    Get all users with filtering and pagination
// @access  Private (Admin/Manager)
router.get('/', 
  requireAdminOrManager,
  validateUserQuery,
  userManagementController.getUsers
);

// @route   GET /api/admin/users/statistics
// @desc    Get user statistics
// @access  Private (Admin/Manager)
router.get('/statistics',
  requireAdminOrManager,
  userManagementController.getUserStatistics
);

// @route   GET /api/admin/users/:userId
// @desc    Get single user by ID
// @access  Private (Admin/Manager)
router.get('/:userId',
  requireAdminOrManager,
  validateUserId,
  userManagementController.getUserById
);

// @route   POST /api/admin/users
// @desc    Create new user
// @access  Private (Admin/Manager)
router.post('/',
  requireAdminOrManager,
  validateCreateUser,
  userManagementController.createUser
);

// @route   PUT /api/admin/users/:userId
// @desc    Update user
// @access  Private (Admin/Manager)
router.put('/:userId',
  requireAdminOrManager,
  validateUpdateUser,
  userManagementController.updateUser
);

// @route   DELETE /api/admin/users/:userId
// @desc    Delete (deactivate) user
// @access  Private (Admin/Manager)
router.delete('/:userId',
  requireAdminOrManager,
  validateUserId,
  preventSelfModification,
  userManagementController.deleteUser
);

// @route   POST /api/admin/users/bulk
// @desc    Bulk operations on users
// @access  Private (Admin/Manager)
router.post('/bulk',
  requireAdminOrManager,
  validateBulkOperation,
  preventSelfModification,
  userManagementController.bulkUpdateUsers
);

// @route   POST /api/admin/users/:userId/reset-password
// @desc    Reset user password
// @access  Private (Admin/Manager)
router.post('/:userId/reset-password',
  requireAdminOrManager,
  validatePasswordReset,
  userManagementController.resetUserPassword
);

module.exports = router;
