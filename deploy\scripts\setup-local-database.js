#!/usr/bin/env node

/**
 * Local Database Setup Script
 * Creates database and tables for local development
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
};

async function setupDatabase() {
  // Load environment variables
  require('dotenv').config();

  log.header('🗄️ Setting up Local Database');

  try {
    // Connect to MySQL server (without specifying database)
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || ''
    });

    log.success('Connected to MySQL server');

    // Create database if it doesn't exist
    const dbName = process.env.DB_NAME || 'nirvana_organics_local';
    
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    log.success(`Database '${dbName}' created or already exists`);

    // Switch to the database
    await connection.execute(`USE \`${dbName}\``);
    log.success(`Switched to database '${dbName}'`);

    // Create basic tables
    log.header('📋 Creating Database Tables');

    // Users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        firstName VARCHAR(100) NOT NULL,
        lastName VARCHAR(100) NOT NULL,
        role ENUM('customer', 'admin', 'manager') DEFAULT 'customer',
        isActive BOOLEAN DEFAULT true,
        emailVerified BOOLEAN DEFAULT false,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    log.success('Users table created');

    // Categories table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        image VARCHAR(500),
        isActive BOOLEAN DEFAULT true,
        sortOrder INT DEFAULT 0,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    log.success('Categories table created');

    // Products table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        shortDescription VARCHAR(500),
        price DECIMAL(10,2) NOT NULL,
        comparePrice DECIMAL(10,2),
        sku VARCHAR(100) UNIQUE,
        stock INT DEFAULT 0,
        images JSON,
        categoryId INT,
        isActive BOOLEAN DEFAULT true,
        isFeatured BOOLEAN DEFAULT false,
        weight DECIMAL(8,2),
        dimensions VARCHAR(100),
        tags JSON,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (categoryId) REFERENCES categories(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    log.success('Products table created');

    // Orders table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        orderNumber VARCHAR(50) UNIQUE NOT NULL,
        userId INT,
        status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
        totalAmount DECIMAL(10,2) NOT NULL,
        shippingAmount DECIMAL(10,2) DEFAULT 0,
        taxAmount DECIMAL(10,2) DEFAULT 0,
        discountAmount DECIMAL(10,2) DEFAULT 0,
        paymentStatus ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
        paymentMethod VARCHAR(50),
        shippingAddress JSON,
        billingAddress JSON,
        notes TEXT,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    log.success('Orders table created');

    // Order items table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS order_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        orderId INT NOT NULL,
        productId INT,
        productName VARCHAR(255) NOT NULL,
        productSku VARCHAR(100),
        quantity INT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        totalPrice DECIMAL(10,2) NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (orderId) REFERENCES orders(id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    log.success('Order items table created');

    // Create admin user
    log.header('👤 Creating Admin User');
    
    const bcrypt = require('bcryptjs');
    const adminPassword = await bcrypt.hash('Admin123!@#', 12);
    
    await connection.execute(`
      INSERT IGNORE INTO users (email, password, firstName, lastName, role, emailVerified)
      VALUES (?, ?, 'Admin', 'User', 'admin', true)
    `, ['<EMAIL>', adminPassword]);
    
    log.success('Admin user created (email: <EMAIL>, password: Admin123!@#)');

    await connection.end();
    log.success('Database setup completed successfully!');

  } catch (error) {
    log.error(`Database setup failed: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      log.error('MySQL server is not running or connection details are incorrect');
      log.info('Please check:');
      log.info('1. MySQL/MariaDB is installed and running');
      log.info('2. Database credentials in .env file are correct');
      log.info('3. Database server is accessible on the specified host and port');
    }
    
    process.exit(1);
  }
}

// Run setup
setupDatabase().catch(error => {
  console.error('Setup failed:', error.message);
  process.exit(1);
});
