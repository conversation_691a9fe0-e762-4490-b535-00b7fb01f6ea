const { Order, OrderItem, Cart, CartItem, Product, User, Coupon, CouponUsage } = require('../models');
const { validationResult } = require('express-validator');
const SquareService = require('../services/squareService');
const CustomerAnalyticsService = require('../services/customerAnalyticsService');
const emailNotificationService = require('../services/emailNotificationService');
const whatsappService = require('../services/whatsappService');
const uspsService = require('../services/uspsService');
const realTimeService = require('../services/realTimeService');
const nodemailer = require('nodemailer');

// Email transporter setup
const createEmailTransporter = () => {
  if (process.env.NODE_ENV === 'test') {
    return nodemailer.createTransport({
      streamTransport: true,
      newline: 'unix',
      buffer: true
    });
  }

  return nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Create new order (checkout)
const createOrder = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      billingAddress,
      shippingAddress,
      paymentMethod,
      notes,
      sourceId,
      customerGender,
      shippingMethod,
      shippingCost
    } = req.body;

    // Determine traffic source from request
    const trafficData = CustomerAnalyticsService.determineTrafficSource(req);

    // Get user's cart
    const cart = await Cart.findOne({ where: { userId: req.user.id } });
    if (!cart) {
      return res.status(400).json({
        success: false,
        message: 'Cart not found'
      });
    }

    // Get cart items separately
    const cartItems = await CartItem.findAll({ where: { cartId: cart.id } });
    if (!cartItems || cartItems.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Cart is empty'
      });
    }
    const productIds = cartItems.map(item => item.productId);
    const products = await Product.findAll({ where: { id: productIds } });

    // Validate stock availability for all items
    for (const item of cartItems) {
      const product = products.find(p => p.id === item.productId);
      if (!product) {
        return res.status(400).json({
          success: false,
          message: `Product not found`
        });
      }

      if (product.trackQuantity && product.quantity < item.quantity) {
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for ${product.name}`
        });
      }
    }

    // Create order items with product snapshots
    const orderItems = cartItems.map(item => {
      const product = products.find(p => p.id === item.productId);
      return {
        productId: item.productId,
        productSnapshot: {
          name: product.name,
          sku: product.sku,
          image: product.images && product.images[0] ? product.images[0].url : null
        },
        variant: item.variant,
        quantity: item.quantity,
        price: item.price,
        total: item.price * item.quantity
      };
    });

    // Check if this is customer's first order
    const isFirstOrder = await CustomerAnalyticsService.isFirstOrder(req.user.id);

    // Determine current membership type
    const currentMembershipType = await CustomerAnalyticsService.determineMembershipType(req.user.id);

    // Calculate lifetime value
    const lifetimeValue = await CustomerAnalyticsService.calculateLifetimeValue(req.user.id);

    // Calculate order totals from cart items
    const subtotal = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.08; // 8% tax rate
    const shipping = shippingCost || 9.99;
    const discount = 0; // No discount for now
    const total = subtotal + tax + shipping - discount;

    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

    // Create order
    const order = await Order.create({
      userId: req.user.id,
      orderNumber,
      email: req.user.email,
      items: orderItems,
      billingAddress,
      shippingAddress,
      subtotal: subtotal,
      tax: tax,
      shipping: shipping,
      discount: discount,
      total: total,
      paymentMethod,
      notes,
      couponCode: cart.couponCode,
      // Enhanced customer data
      customerGender: customerGender || null,
      customerMembershipType: currentMembershipType,
      trafficSource: trafficData.source,
      referralSource: trafficData.details,
      shippingMethod: shippingMethod || 'regular',
      shippingCost: shippingCost || cart.shipping,
      isFirstOrder,
      customerLifetimeValue: lifetimeValue,
      orderSource: 'web'
    });

    // Process Square payment
    let squarePaymentResult = null;
    if (paymentMethod === 'square' && sourceId) {
      squarePaymentResult = await SquareService.createPayment({
        sourceId,
        amountMoney: {
          amount: order.total,
          currency: 'USD'
        },
        orderId: order.orderNumber,
        buyerEmailAddress: req.user.email,
        billingAddress,
        shippingAddress
      });

      if (squarePaymentResult.success) {
        order.squarePaymentId = squarePaymentResult.payment.id;
        order.paymentId = squarePaymentResult.payment.id;
        order.paymentStatus = squarePaymentResult.payment.status === 'COMPLETED' ? 'paid' : 'pending';

        // Create Square invoice if payment is successful
        try {
          const invoiceResult = await SquareService.createInvoice(await realTimeService.enrichOrderData(order));
          if (invoiceResult.success) {
            order.invoiceId = invoiceResult.invoiceId;
            order.invoiceUrl = invoiceResult.invoiceUrl;
            console.log(`Square invoice created for order: ${order.orderNumber}`);
          }
        } catch (invoiceError) {
          console.error('Failed to create Square invoice:', invoiceError);
          // Don't fail the order for invoice errors
        }
      } else {
        return res.status(400).json({
          success: false,
          message: 'Payment processing failed',
          error: squarePaymentResult.error
        });
      }
    }

    await order.save();

    // Update product quantities
    for (const item of cartItems) {
      const product = products.find(p => p.id === item.productId);
      if (product && product.trackQuantity) {
        await product.update({
          quantity: product.quantity - item.quantity,
          salesCount: product.salesCount + item.quantity
        });
      }
    }

    // Clear cart items
    await CartItem.destroy({ where: { cartId: cart.id } });

    // Update customer membership type and user data after successful order
    try {
      const updatedMembershipType = await CustomerAnalyticsService.updateCustomerMembership(req.user.id);
      const newLifetimeValue = await CustomerAnalyticsService.calculateLifetimeValue(req.user.id);

      // Update user record with new data
      await User.update({
        membershipType: updatedMembershipType,
        trafficSource: trafficData.source,
        lastOrderAt: new Date()
      }, {
        where: { id: req.user.id }
      });

      // Update order with final membership type if it changed
      if (updatedMembershipType !== currentMembershipType) {
        await order.update({
          customerMembershipType: updatedMembershipType,
          customerLifetimeValue: newLifetimeValue
        });
      }
    } catch (analyticsError) {
      console.error('Failed to update customer analytics:', analyticsError);
      // Don't fail the order for analytics errors
    }

    // Send enhanced email notifications
    try {
      // Get complete order data for email notifications
      const completeOrder = await Order.findByPk(order.id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['firstName', 'lastName', 'email', 'membershipType']
          },
          {
            model: OrderItem,
            as: 'items',
            include: [
              {
                model: Product,
                as: 'product',
                attributes: ['name', 'sku']
              }
            ]
          }
        ]
      });

      // Prepare order data for email service
      const orderData = await realTimeService.enrichOrderData(completeOrder);

      // Send customer confirmation email
      await emailNotificationService.sendOrderConfirmation(orderData);

      // Send admin notification email
      await emailNotificationService.sendAdminOrderNotification(orderData);

      // Send WhatsApp confirmation if customer phone is available
      if (req.user.phone || (billingAddress && billingAddress.phone)) {
        const customerPhone = req.user.phone || billingAddress.phone;
        try {
          await whatsappService.sendOrderConfirmation(customerPhone, orderData);
          console.log(`WhatsApp confirmation sent for order: ${order.orderNumber}`);
        } catch (whatsappError) {
          console.error('Failed to send WhatsApp confirmation:', whatsappError);
          // Don't fail the order for WhatsApp errors
        }
      }

      // Broadcast real-time order update to admin dashboard
      await realTimeService.broadcastNewOrder(orderData);

      console.log(`Notifications sent for order: ${order.orderNumber}`);
    } catch (emailError) {
      console.error('Failed to send email notifications:', emailError);
      // Don't fail the order for email errors
    }

    // Get user details for response
    const user = await User.findByPk(req.user.id, {
      attributes: ['id', 'firstName', 'lastName', 'email']
    });

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: {
        order: {
          ...order.toJSON(),
          user
        },
        paymentResult: squarePaymentResult
      }
    });

  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: error.message
    });
  }
};

// Get user's orders
const getUserOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;

    // Build filter
    const where = { userId: req.user.id };
    if (status) where.status = status;

    // Calculate pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get orders
    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit),
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    // Get total count
    const total = count;
    const pages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages
        }
      }
    });

  } catch (error) {
    console.error('Get user orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
};

// Get single order
const getOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await Order.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user owns the order or is admin
    if (order.userId !== req.user.id &&
        !['admin', 'super_admin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: order
    });

  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order',
      error: error.message
    });
  }
};

// Update order status (Admin only)
const updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, trackingNumber, notes } = req.body;

    const order = await Order.findByPk(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Update order
    const updateData = { status };
    if (trackingNumber) updateData.trackingNumber = trackingNumber;
    if (notes) updateData.adminNotes = notes;

    await order.update(updateData);

    // Reload the order to get the updated data
    await order.reload();
    console.log('🔍 DEBUG: Updated order status:', order.status);

    // Send notifications for status changes
    try {
      const user = await User.findByPk(order.userId);
      const orderData = await realTimeService.enrichOrderData(order);

      // Send email notification for status update
      await emailNotificationService.sendStatusUpdate(orderData, status, { trackingNumber });

      // Send WhatsApp notifications for specific status changes
      const customerPhone = user?.phone || (order.billingAddress && order.billingAddress.phone);

      if (customerPhone) {
        if (status === 'shipped' && trackingNumber) {
          await whatsappService.sendShippingNotification(customerPhone, orderData, trackingNumber);
        } else if (status === 'delivered') {
          await whatsappService.sendDeliveryConfirmation(customerPhone, orderData);
        }
      }

      console.log(`Status update notifications sent for order: ${order.orderNumber}`);
    } catch (notificationError) {
      console.error('Failed to send status update notifications:', notificationError);
      // Don't fail the order update for notification errors
    }

    console.log('🔍 DEBUG: Returning order data:', JSON.stringify(order, null, 2));
    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: order
    });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: error.message
    });
  }
};

// Cancel order
const cancelOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const order = await Order.findByPk(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user owns the order
    if (order.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Check if order can be cancelled
    if (!order.canBeCancelled()) {
      return res.status(400).json({
        success: false,
        message: 'Order cannot be cancelled at this stage'
      });
    }

    // Update order status
    const updateData = { status: 'cancelled' };
    if (reason) updateData.notes = reason;
    await order.update(updateData);

    // Restore product quantities
    const orderItems = order.items || [];
    for (const item of orderItems) {
      const product = await Product.findByPk(item.productId);
      if (product && product.trackQuantity) {
        await product.update({
          quantity: product.quantity + item.quantity,
          salesCount: product.salesCount - item.quantity
        });
      }
    }

    res.json({
      success: true,
      message: 'Order cancelled successfully',
      data: { order }
    });

  } catch (error) {
    console.error('Cancel order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel order',
      error: error.message
    });
  }
};

// Get all orders (Admin only)
const getAllOrders = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status, 
      paymentStatus,
      startDate,
      endDate 
    } = req.query;

    // Build filter
    const where = {};
    if (status) where.status = status;
    if (paymentStatus) where.paymentStatus = paymentStatus;
    if (startDate && endDate) {
      where.createdAt = {
        [require('sequelize').Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    // Calculate pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get orders
    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // Get total count
    const total = count;
    const pages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages
        }
      }
    });

  } catch (error) {
    console.error('Get all orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
};

module.exports = {
  createOrder,
  getUserOrders,
  getOrder,
  updateOrderStatus,
  cancelOrder,
  getAllOrders
};
