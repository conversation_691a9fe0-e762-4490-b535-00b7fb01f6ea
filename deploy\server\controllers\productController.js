const { Product, Category, Review } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// Get all products with filtering, sorting, and pagination
const getProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 12,
      category,
      subcategory,
      cannabinoid,
      strain,
      minPrice,
      maxPrice,
      inStock,
      featured,
      bestSeller,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object for Sequelize
    const where = { isActive: true };

    if (category) {
      where.categoryId = category;
    }

    if (cannabinoid) {
      const cannabinoids = Array.isArray(cannabinoid) ? cannabinoid : [cannabinoid];
      where.cannabinoid = { [Op.in]: cannabinoids };
    }

    if (strain) {
      const strains = Array.isArray(strain) ? strain : [strain];
      where.strain = { [Op.in]: strains };
    }

    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price[Op.gte] = parseFloat(minPrice);
      if (maxPrice) where.price[Op.lte] = parseFloat(maxPrice);
    }

    if (inStock === 'true') {
      where[Op.or] = [
        { trackQuantity: false },
        { trackQuantity: true, quantity: { [Op.gt]: 0 } }
      ];
    }

    if (featured === 'true') {
      where.isFeatured = true;
    }

    if (bestSeller === 'true') {
      // For best sellers, we'll order by salesCount instead of filtering
      // since we don't have a bestSeller field in the database
    }

    if (search) {
      where[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
        { shortDescription: { [Op.like]: `%${search}%` } }
      ];
    }

    // Build sort order for Sequelize
    const order = [[sortBy, sortOrder.toUpperCase()]];

    // Calculate pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with Sequelize
    const { count, rows: products } = await Product.findAndCountAll({
      where,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ],
      order,
      offset,
      limit: parseInt(limit)
    });

    // Get total count for pagination
    const total = count;
    const pages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages
        }
      }
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: error.message
    });
  }
};

// Get single product by ID
const getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id, {
      where: { isActive: true },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Get product by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve product',
      error: error.message
    });
  }
};

// Get single product by slug
const getProductBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const product = await Product.findOne({
      where: { slug, isActive: true },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Increment view count
    product.viewCount += 1;
    await product.save();

    // Get related products
    const relatedProducts = await Product.findAll({
      where: {
        id: { [Op.ne]: product.id },
        categoryId: product.categoryId,
        isActive: true
      },
      attributes: ['id', 'name', 'slug', 'price', 'images', 'rating', 'reviewCount'],
      limit: 4
    });

    // Get product reviews
    const reviews = await Review.findAll({
      where: {
        productId: product.id,
        status: 'approved'
      },
      include: [
        {
          model: require('../models').User,
          as: 'user',
          attributes: ['firstName', 'lastName']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    res.json({
      success: true,
      data: {
        product,
        relatedProducts,
        reviews
      }
    });

  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product',
      error: error.message
    });
  }
};

// Create new product (Admin only)
const createProduct = async (req, res) => {
  console.log('🔍 DEBUG: createProduct method called');
  console.log('🔍 DEBUG: req.body:', req.body);
  console.log('🔍 DEBUG: req.user:', req.user);
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const productData = req.body;

    // Handle uploaded images
    if (req.files && req.files.length > 0) {
      const { getFileUrl } = require('../middleware/upload');
      productData.images = req.files.map(file => ({
        url: getFileUrl(file.filename),
        filename: file.filename,
        originalName: file.originalname,
        size: file.size
      }));
    } else if (req.file) {
      const { getFileUrl } = require('../middleware/upload');
      productData.images = [{
        url: getFileUrl(req.file.filename),
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size
      }];
    }

    // Generate slug from name if not provided
    if (!productData.slug) {
      productData.slug = productData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Check if slug already exists
    const existingProduct = await Product.findOne({ where: { slug: productData.slug } });
    if (existingProduct) {
      productData.slug = `${productData.slug}-${Date.now()}`;
    }

    const product = await Product.create(productData);

    // Get product with category information
    const productWithCategory = await Product.findByPk(product.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: { product: productWithCategory }
    });

  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create product',
      error: error.message
    });
  }
};

// Update product (Admin only)
const updateProduct = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    // Handle uploaded images
    if (req.files && req.files.length > 0) {
      const { getFileUrl } = require('../middleware/upload');
      updateData.images = req.files.map(file => ({
        url: getFileUrl(file.filename),
        filename: file.filename,
        originalName: file.originalname,
        size: file.size
      }));
    } else if (req.file) {
      const { getFileUrl } = require('../middleware/upload');
      updateData.images = [{
        url: getFileUrl(req.file.filename),
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size
      }];
    }

    // If name is being updated, regenerate slug
    if (updateData.name && !updateData.slug) {
      updateData.slug = updateData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Check if slug already exists (excluding current product)
    if (updateData.slug) {
      const existingProduct = await Product.findOne({
        where: {
          slug: updateData.slug,
          id: { [Op.ne]: id }
        }
      });
      if (existingProduct) {
        updateData.slug = `${updateData.slug}-${Date.now()}`;
      }
    }

    // Update the product
    const [updatedRowsCount] = await Product.update(updateData, {
      where: { id: parseInt(id) }
    });

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Get updated product with category information
    const product = await Product.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ]
    });

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: { product }
    });

  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update product',
      error: error.message
    });
  }
};

// Delete product (Admin only)
const deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    // First check if product exists
    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Delete the product
    await Product.destroy({
      where: { id: parseInt(id) }
    });

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete product',
      error: error.message
    });
  }
};

// Get featured products
const getFeaturedProducts = async (req, res) => {
  try {
    const { limit = 8 } = req.query;

    const products = await Product.findAll({
      where: {
        isFeatured: true,
        isActive: true
      },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: { products }
    });

  } catch (error) {
    console.error('Get featured products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch featured products',
      error: error.message
    });
  }
};

// Get best seller products
const getBestSellerProducts = async (req, res) => {
  try {
    const { limit = 8 } = req.query;

    const products = await Product.findAll({
      where: {
        isActive: true
      },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ],
      order: [['salesCount', 'DESC']],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: { products }
    });

  } catch (error) {
    console.error('Get best seller products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch best seller products',
      error: error.message
    });
  }
};

// Search products
const searchProducts = async (req, res) => {
  try {
    const { q, limit = 10 } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters long'
      });
    }

    const products = await Product.findAll({
      where: {
        isActive: true,
        [Op.or]: [
          { name: { [Op.like]: `%${q}%` } },
          { description: { [Op.like]: `%${q}%` } },
          { shortDescription: { [Op.like]: `%${q}%` } }
        ]
      },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ],
      attributes: ['id', 'name', 'slug', 'price', 'images', 'rating', 'reviewCount'],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: { products }
    });

  } catch (error) {
    console.error('Search products error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed',
      error: error.message
    });
  }
};

// Get product variants
const getProductVariants = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id, {
      attributes: ['id', 'name', 'variants']
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: {
        productId: product.id,
        productName: product.name,
        variants: product.variants || []
      }
    });

  } catch (error) {
    console.error('Get product variants error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product variants',
      error: error.message
    });
  }
};

// Add product variant
const addProductVariant = async (req, res) => {
  try {
    const { id } = req.params;
    const variantData = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    const variants = product.variants || [];

    // Generate variant ID
    const variantId = Date.now().toString();
    const newVariant = {
      id: variantId,
      ...variantData,
      createdAt: new Date()
    };

    variants.push(newVariant);
    await product.update({ variants });

    res.status(201).json({
      success: true,
      message: 'Product variant added successfully',
      data: { variant: newVariant }
    });

  } catch (error) {
    console.error('Add product variant error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add product variant',
      error: error.message
    });
  }
};

// Update product variant
const updateProductVariant = async (req, res) => {
  try {
    const { id, variantId } = req.params;
    const updateData = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    const variants = product.variants || [];
    const variantIndex = variants.findIndex(v => v.id === variantId);

    if (variantIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Product variant not found'
      });
    }

    // Update variant
    variants[variantIndex] = {
      ...variants[variantIndex],
      ...updateData,
      updatedAt: new Date()
    };

    await product.update({ variants });

    res.json({
      success: true,
      message: 'Product variant updated successfully',
      data: { variant: variants[variantIndex] }
    });

  } catch (error) {
    console.error('Update product variant error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update product variant',
      error: error.message
    });
  }
};

// Delete product variant
const deleteProductVariant = async (req, res) => {
  try {
    const { id, variantId } = req.params;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    const variants = product.variants || [];
    const variantIndex = variants.findIndex(v => v.id === variantId);

    if (variantIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Product variant not found'
      });
    }

    // Remove variant
    variants.splice(variantIndex, 1);
    await product.update({ variants });

    res.json({
      success: true,
      message: 'Product variant deleted successfully'
    });

  } catch (error) {
    console.error('Delete product variant error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete product variant',
      error: error.message
    });
  }
};

module.exports = {
  getProducts,
  getProductById,
  getProductBySlug,
  createProduct,
  updateProduct,
  deleteProduct,
  getFeaturedProducts,
  getBestSellerProducts,
  searchProducts,
  getProductVariants,
  addProductVariant,
  updateProductVariant,
  deleteProductVariant
};
