#!/bin/bash

# Maintenance Scripts for Production Environment
# shopnirvanaorganics.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_PATH="/var/www/nirvana-backend"
DOMAIN="shopnirvanaorganics.com"
DB_NAME="nirvana_organics_production"
DB_USER="nirvana_prod_user"

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# System status check with enhanced monitoring
check_system_status() {
    log_info "Checking production system status..."
    
    echo -e "${BLUE}=== System Information ===${NC}"
    echo "Hostname: $(hostname)"
    echo "Uptime: $(uptime -p)"
    echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
    echo "Memory Usage: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2 " (" int($3/$2*100) "%)"}')"
    echo "Disk Usage: $(df -h / | tail -1 | awk '{print $5 " used of " $2}')"
    echo "CPU Temperature: $(sensors 2>/dev/null | grep 'Core 0' | awk '{print $3}' || echo 'N/A')"
    
    echo -e "\n${BLUE}=== Service Status ===${NC}"
    for service in nginx mysql redis-server fail2ban ufw; do
        if systemctl is-active --quiet $service 2>/dev/null; then
            echo -e "$service: ${GREEN}Running${NC}"
        else
            echo -e "$service: ${RED}Stopped${NC}"
        fi
    done
    
    echo -e "\n${BLUE}=== PM2 Processes ===${NC}"
    sudo -u nirvana pm2 list 2>/dev/null || echo "PM2 not running or no processes"
    
    echo -e "\n${BLUE}=== Application Health ===${NC}"
    local api_response=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/api/health)
    if [ "$api_response" = "200" ]; then
        echo -e "API Health: ${GREEN}OK (HTTP $api_response)${NC}"
    else
        echo -e "API Health: ${RED}Failed (HTTP $api_response)${NC}"
    fi
    
    local frontend_response=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN)
    if [ "$frontend_response" = "200" ]; then
        echo -e "Frontend: ${GREEN}OK (HTTP $frontend_response)${NC}"
    else
        echo -e "Frontend: ${RED}Failed (HTTP $frontend_response)${NC}"
    fi
    
    echo -e "\n${BLUE}=== Database Status ===${NC}"
    if mysql -u $DB_USER -p$DB_PASSWORD -e "SELECT 1;" $DB_NAME >/dev/null 2>&1; then
        echo -e "Database Connection: ${GREEN}OK${NC}"
        local db_size=$(mysql -u $DB_USER -p$DB_PASSWORD -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='$DB_NAME';" | tail -1)
        echo "Database Size: ${db_size}MB"
    else
        echo -e "Database Connection: ${RED}Failed${NC}"
    fi
}

# Zero-downtime application update
update_application() {
    log_info "Performing zero-downtime production update..."
    
    cd $PROJECT_PATH
    
    # Create backup before update
    log_info "Creating pre-update backup..."
    /usr/local/bin/nirvana-backup quick
    
    # Stash any local changes
    sudo -u nirvana git stash push -m "Pre-update backup $(date)"
    
    # Pull latest changes
    log_info "Pulling latest changes from main branch..."
    sudo -u nirvana git pull origin main
    
    # Install dependencies
    log_info "Installing dependencies..."
    sudo -u nirvana npm ci --production --no-audit
    
    # Build frontend
    log_info "Building frontend..."
    sudo -u nirvana npm run build:prod
    
    # Run migrations
    log_info "Running database migrations..."
    sudo -u nirvana npm run migrate:prod
    
    # Graceful reload of PM2 processes (zero downtime)
    log_info "Performing graceful reload..."
    sudo -u nirvana pm2 reload all --update-env
    
    # Wait for services to stabilize
    sleep 15
    
    # Verify update
    local health_check=0
    for i in {1..5}; do
        if curl -f -s https://$DOMAIN/api/health >/dev/null; then
            health_check=1
            break
        fi
        sleep 5
    done
    
    if [ $health_check -eq 1 ]; then
        log_info "✅ Production update completed successfully"
        
        # Send success notification
        echo "Production update completed successfully at $(date)" | logger -t nirvana-update
    else
        log_error "❌ Production update failed - rolling back..."
        
        # Rollback
        sudo -u nirvana git reset --hard HEAD~1
        sudo -u nirvana npm ci --production
        sudo -u nirvana npm run build:prod
        sudo -u nirvana pm2 reload all
        
        return 1
    fi
}

# Enhanced system cleanup for production
cleanup_system() {
    log_info "Performing production system cleanup..."
    
    # Clean application logs older than 30 days
    find $PROJECT_PATH/logs -name "*.log" -mtime +30 -delete 2>/dev/null || true
    
    # Compress old logs
    find $PROJECT_PATH/logs -name "*.log" -mtime +7 -exec gzip {} \; 2>/dev/null || true
    
    # Clean PM2 logs
    sudo -u nirvana pm2 flush
    
    # Clean npm cache
    sudo -u nirvana npm cache clean --force
    
    # Clean system logs (keep 30 days)
    sudo journalctl --vacuum-time=30d
    
    # Clean package cache
    sudo apt autoremove -y
    sudo apt autoclean
    
    # Clean temporary files older than 7 days
    sudo find /tmp -type f -atime +7 -delete 2>/dev/null || true
    
    # Clean old backup files (keep 30 days as per retention policy)
    find $PROJECT_PATH/backups -name "*.sql.gz" -mtime +30 -delete 2>/dev/null || true
    find $PROJECT_PATH/backups -name "*.tar.gz" -mtime +30 -delete 2>/dev/null || true
    
    # Clean Nginx logs older than 90 days
    find /var/log/nginx -name "*.log.*" -mtime +90 -delete 2>/dev/null || true
    
    log_info "Production system cleanup completed"
}

# Production-grade database maintenance
maintain_database() {
    log_info "Performing production database maintenance..."
    
    # Create maintenance backup
    log_info "Creating maintenance backup..."
    /usr/local/bin/nirvana-backup database
    
    # Check for corrupted tables
    log_info "Checking for corrupted tables..."
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "CHECK TABLE $(mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e 'SHOW TABLES' | tail -n +2 | tr '\n' ',' | sed 's/,$//');"
    
    # Optimize tables during low-traffic hours
    log_info "Optimizing database tables..."
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "CALL OptimizeAllTables();"
    
    # Analyze tables for query optimization
    log_info "Analyzing tables..."
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "CALL AnalyzeAllTables();"
    
    # Update table statistics
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "FLUSH TABLES;"
    
    # Generate database statistics report
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "CALL GetDatabaseStats();" > $PROJECT_PATH/logs/db_stats_$(date +%Y%m%d).txt
    
    log_info "Production database maintenance completed"
}

# Enhanced SSL certificate management
check_ssl() {
    log_info "Checking SSL certificates for production..."
    
    local cert_file="/etc/ssl/certs/$DOMAIN.crt"
    
    if [ -f "$cert_file" ]; then
        local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( ($expiry_timestamp - $current_timestamp) / 86400 ))
        
        echo "SSL Certificate expires in $days_until_expiry days"
        
        # Check certificate chain
        if openssl verify -CAfile "$cert_file" "$cert_file" >/dev/null 2>&1; then
            echo -e "Certificate chain: ${GREEN}Valid${NC}"
        else
            echo -e "Certificate chain: ${YELLOW}Warning${NC}"
        fi
        
        # Check certificate strength
        local key_size=$(openssl x509 -in "$cert_file" -noout -text | grep "Public-Key:" | awk '{print $2}' | tr -d '()')
        echo "Key size: $key_size"
        
        if [ $days_until_expiry -lt 30 ]; then
            log_warning "SSL certificate expires soon, attempting renewal..."
            
            # Test renewal first
            if sudo certbot renew --dry-run; then
                # Perform actual renewal
                sudo certbot renew --quiet
                
                if [ $? -eq 0 ]; then
                    log_info "SSL certificate renewed successfully"
                    
                    # Copy renewed certificates
                    sudo cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "/etc/ssl/certs/$DOMAIN.crt"
                    sudo cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "/etc/ssl/private/$DOMAIN.key"
                    
                    # Test Nginx configuration
                    if sudo nginx -t; then
                        sudo systemctl reload nginx
                        log_info "Nginx reloaded with new certificates"
                    else
                        log_error "Nginx configuration test failed"
                    fi
                else
                    log_error "SSL certificate renewal failed"
                fi
            else
                log_error "SSL certificate renewal dry-run failed"
            fi
        else
            log_info "SSL certificate is valid"
        fi
    else
        log_error "SSL certificate file not found"
    fi
}

# Comprehensive performance monitoring
monitor_performance() {
    log_info "Monitoring production system performance..."
    
    echo -e "${BLUE}=== CPU Usage ===${NC}"
    echo "Current: $(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}')"
    echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
    
    echo -e "\n${BLUE}=== Memory Usage ===${NC}"
    free -h
    echo "Memory pressure: $(cat /proc/pressure/memory 2>/dev/null | head -1 || echo 'N/A')"
    
    echo -e "\n${BLUE}=== Disk I/O ===${NC}"
    iostat -x 1 1 2>/dev/null | tail -n +4 || echo "iostat not available"
    
    echo -e "\n${BLUE}=== Network Statistics ===${NC}"
    ss -tuln | grep -E ':(80|443|3306|5000|5001)'
    echo "Active connections: $(ss -t | grep ESTAB | wc -l)"
    
    echo -e "\n${BLUE}=== Database Performance ===${NC}"
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "SHOW GLOBAL STATUS LIKE 'Threads_connected';"
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "SHOW GLOBAL STATUS LIKE 'Queries';"
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "SHOW GLOBAL STATUS LIKE 'Slow_queries';"
    
    echo -e "\n${BLUE}=== Application Metrics ===${NC}"
    sudo -u nirvana pm2 show nirvana-main 2>/dev/null | grep -E "(uptime|restarts|memory|cpu)" || echo "PM2 metrics not available"
    
    echo -e "\n${BLUE}=== Top Resource Consumers ===${NC}"
    ps aux --sort=-%cpu | head -10
}

# Enhanced security audit for production
security_audit() {
    log_info "Performing production security audit..."
    
    echo -e "${BLUE}=== Authentication Logs ===${NC}"
    echo "Failed login attempts (last 24h):"
    sudo grep "Failed password" /var/log/auth.log | grep "$(date '+%b %d')" | wc -l
    
    echo -e "\n${BLUE}=== Firewall Status ===${NC}"
    sudo ufw status numbered
    
    echo -e "\n${BLUE}=== Fail2ban Status ===${NC}"
    sudo fail2ban-client status
    for jail in $(sudo fail2ban-client status | grep "Jail list:" | cut -d: -f2 | tr -d ' ' | tr ',' '\n'); do
        echo "Jail $jail: $(sudo fail2ban-client status $jail | grep "Currently banned:" | cut -d: -f2)"
    done
    
    echo -e "\n${BLUE}=== Open Ports ===${NC}"
    sudo netstat -tuln | grep LISTEN
    
    echo -e "\n${BLUE}=== SSL Security ===${NC}"
    echo "SSL Labs rating: Run 'curl -s https://api.ssllabs.com/api/v3/analyze?host=$DOMAIN' for detailed analysis"
    
    echo -e "\n${BLUE}=== System Updates ===${NC}"
    local updates=$(apt list --upgradable 2>/dev/null | wc -l)
    echo "$updates packages can be upgraded"
    
    if [ $updates -gt 0 ]; then
        echo "Security updates available:"
        apt list --upgradable 2>/dev/null | grep -i security | head -5
    fi
    
    echo -e "\n${BLUE}=== File Permissions Audit ===${NC}"
    find $PROJECT_PATH -name ".env*" -exec ls -la {} \;
    find /etc/ssl/private -name "*.key" -exec ls -la {} \; 2>/dev/null || echo "SSL keys not accessible (good)"
}

# Generate comprehensive production report
generate_report() {
    local report_file="$PROJECT_PATH/logs/production_maintenance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "Generating comprehensive production maintenance report..."
    
    {
        echo "Nirvana Organics Production Environment Maintenance Report"
        echo "========================================================"
        echo "Generated: $(date)"
        echo "Server: $(hostname)"
        echo "Report ID: $(uuidgen 2>/dev/null || echo "$(date +%s)")"
        echo ""
        
        echo "EXECUTIVE SUMMARY:"
        echo "=================="
        local uptime_days=$(uptime -p | grep -o '[0-9]* day' | awk '{print $1}' || echo "0")
        local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
        local memory_usage=$(free | grep '^Mem:' | awk '{printf("%.1f", $3/$2*100)}')
        local disk_usage=$(df / | tail -1 | awk '{print $5}' | tr -d '%')
        
        echo "- System uptime: $uptime_days days"
        echo "- Load average: $load_avg"
        echo "- Memory usage: ${memory_usage}%"
        echo "- Disk usage: ${disk_usage}%"
        echo ""
        
        echo "DETAILED SYSTEM STATUS:"
        echo "======================"
        check_system_status
        echo ""
        
        echo "PERFORMANCE METRICS:"
        echo "==================="
        monitor_performance
        echo ""
        
        echo "SECURITY AUDIT:"
        echo "==============="
        security_audit
        echo ""
        
        echo "DISK USAGE BREAKDOWN:"
        echo "===================="
        df -h
        echo ""
        du -sh $PROJECT_PATH/* 2>/dev/null | sort -hr
        echo ""
        
        echo "LOG FILE ANALYSIS:"
        echo "=================="
        echo "Application logs:"
        find $PROJECT_PATH/logs -name "*.log" -exec ls -lh {} \; 2>/dev/null | head -10
        echo ""
        echo "System logs size:"
        du -sh /var/log 2>/dev/null || echo "Cannot access system logs"
        echo ""
        
        echo "BACKUP STATUS:"
        echo "=============="
        echo "Recent backups:"
        find $PROJECT_PATH/backups -name "*.sql.gz" -o -name "*.tar.gz" | head -10 | xargs ls -lh 2>/dev/null || echo "No recent backups found"
        echo ""
        
        echo "RECOMMENDATIONS:"
        echo "================"
        if [ $disk_usage -gt 80 ]; then
            echo "- WARNING: Disk usage is high (${disk_usage}%). Consider cleanup or expansion."
        fi
        if [ $(echo "$memory_usage > 85" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
            echo "- WARNING: Memory usage is high (${memory_usage}%). Monitor for memory leaks."
        fi
        if [ $updates -gt 0 ]; then
            echo "- INFO: $updates system updates available. Schedule maintenance window."
        fi
        echo "- Regular maintenance completed successfully."
        
    } > "$report_file"
    
    chown nirvana:www-data "$report_file"
    chmod 644 "$report_file"
    
    log_info "Production maintenance report generated: $report_file"
    
    # Send report summary to syslog
    logger -t nirvana-maintenance "Production maintenance report generated: $report_file"
}

# Emergency recovery procedures
emergency_recovery() {
    log_error "Initiating emergency recovery procedures..."
    
    # Stop all services
    sudo -u nirvana pm2 stop all
    
    # Restart system services
    sudo systemctl restart nginx mysql redis-server
    
    # Wait for services to stabilize
    sleep 30
    
    # Start application with fresh processes
    sudo -u nirvana pm2 delete all
    sudo -u nirvana pm2 start ecosystem.config.js --env production
    
    # Wait for application to start
    sleep 60
    
    # Verify recovery
    if curl -f -s https://$DOMAIN/api/health >/dev/null; then
        log_info "✅ Emergency recovery successful"
        logger -t nirvana-emergency "Emergency recovery completed successfully"
    else
        log_error "❌ Emergency recovery failed"
        logger -t nirvana-emergency "Emergency recovery failed - manual intervention required"
        return 1
    fi
}

# Main script logic
case "${1:-status}" in
    "status")
        check_system_status
        ;;
    "update")
        update_application
        ;;
    "cleanup")
        cleanup_system
        ;;
    "restart")
        restart_services
        ;;
    "database")
        maintain_database
        ;;
    "ssl")
        check_ssl
        ;;
    "monitor")
        monitor_performance
        ;;
    "security")
        security_audit
        ;;
    "report")
        generate_report
        ;;
    "emergency")
        emergency_recovery
        ;;
    "full")
        log_info "Running full production maintenance cycle..."
        check_system_status
        cleanup_system
        maintain_database
        check_ssl
        monitor_performance
        security_audit
        generate_report
        log_info "Full production maintenance cycle completed"
        ;;
    *)
        echo "Usage: $0 {status|update|cleanup|restart|database|ssl|monitor|security|report|emergency|full}"
        echo ""
        echo "Commands:"
        echo "  status    - Check system and application status"
        echo "  update    - Zero-downtime application update"
        echo "  cleanup   - Clean up logs and temporary files"
        echo "  restart   - Restart all services"
        echo "  database  - Perform database maintenance"
        echo "  ssl       - Check and renew SSL certificates"
        echo "  monitor   - Show performance monitoring"
        echo "  security  - Perform security audit"
        echo "  report    - Generate comprehensive maintenance report"
        echo "  emergency - Emergency recovery procedures"
        echo "  full      - Run complete maintenance cycle"
        exit 1
        ;;
esac
