/**
 * Migration: Add Social Authentication Fields
 * Adds Google ID, Facebook ID, and profile picture fields to users table
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add social authentication columns to Users table
      await queryInterface.addColumn('Users', 'google_id', {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true
      });

      await queryInterface.addColumn('Users', 'facebook_id', {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true
      });

      await queryInterface.addColumn('Users', 'profile_picture', {
        type: Sequelize.TEXT,
        allowNull: true
      });

      // Make password field nullable for social authentication users
      await queryInterface.changeColumn('Users', 'password', {
        type: Sequelize.STRING,
        allowNull: true,
        validate: {
          len: [8, 255]
        }
      });

      console.log('✅ Social authentication fields added successfully');
    } catch (error) {
      console.error('❌ Error adding social authentication fields:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Remove social authentication columns
      await queryInterface.removeColumn('Users', 'google_id');
      await queryInterface.removeColumn('Users', 'facebook_id');
      await queryInterface.removeColumn('Users', 'profile_picture');

      // Make password field required again
      await queryInterface.changeColumn('Users', 'password', {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          len: [8, 255]
        }
      });

      console.log('✅ Social authentication fields removed successfully');
    } catch (error) {
      console.error('❌ Error removing social authentication fields:', error);
      throw error;
    }
  }
};
