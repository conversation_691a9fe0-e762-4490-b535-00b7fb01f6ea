const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * SSL Configuration Module
 * Handles SSL certificate loading and HTTPS server configuration
 */

class SSLConfig {
  constructor() {
    this.sslEnabled = process.env.SSL_CERT_PATH && process.env.SSL_KEY_PATH;
    this.certPath = process.env.SSL_CERT_PATH;
    this.keyPath = process.env.SSL_KEY_PATH;
    this.caPath = process.env.SSL_CA_PATH; // Optional CA bundle
    this.sslOptions = null;
    
    if (this.sslEnabled) {
      this.loadCertificates();
    }
  }

  /**
   * Load SSL certificates from file system
   */
  loadCertificates() {
    try {
      console.log('🔐 Loading SSL certificates...');
      
      // Check if certificate files exist
      if (!fs.existsSync(this.certPath)) {
        throw new Error(`SSL certificate file not found: ${this.certPath}`);
      }
      
      if (!fs.existsSync(this.keyPath)) {
        throw new Error(`SSL private key file not found: ${this.keyPath}`);
      }

      // Read certificate files
      const cert = fs.readFileSync(this.certPath, 'utf8');
      const key = fs.readFileSync(this.keyPath, 'utf8');
      
      this.sslOptions = {
        cert: cert,
        key: key
      };

      // Load CA bundle if provided
      if (this.caPath && fs.existsSync(this.caPath)) {
        const ca = fs.readFileSync(this.caPath, 'utf8');
        this.sslOptions.ca = ca;
        console.log('📋 SSL CA bundle loaded');
      }

      // Additional SSL security options
      this.sslOptions = {
        ...this.sslOptions,
        // Disable weak SSL/TLS versions
        secureProtocol: 'TLSv1_2_method',
        // Prefer server cipher order
        honorCipherOrder: true,
        // Disable session resumption for security
        sessionIdContext: 'nirvana-organics'
      };

      console.log('✅ SSL certificates loaded successfully');
      
      // Validate certificate expiration
      this.validateCertificate();
      
    } catch (error) {
      console.error('❌ Failed to load SSL certificates:', error.message);
      this.sslEnabled = false;
      this.sslOptions = null;
    }
  }

  /**
   * Validate SSL certificate expiration
   */
  validateCertificate() {
    try {
      const cert = this.sslOptions.cert;
      const certLines = cert.split('\n');
      const certStart = certLines.findIndex(line => line.includes('-----BEGIN CERTIFICATE-----'));
      const certEnd = certLines.findIndex(line => line.includes('-----END CERTIFICATE-----'));
      
      if (certStart !== -1 && certEnd !== -1) {
        const certData = certLines.slice(certStart + 1, certEnd).join('');
        const certBuffer = Buffer.from(certData, 'base64');
        
        // Parse certificate (basic validation)
        // Note: For production, consider using a proper certificate parsing library
        console.log('📅 SSL certificate validation completed');
      }
    } catch (error) {
      console.warn('⚠️ Could not validate SSL certificate expiration:', error.message);
    }
  }

  /**
   * Create HTTPS server with SSL options
   */
  createHTTPSServer(app) {
    if (!this.isEnabled()) {
      throw new Error('SSL is not enabled or certificates are not loaded');
    }

    try {
      const server = https.createServer(this.sslOptions, app);
      console.log('🔒 HTTPS server created with SSL certificates');
      return server;
    } catch (error) {
      console.error('❌ Failed to create HTTPS server:', error);
      throw error;
    }
  }

  /**
   * Check if SSL is enabled and configured
   */
  isEnabled() {
    return this.sslEnabled && this.sslOptions !== null;
  }

  /**
   * Get SSL options for server creation
   */
  getSSLOptions() {
    return this.sslOptions;
  }

  /**
   * Reload certificates (useful for certificate renewal)
   */
  reloadCertificates() {
    console.log('🔄 Reloading SSL certificates...');
    this.loadCertificates();
  }

  /**
   * Get SSL configuration status
   */
  getStatus() {
    return {
      enabled: this.sslEnabled,
      certPath: this.certPath,
      keyPath: this.keyPath,
      caPath: this.caPath,
      loaded: this.sslOptions !== null,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Middleware to redirect HTTP to HTTPS
   */
  redirectToHTTPS() {
    return (req, res, next) => {
      // Skip redirect in development
      if (process.env.NODE_ENV === 'development') {
        return next();
      }

      // Check if request is already HTTPS
      if (req.secure || req.get('X-Forwarded-Proto') === 'https') {
        return next();
      }

      // Redirect to HTTPS
      const httpsUrl = `https://${req.get('Host')}${req.url}`;
      res.redirect(301, httpsUrl);
    };
  }

  /**
   * Security headers for HTTPS
   */
  securityHeaders() {
    return (req, res, next) => {
      if (this.isEnabled()) {
        // Strict Transport Security
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        
        // Upgrade insecure requests
        res.setHeader('Content-Security-Policy', 'upgrade-insecure-requests');
      }
      
      next();
    };
  }

  /**
   * Watch certificate files for changes (for auto-renewal)
   */
  watchCertificates() {
    if (!this.isEnabled()) {
      return;
    }

    try {
      fs.watchFile(this.certPath, (curr, prev) => {
        console.log('📋 SSL certificate file changed, reloading...');
        this.reloadCertificates();
      });

      fs.watchFile(this.keyPath, (curr, prev) => {
        console.log('🔑 SSL private key file changed, reloading...');
        this.reloadCertificates();
      });

      console.log('👀 Watching SSL certificate files for changes');
    } catch (error) {
      console.warn('⚠️ Could not watch SSL certificate files:', error.message);
    }
  }
}

// Create singleton instance
const sslConfig = new SSLConfig();

// Auto-reload certificates on file changes (useful for Let's Encrypt renewals)
if (sslConfig.isEnabled()) {
  sslConfig.watchCertificates();
}

module.exports = sslConfig;
