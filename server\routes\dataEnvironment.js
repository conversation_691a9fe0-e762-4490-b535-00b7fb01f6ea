const express = require('express');
const router = express.Router();
const { body, param } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const {
  dataEnvironmentLimiter,
  validateDataEnvironmentSession,
  auditDataEnvironmentOperation,
  adminIPWhitelist
} = require('../config/security');
const dataEnvironmentController = require('../controllers/dataEnvironmentController');

// Validation middleware
const validateDataMode = [
  body('mode')
    .isIn(['mock', 'real'])
    .withMessage('Mode must be either "mock" or "real"')
];

const validateMockData = [
  body('entityType')
    .notEmpty()
    .withMessage('Entity type is required')
    .isIn(['product', 'order', 'user', 'category'])
    .withMessage('Invalid entity type'),
  body('data')
    .isObject()
    .withMessage('Data must be an object'),
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('metadata')
    .optional()
    .isObject()
    .withMessage('Metadata must be an object')
];

const validateMockId = [
  param('mockId')
    .notEmpty()
    .withMessage('Mock ID is required')
];

const validateEntityType = [
  param('entityType')
    .notEmpty()
    .withMessage('Entity type is required')
    .isIn(['product', 'order', 'user', 'category'])
    .withMessage('Invalid entity type')
];

/**
 * @route GET /api/admin/data-environment/mode
 * @desc Get current data environment mode
 * @access Private (Admin only)
 */
router.get('/mode',
  dataEnvironmentLimiter,
  authenticate,
  requireAdmin,
  adminIPWhitelist,
  validateDataEnvironmentSession,
  auditDataEnvironmentOperation('GET_DATA_MODE'),
  dataEnvironmentController.getCurrentMode
);

/**
 * @route POST /api/admin/data-environment/mode
 * @desc Set data environment mode
 * @access Private (Admin only)
 */
router.post('/mode',
  dataEnvironmentLimiter,
  authenticate,
  requireAdmin,
  adminIPWhitelist,
  validateDataEnvironmentSession,
  validateDataMode,
  auditDataEnvironmentOperation('SET_DATA_MODE'),
  dataEnvironmentController.setDataMode
);

/**
 * @route POST /api/admin/data-environment/toggle
 * @desc Toggle data environment mode
 * @access Private (Admin only)
 */
router.post('/toggle',
  dataEnvironmentLimiter,
  authenticate,
  requireAdmin,
  adminIPWhitelist,
  validateDataEnvironmentSession,
  auditDataEnvironmentOperation('TOGGLE_DATA_MODE'),
  dataEnvironmentController.toggleDataMode
);

/**
 * @route GET /api/admin/data-environment/data/:entityType
 * @desc Get data based on current mode
 * @access Private (Admin only)
 */
router.get('/data/:entityType', 
  authenticate, 
  requireAdmin, 
  validateEntityType,
  dataEnvironmentController.getData
);

/**
 * @route POST /api/admin/data-environment/mock
 * @desc Create mock data entry
 * @access Private (Admin only)
 */
router.post('/mock', 
  authenticate, 
  requireAdmin, 
  validateMockData,
  dataEnvironmentController.createMockData
);

/**
 * @route PUT /api/admin/data-environment/mock/:mockId
 * @desc Update mock data entry
 * @access Private (Admin only)
 */
router.put('/mock/:mockId', 
  authenticate, 
  requireAdmin, 
  validateMockId,
  [
    body('data')
      .optional()
      .isObject()
      .withMessage('Data must be an object'),
    body('description')
      .optional()
      .isString()
      .withMessage('Description must be a string'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object')
  ],
  dataEnvironmentController.updateMockData
);

/**
 * @route DELETE /api/admin/data-environment/mock/:mockId
 * @desc Delete mock data entry
 * @access Private (Admin only)
 */
router.delete('/mock/:mockId', 
  authenticate, 
  requireAdmin, 
  validateMockId,
  dataEnvironmentController.deleteMockData
);

/**
 * @route DELETE /api/admin/data-environment/clear/:entityType
 * @desc Clear mock data for entity type
 * @access Private (Admin only)
 */
router.delete('/clear/:entityType', 
  authenticate, 
  requireAdmin, 
  validateEntityType,
  dataEnvironmentController.clearMockData
);

/**
 * @route DELETE /api/admin/data-environment/clear-all
 * @desc Clear all mock data
 * @access Private (Admin only)
 */
router.delete('/clear-all', 
  authenticate, 
  requireAdmin, 
  dataEnvironmentController.clearMockData
);

/**
 * @route GET /api/admin/data-environment/stats
 * @desc Get mock data statistics
 * @access Private (Admin only)
 */
router.get('/stats', 
  authenticate, 
  requireAdmin, 
  dataEnvironmentController.getMockDataStats
);

/**
 * @route POST /api/admin/data-environment/generate-sample
 * @desc Generate sample mock data
 * @access Private (Admin only)
 */
router.post('/generate-sample', 
  authenticate, 
  requireAdmin, 
  dataEnvironmentController.generateSampleData
);

module.exports = router;
