import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAppSelector } from '../hooks/redux';

interface DataEnvironmentState {
  mode: 'mock' | 'real';
  isMockMode: boolean;
  isRealMode: boolean;
  sessionId: string | null;
  isLoading: boolean;
  error: string | null;
}

interface DataEnvironmentContextType extends DataEnvironmentState {
  setDataMode: (mode: 'mock' | 'real') => Promise<void>;
  toggleDataMode: () => Promise<void>;
  refreshMode: () => Promise<void>;
  clearError: () => void;
}

const DataEnvironmentContext = createContext<DataEnvironmentContextType | undefined>(undefined);

interface DataEnvironmentProviderProps {
  children: ReactNode;
}

export const DataEnvironmentProvider: React.FC<DataEnvironmentProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);
  const [state, setState] = useState<DataEnvironmentState>({
    mode: 'real',
    isMockMode: false,
    isRealMode: true,
    sessionId: null,
    isLoading: false,
    error: null
  });

  // Generate or get session ID
  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('data-environment-session-id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('data-environment-session-id', sessionId);
    }
    return sessionId;
  };

  // API call helper
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const token = localStorage.getItem('token');
    const sessionId = getSessionId();
    
    const response = await fetch(`${import.meta.env.VITE_API_URL || '/api'}/admin/data-environment${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'X-Session-Id': sessionId,
        ...options.headers
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(errorData.message || 'API request failed');
    }

    return response.json();
  };

  // Get current data mode
  const getCurrentMode = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const response = await apiCall('/mode');
      
      const mode = response.data.mode;
      setState(prev => ({
        ...prev,
        mode,
        isMockMode: mode === 'mock',
        isRealMode: mode === 'real',
        sessionId: response.data.sessionId,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to get data mode',
        isLoading: false
      }));
    }
  };

  // Set data mode
  const setDataMode = async (mode: 'mock' | 'real') => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      await apiCall('/mode', {
        method: 'POST',
        body: JSON.stringify({ mode })
      });

      setState(prev => ({
        ...prev,
        mode,
        isMockMode: mode === 'mock',
        isRealMode: mode === 'real',
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to set data mode',
        isLoading: false
      }));
      throw error;
    }
  };

  // Toggle data mode
  const toggleDataMode = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const response = await apiCall('/toggle', {
        method: 'POST'
      });

      const mode = response.data.mode;
      setState(prev => ({
        ...prev,
        mode,
        isMockMode: mode === 'mock',
        isRealMode: mode === 'real',
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to toggle data mode',
        isLoading: false
      }));
      throw error;
    }
  };

  // Refresh current mode
  const refreshMode = async () => {
    await getCurrentMode();
  };

  // Clear error
  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  // Initialize data mode when user is authenticated as admin
  useEffect(() => {
    if (isAuthenticated && user && (user.role === 'admin' || user.role === 'manager')) {
      getCurrentMode();
    } else {
      // Reset to default state for non-admin users
      setState({
        mode: 'real',
        isMockMode: false,
        isRealMode: true,
        sessionId: null,
        isLoading: false,
        error: null
      });
    }
  }, [isAuthenticated, user]);

  const contextValue: DataEnvironmentContextType = {
    ...state,
    setDataMode,
    toggleDataMode,
    refreshMode,
    clearError
  };

  return (
    <DataEnvironmentContext.Provider value={contextValue}>
      {children}
    </DataEnvironmentContext.Provider>
  );
};

export const useDataEnvironment = (): DataEnvironmentContextType => {
  const context = useContext(DataEnvironmentContext);
  if (context === undefined) {
    throw new Error('useDataEnvironment must be used within a DataEnvironmentProvider');
  }
  return context;
};

export default DataEnvironmentContext;
