# Nirvana Organics E-commerce - Troubleshooting Guide

This guide provides solutions to common issues encountered during deployment and operation of the Nirvana Organics E-commerce platform.

## 🚨 Emergency Quick Fixes

### Application Down
```bash
# Quick restart
sudo pm2 restart all

# Check status
sudo pm2 status

# View recent logs
sudo pm2 logs --lines 50
```

### Database Connection Lost
```bash
# Restart MySQL
sudo systemctl restart mysql

# Test connection
cd /var/www/nirvana-backend
node scripts/test-database-connection.js
```

### Nginx Issues
```bash
# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx

# Check status
sudo systemctl status nginx
```

## 🔍 Diagnostic Commands

### System Health Check
```bash
# Run comprehensive status check
cd /var/www/nirvana-backend
node scripts/system-status.js

# Check disk space
df -h

# Check memory usage
free -h

# Check CPU usage
top
```

### Application Diagnostics
```bash
# PM2 monitoring
sudo pm2 monit

# Application logs
sudo pm2 logs nirvana-production --lines 100

# Error logs only
sudo pm2 logs nirvana-production --err --lines 50

# Real-time log monitoring
sudo pm2 logs nirvana-production --follow
```

### Network Diagnostics
```bash
# Check port availability
sudo netstat -tlnp | grep :5000
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# Test API endpoint
curl -I https://shopnirvanaorganics.com/api/health

# Test admin panel
curl -I https://shopnirvanaorganics.com/admin/
```

## 🐛 Common Issues and Solutions

### 1. Application Won't Start

#### Symptoms
- PM2 shows app as "errored" or "stopped"
- Cannot access website
- 502 Bad Gateway errors

#### Diagnosis
```bash
# Check PM2 status
sudo pm2 status

# View error logs
sudo pm2 logs nirvana-production --err --lines 20

# Check environment file
cat /var/www/nirvana-backend/.env
```

#### Solutions

**Missing Environment File**
```bash
# Copy template
cd /var/www/nirvana-backend
cp config/.env.production.template .env

# Edit with your values
sudo nano .env

# Restart application
sudo pm2 restart nirvana-production
```

**Database Connection Error**
```bash
# Test database connection
node scripts/test-database-connection.js

# Check MySQL status
sudo systemctl status mysql

# Restart MySQL if needed
sudo systemctl restart mysql
```

**Port Already in Use**
```bash
# Find process using port 5000
sudo lsof -i :5000

# Kill conflicting process
sudo kill -9 <PID>

# Restart application
sudo pm2 restart nirvana-production
```

### 2. Admin Panel Not Accessible

#### Symptoms
- 404 errors when accessing `/admin`
- Admin panel loads but shows blank page
- Assets not loading for admin panel

#### Diagnosis
```bash
# Check if admin build exists
ls -la /var/www/nirvana-backend/dist/admin/

# Check nginx configuration
sudo nginx -t

# Check admin-specific logs
sudo tail -f /var/log/nginx/nirvana-production-access.log | grep admin
```

#### Solutions

**Missing Admin Build**
```bash
# Verify admin build exists
ls -la /var/www/nirvana-backend/dist/admin/admin.html

# If missing, rebuild (from development environment)
npm run build:unified

# Copy admin build to server
scp -r dist/admin/ user@server:/var/www/nirvana-backend/dist/
```

**Nginx Configuration Issue**
```bash
# Check nginx config for admin routing
sudo grep -n "location /admin" /etc/nginx/sites-available/nirvana-*

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

**Base Href Issue**
```bash
# Check admin.html for correct base href
grep "base href" /var/www/nirvana-backend/dist/admin/admin.html

# Should show: <base href="/admin/">
```

### 3. Database Issues

#### Symptoms
- "Connection refused" errors
- "Access denied" errors
- "Database does not exist" errors

#### Diagnosis
```bash
# Test database connection
cd /var/www/nirvana-backend
node scripts/test-database-connection.js

# Check MySQL status
sudo systemctl status mysql

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log
```

#### Solutions

**MySQL Not Running**
```bash
# Start MySQL
sudo systemctl start mysql

# Enable auto-start
sudo systemctl enable mysql

# Check status
sudo systemctl status mysql
```

**Database Doesn't Exist**
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE nirvana_organics_production;"

# Run setup script
cd /var/www/nirvana-backend
node scripts/setup-database.js
```

**User Access Issues**
```bash
# Create database user
mysql -u root -p
CREATE USER 'nirvana_prod_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON nirvana_organics_production.* TO 'nirvana_prod_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

**Table Missing**
```bash
# Run table creation
cd /var/www/nirvana-backend
node scripts/create-database-tables.js

# Run migrations
node scripts/run-migrations.js
```

### 4. SSL Certificate Issues

#### Symptoms
- "Not secure" warnings in browser
- SSL certificate expired errors
- Mixed content warnings

#### Diagnosis
```bash
# Check certificate status
sudo certbot certificates

# Test SSL configuration
openssl s_client -connect shopnirvanaorganics.com:443

# Check nginx SSL config
sudo grep -n "ssl_certificate" /etc/nginx/sites-available/nirvana-*
```

#### Solutions

**Certificate Expired**
```bash
# Renew certificates
sudo certbot renew

# Restart nginx
sudo systemctl restart nginx
```

**Certificate Not Found**
```bash
# Install new certificate
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com

# Verify installation
sudo certbot certificates
```

**Mixed Content Issues**
```bash
# Check for HTTP resources in HTTPS pages
# Update environment file
sudo nano /var/www/nirvana-backend/.env

# Ensure APP_URL uses https://
APP_URL=https://shopnirvanaorganics.com
```

### 5. Payment Integration Issues

#### Symptoms
- Payment processing fails
- Square API errors
- Webhook failures

#### Diagnosis
```bash
# Check Square configuration in environment
grep SQUARE /var/www/nirvana-backend/.env

# Check payment logs
sudo pm2 logs nirvana-production | grep -i square

# Test Square connection
cd /var/www/nirvana-backend
node scripts/test-square-connection.js
```

#### Solutions

**Invalid Square Credentials**
```bash
# Update environment file
sudo nano /var/www/nirvana-backend/.env

# Verify credentials in Square dashboard
# Restart application
sudo pm2 restart nirvana-production
```

**Sandbox vs Production Environment**
```bash
# For testing environment
SQUARE_ENVIRONMENT=sandbox

# For production environment
SQUARE_ENVIRONMENT=production
```

### 6. Email Service Issues

#### Symptoms
- Emails not being sent
- SMTP authentication errors
- Email delivery failures

#### Diagnosis
```bash
# Test email configuration
cd /var/www/nirvana-backend
node scripts/test-email-service.js

# Check email logs
sudo pm2 logs nirvana-production | grep -i email
```

#### Solutions

**Gmail SMTP Issues**
```bash
# Use app-specific password
# Update environment file
sudo nano /var/www/nirvana-backend/.env

EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
```

**Email Template Issues**
```bash
# Check email templates
ls -la /var/www/nirvana-backend/server/templates/

# Verify template syntax
node -c /var/www/nirvana-backend/server/templates/welcome.js
```

### 7. Performance Issues

#### Symptoms
- Slow page loading
- High CPU usage
- Memory leaks
- Database timeouts

#### Diagnosis
```bash
# Monitor system resources
htop

# Check PM2 monitoring
sudo pm2 monit

# Check database performance
mysql -u root -p -e "SHOW PROCESSLIST;"

# Check nginx access logs for slow requests
sudo tail -f /var/log/nginx/nirvana-production-access.log
```

#### Solutions

**High Memory Usage**
```bash
# Restart application
sudo pm2 restart nirvana-production

# Check for memory leaks
sudo pm2 logs nirvana-production | grep -i memory

# Optimize PM2 configuration
sudo nano /var/www/nirvana-backend/ecosystem.config.js
```

**Database Performance**
```bash
# Optimize database
mysql -u root -p -e "OPTIMIZE TABLE products, categories, orders;"

# Check slow query log
sudo tail -f /var/log/mysql/slow.log
```

**Static Asset Caching**
```bash
# Verify nginx caching headers
curl -I https://shopnirvanaorganics.com/assets/main.js

# Should show cache-control headers
```

## 🔧 Maintenance Commands

### Regular Maintenance
```bash
# Weekly system update
sudo apt update && sudo apt upgrade

# Monthly log rotation
sudo logrotate -f /etc/logrotate.conf

# Database backup
cd /var/www/nirvana-backend
node scripts/backup-database.js

# Clear PM2 logs
sudo pm2 flush
```

### Security Maintenance
```bash
# Update Node.js dependencies
cd /var/www/nirvana-backend
npm audit fix

# Check for security updates
sudo unattended-upgrades --dry-run

# Review access logs for suspicious activity
sudo grep -i "404\|403\|500" /var/log/nginx/nirvana-production-access.log
```

## 📊 Monitoring and Alerts

### Log Monitoring
```bash
# Real-time error monitoring
sudo tail -f /var/log/nginx/nirvana-production-error.log

# Application error monitoring
sudo pm2 logs nirvana-production --err --follow

# Database error monitoring
sudo tail -f /var/log/mysql/error.log
```

### Performance Monitoring
```bash
# System resource monitoring
watch -n 5 'free -h && df -h'

# Application performance
sudo pm2 monit

# Database performance
mysql -u root -p -e "SHOW STATUS LIKE 'Threads_connected';"
```

## 🆘 Emergency Procedures

### Complete System Recovery
```bash
# 1. Stop all services
sudo pm2 stop all
sudo systemctl stop nginx
sudo systemctl stop mysql

# 2. Check system integrity
sudo fsck /dev/sda1

# 3. Restart services in order
sudo systemctl start mysql
sudo systemctl start nginx
sudo pm2 start ecosystem.config.js

# 4. Verify functionality
curl https://shopnirvanaorganics.com/api/health
```

### Database Recovery
```bash
# 1. Stop application
sudo pm2 stop nirvana-production

# 2. Backup current database
mysqldump -u root -p nirvana_organics_production > emergency_backup.sql

# 3. Restore from backup
mysql -u root -p nirvana_organics_production < latest_backup.sql

# 4. Restart application
sudo pm2 start nirvana-production
```

## 📞 Getting Help

### Log Collection for Support
```bash
# Collect all relevant logs
mkdir /tmp/nirvana-logs
sudo cp /var/log/nginx/nirvana-* /tmp/nirvana-logs/
sudo pm2 logs nirvana-production --lines 200 > /tmp/nirvana-logs/pm2.log
sudo journalctl -u mysql --lines 100 > /tmp/nirvana-logs/mysql.log

# Create archive
tar -czf nirvana-logs.tar.gz /tmp/nirvana-logs/
```

### System Information
```bash
# Collect system information
uname -a > system-info.txt
cat /etc/os-release >> system-info.txt
node --version >> system-info.txt
npm --version >> system-info.txt
mysql --version >> system-info.txt
nginx -v >> system-info.txt 2>&1
```

---

**Last Updated**: ${new Date().toISOString().split('T')[0]}
**Support**: Nirvana Organics Development Team
