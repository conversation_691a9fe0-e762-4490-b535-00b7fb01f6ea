# Nirvana Organics E-commerce - Environment Template
# Copy to .env and update with actual values
# NEVER commit .env file to repository

# Application Configuration
NODE_ENV=production
PORT=5000
APP_NAME="Nirvana Organics E-commerce"
APP_URL=https://your-domain.com/

# Database Configuration
DB_HOST=your-database-host
DB_PORT=3306
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASSWORD=your-secure-database-password

# Database Pool Configuration (for production optimization)
DB_POOL_MAX=10
DB_POOL_MIN=2
DB_SSL=false

# JWT Configuration (Generate with: openssl rand -base64 32)
JWT_SECRET=your-jwt-secret-key-here-minimum-32-characters
JWT_REFRESH_SECRET=your-refresh-secret-key-here-minimum-32-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-session-secret-minimum-32-characters
BCRYPT_ROUNDS=12

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS="your-app-password"
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME="Your App Name"

# Specialized Email Addresses
EMAIL_ORDERS=<EMAIL>
EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_SUPPORT=<EMAIL>

# Orders Email SMTP Configuration
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS="your-orders-email-password"

# Support Email SMTP Configuration
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS="your-support-email-password"

# SMTP Fallback Configuration (used as fallbacks in emailNotificationService)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS="your-fallback-email-password"

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_OAUTH_CALLBACK_URL=https://your-domain.com/api/auth/google/callback

# VAPID Keys for Push Notifications (Generate with web-push library)
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_EMAIL=mailto:<EMAIL>

# Square Payment Integration
SQUARE_ACCESS_TOKEN=your-square-access-token
SQUARE_APPLICATION_ID=your-square-application-id
SQUARE_ENVIRONMENT=sandbox
SQUARE_WEBHOOK_SIGNATURE_KEY=your-webhook-signature-key
SQUARE_LOCATION_ID=your-square-location-id

# Square OAuth Configuration
SQUARE_OAUTH_CLIENT_ID=your-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-square-oauth-client-secret
SQUARE_OAUTH_CALLBACK_URL=https://your-domain.com/api/auth/square/callback

# Security Configuration
CORS_ORIGIN=https://your-domain.com
TRUSTED_PROXIES=127.0.0.1,::1
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://your-domain.com

# File Upload Configuration
UPLOAD_PATH=/var/www/nirvana-backend/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,webp,png,gif,pdf,doc,docx

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/your-domain.com.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.com.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# Backup Configuration
BACKUP_PATH=/var/www/nirvana-backend/backups
BACKUP_RETENTION_DAYS=30

# Logging Configuration
LOG_LEVEL=info
LOG_PATH=/var/www/nirvana-backend/logs

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Redis Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
CACHE_TTL=3600

# MISSING BACKEND VARIABLES - CRITICAL ADDITIONS
# Admin Configuration (Used in passport.js for OAuth admin identification)
ADMIN_EMAIL=<EMAIL>
ADMIN_EMAIL_SECONDARY=<EMAIL>

# Password Hashing Configuration (Used in User.js model)
BCRYPT_SALT_ROUNDS=12

# Webhook Configuration (Used in squareWebhookTester.js)
WEBHOOK_URL=https://your-domain.com/api/webhooks/square

# USPS Shipping Integration (Used in uspsService.js)
USPS_USER_ID=your-usps-user-id
USPS_API_URL=https://secure.shippingapis.com/ShippingAPI.dll

# WhatsApp Business API Integration (Used in whatsappService.js)
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_PHONE_NUMBER_ID=your-whatsapp-phone-number-id
WHATSAPP_BUSINESS_ACCOUNT_ID=your-whatsapp-business-account-id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-whatsapp-webhook-verify-token

# Security Configuration (Used in admin-server.js and index.js)
ADMIN_SECURITY_MODE=enhanced
FORCE_HTTPS=true
HTTPS_PORT=443

# Email Configuration Extensions (Used in various email services)
EMAIL_CONNECTION_TIMEOUT=30000
EMAIL_SOCKET_TIMEOUT=30000
EMAIL_POOL=true
EMAIL_MAX_CONNECTIONS=5
EMAIL_MAX_MESSAGES=100

# Additional Security Variables
WEBHOOK_SECRET=your-webhook-secret-32-chars-minimum
API_KEY=your-api-key-24-chars-minimum

# Cache Configuration (Redis - optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_TTL=3600

# Monitoring Configuration
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Admin IP Whitelist (comma-separated, leave empty to disable)
ADMIN_IP_WHITELIST=

# Development/Debug Settings (set to false in production)
DEBUG_MODE=false
ENABLE_CORS_DEBUG=false
ENABLE_SQL_LOGGING=false
