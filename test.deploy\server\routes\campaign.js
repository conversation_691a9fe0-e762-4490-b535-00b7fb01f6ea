const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const campaignController = require('../controllers/campaignController');

// Public routes
// @route   POST /api/newsletter/subscribe
// @desc    Subscribe to newsletter
// @access  Public
router.post('/newsletter/subscribe', [
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('source')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Source must be between 1 and 100 characters')
], campaignController.subscribeToNewsletter);

// @route   GET /api/newsletter/unsubscribe/:token
// @desc    Unsubscribe from newsletter
// @access  Public
router.get('/newsletter/unsubscribe/:token', campaignController.unsubscribeFromNewsletter);

// Admin routes - require admin authentication
router.use('/admin', authenticate);
router.use('/admin', requireAdmin);

// @route   GET /api/admin/campaigns
// @desc    Get all campaigns with pagination and filtering
// @access  Private (Admin)
router.get('/admin/campaigns', campaignController.getAllCampaigns);

// @route   POST /api/admin/campaigns
// @desc    Create new campaign
// @access  Private (Admin)
router.post('/admin/campaigns', [
  body('name')
    .notEmpty()
    .withMessage('Campaign name is required')
    .isLength({ min: 3, max: 100 })
    .withMessage('Campaign name must be between 3 and 100 characters'),
  body('type')
    .isIn(['email', 'push', 'sms', 'in_app'])
    .withMessage('Invalid campaign type'),
  body('targetAudience')
    .isIn(['all', 'customers', 'subscribers', 'segment', 'custom'])
    .withMessage('Invalid target audience'),
  body('subject')
    .if(body('type').equals('email'))
    .notEmpty()
    .withMessage('Subject is required for email campaigns')
    .isLength({ min: 1, max: 200 })
    .withMessage('Subject must be between 1 and 200 characters'),
  body('content')
    .notEmpty()
    .withMessage('Content is required'),
  body('scheduledAt')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid ISO 8601 date'),
  body('segmentCriteria')
    .optional()
    .isObject()
    .withMessage('Segment criteria must be an object')
], campaignController.createCampaign);

// @route   PUT /api/admin/campaigns/:id
// @desc    Update campaign
// @access  Private (Admin)
router.put('/admin/campaigns/:id', [
  body('name')
    .optional()
    .isLength({ min: 3, max: 100 })
    .withMessage('Campaign name must be between 3 and 100 characters'),
  body('subject')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('Subject must be between 1 and 200 characters'),
  body('content')
    .optional()
    .notEmpty()
    .withMessage('Content cannot be empty'),
  body('scheduledAt')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid ISO 8601 date'),
  body('status')
    .optional()
    .isIn(['draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled'])
    .withMessage('Invalid campaign status')
], campaignController.updateCampaign);

// @route   POST /api/admin/campaigns/:id/send
// @desc    Send campaign
// @access  Private (Admin)
router.post('/admin/campaigns/:id/send', campaignController.sendCampaign);

// @route   GET /api/admin/campaigns/:id/analytics
// @desc    Get campaign analytics
// @access  Private (Admin)
router.get('/admin/campaigns/:id/analytics', campaignController.getCampaignAnalytics);

// @route   DELETE /api/admin/campaigns/:id
// @desc    Delete campaign
// @access  Private (Admin)
router.delete('/admin/campaigns/:id', campaignController.deleteCampaign);

// @route   GET /api/admin/email-templates
// @desc    Get email templates
// @access  Private (Admin)
router.get('/admin/email-templates', campaignController.getEmailTemplates);

// @route   POST /api/admin/email-templates
// @desc    Create email template
// @access  Private (Admin)
router.post('/admin/email-templates', [
  body('name')
    .notEmpty()
    .withMessage('Template name is required')
    .isLength({ min: 3, max: 100 })
    .withMessage('Template name must be between 3 and 100 characters'),
  body('category')
    .isIn(['marketing', 'transactional', 'notification', 'welcome', 'abandoned_cart', 'order_confirmation'])
    .withMessage('Invalid template category'),
  body('subject')
    .notEmpty()
    .withMessage('Subject is required')
    .isLength({ min: 1, max: 200 })
    .withMessage('Subject must be between 1 and 200 characters'),
  body('htmlContent')
    .notEmpty()
    .withMessage('HTML content is required'),
  body('variables')
    .optional()
    .isArray()
    .withMessage('Variables must be an array'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean')
], campaignController.createEmailTemplate);

module.exports = router;
