#!/bin/bash

# Test connection to Hostinger server
echo "🔍 Testing Hostinger Server Connection"
echo ""

# Prompt for server details
read -p "Enter your Hostinger server IP or hostname: " SERVER_HOST
read -p "Enter username (default: root): " SERVER_USER
SERVER_USER=${SERVER_USER:-root}

SERVER="$SERVER_USER@$SERVER_HOST"

echo ""
echo "Testing connection to $SERVER..."
echo ""

# Test SSH connection
echo "1️⃣ Testing SSH connection..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes $SERVER "echo 'SSH connection successful!'" 2>/dev/null; then
    echo "✅ SSH connection successful"
else
    echo "❌ SSH connection failed"
    echo "💡 Make sure:"
    echo "   - Server IP/hostname is correct"
    echo "   - SSH key is configured or password authentication is enabled"
    echo "   - Server is running and accessible"
    exit 1
fi

# Test server information
echo ""
echo "2️⃣ Getting server information..."
ssh $SERVER << 'EOF'
echo "Server hostname: $(hostname)"
echo "Ubuntu version: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Unknown')"
echo "Current user: $(whoami)"
echo "Current directory: $(pwd)"
echo "Available disk space:"
df -h / | tail -1
echo ""
echo "Checking if Node.js is installed:"
if command -v node &> /dev/null; then
    echo "Node.js version: $(node --version)"
    echo "NPM version: $(npm --version)"
else
    echo "Node.js is not installed"
fi
EOF

# Test file transfer
echo ""
echo "3️⃣ Testing file transfer..."
echo "test file" > /tmp/test-hostinger.txt
if scp /tmp/test-hostinger.txt $SERVER:/tmp/ 2>/dev/null; then
    echo "✅ File transfer successful"
    ssh $SERVER "rm /tmp/test-hostinger.txt"
    rm /tmp/test-hostinger.txt
else
    echo "❌ File transfer failed"
    rm /tmp/test-hostinger.txt
    exit 1
fi

echo ""
echo "🎉 All tests passed! Your server is ready for deployment."
echo ""
echo "📋 Server Details:"
echo "   Server: $SERVER"
echo "   Ready for deployment: ✅"
echo ""
echo "🚀 To deploy your backend, run:"
echo "   ./scripts/deploy-to-hostinger.sh"
echo ""
echo "💡 Don't forget to update the server details in the deployment script!"
