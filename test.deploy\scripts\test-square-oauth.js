#!/usr/bin/env node

/**
 * Square OAuth Integration Test Script
 * Tests the Square OAuth authentication flow for admin users
 */

const axios = require('axios');
const { execSync } = require('child_process');
require('dotenv').config();

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}[STEP]${colors.reset} ${msg}`)
};

async function testSquareOAuthIntegration() {
  console.log(`${colors.bright}🔍 SQUARE OAUTH INTEGRATION TEST${colors.reset}`);
  console.log('='.repeat(50));

  // Test 1: Environment Variables
  log.step('1. Testing environment variables...');
  const requiredEnvVars = [
    'SQUARE_OAUTH_CLIENT_ID',
    'SQUARE_OAUTH_CLIENT_SECRET',
    'SQUARE_OAUTH_CALLBACK_URL',
    'SQUARE_ENVIRONMENT'
  ];

  let envTestPassed = true;
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      log.error(`Missing environment variable: ${envVar}`);
      envTestPassed = false;
    } else {
      log.success(`✓ ${envVar} is set`);
    }
  }

  if (!envTestPassed) {
    log.error('Environment variables test failed. Please check your .env file.');
    return false;
  }

  // Test 2: Database Migration
  log.step('2. Testing database migration for Square ID field...');
  try {
    const { sequelize } = require('../server/models');
    await sequelize.authenticate();
    
    // Check if square_id column exists
    const [results] = await sequelize.query(
      "SHOW COLUMNS FROM Users LIKE 'square_id'"
    );
    
    if (results.length === 0) {
      log.warning('square_id column not found. Running migration...');
      try {
        execSync('npm run migrate', { stdio: 'inherit' });
        log.success('✓ Migration completed successfully');
      } catch (error) {
        log.error('Migration failed. Please run: npm run migrate');
        return false;
      }
    } else {
      log.success('✓ square_id column exists in Users table');
    }
  } catch (error) {
    log.error(`Database connection failed: ${error.message}`);
    return false;
  }

  // Test 3: Square OAuth URLs
  log.step('3. Testing Square OAuth URLs...');
  const baseUrl = process.env.BACKEND_URL || 'http://localhost:5000';
  const squareLoginUrl = `${baseUrl}/api/auth/square/login`;
  const squareCallbackUrl = `${baseUrl}/api/auth/square/callback`;

  try {
    // Test login endpoint (should redirect to Square)
    const loginResponse = await axios.get(squareLoginUrl, {
      maxRedirects: 0,
      validateStatus: (status) => status === 302
    });
    
    if (loginResponse.status === 302) {
      const location = loginResponse.headers.location;
      if (location && location.includes('squareup')) {
        log.success('✓ Square login endpoint redirects correctly');
      } else {
        log.error('Square login endpoint redirect URL is invalid');
        return false;
      }
    } else {
      log.error('Square login endpoint should return 302 redirect');
      return false;
    }
  } catch (error) {
    if (error.response && error.response.status === 302) {
      log.success('✓ Square login endpoint is working');
    } else {
      log.error(`Square login endpoint test failed: ${error.message}`);
      return false;
    }
  }

  // Test 4: Passport Strategy Configuration
  log.step('4. Testing Passport Square strategy configuration...');
  try {
    const passport = require('../server/config/passport');
    const strategies = passport._strategies;
    
    if (strategies.square) {
      log.success('✓ Square OAuth strategy is registered');
    } else {
      log.error('Square OAuth strategy is not registered');
      return false;
    }
  } catch (error) {
    log.error(`Passport configuration test failed: ${error.message}`);
    return false;
  }

  // Test 5: Frontend Integration
  log.step('5. Testing frontend integration...');
  const fs = require('fs');
  const path = require('path');

  // Check AdminLogin.tsx
  const adminLoginPath = path.join(__dirname, '../src/pages/admin/AdminLogin.tsx');
  if (fs.existsSync(adminLoginPath)) {
    const adminLoginContent = fs.readFileSync(adminLoginPath, 'utf8');
    if (adminLoginContent.includes('handleSquareLogin') && adminLoginContent.includes('Sign in with Square')) {
      log.success('✓ Square OAuth button added to AdminLogin.tsx');
    } else {
      log.error('Square OAuth button not found in AdminLogin.tsx');
      return false;
    }
  } else {
    log.error('AdminLogin.tsx file not found');
    return false;
  }

  // Check SocialLogin.tsx
  const socialLoginPath = path.join(__dirname, '../src/components/auth/SocialLogin.tsx');
  if (fs.existsSync(socialLoginPath)) {
    const socialLoginContent = fs.readFileSync(socialLoginPath, 'utf8');
    if (socialLoginContent.includes('handleSquareLogin') && socialLoginContent.includes('Continue with Square')) {
      log.success('✓ Square OAuth button added to SocialLogin.tsx');
    } else {
      log.error('Square OAuth button not found in SocialLogin.tsx');
      return false;
    }
  } else {
    log.error('SocialLogin.tsx file not found');
    return false;
  }

  // Test 6: Square API Configuration
  log.step('6. Testing Square API configuration...');
  const squareEnvironment = process.env.SQUARE_ENVIRONMENT;
  const expectedAuthUrl = squareEnvironment === 'production' 
    ? 'https://connect.squareup.com/oauth2/authorize'
    : 'https://connect.squareupsandbox.com/oauth2/authorize';
  
  log.success(`✓ Square environment: ${squareEnvironment}`);
  log.success(`✓ Expected auth URL: ${expectedAuthUrl}`);

  console.log('\n' + '='.repeat(50));
  log.success('🎉 All Square OAuth integration tests passed!');
  
  console.log(`\n${colors.bright}Next Steps:${colors.reset}`);
  console.log('1. Configure your Square OAuth application in Square Developer Dashboard');
  console.log('2. Update environment variables with actual Square OAuth credentials');
  console.log('3. Test the OAuth flow in your browser');
  console.log('4. Verify admin role assignment works correctly');
  
  return true;
}

// Run the test
if (require.main === module) {
  testSquareOAuthIntegration()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      log.error(`Test failed with error: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { testSquareOAuthIntegration };
