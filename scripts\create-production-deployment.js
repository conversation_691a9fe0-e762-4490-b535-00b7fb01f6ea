#!/usr/bin/env node

/**
 * Production Environment Deployment Package Creator
 *
 * Creates a complete production deployment package with:
 * - Production configuration for shopnirvanaorganics.com and www.shopnirvanaorganics.com
 * - Optimized logging and secure rate limits
 * - All required files and scripts for VPS deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(70));
    console.log(`🚀 ${msg}`);
    console.log('='.repeat(70));
  }
};

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log.info(`Created directory: ${dirPath}`);
  }
}

function copyDirectory(src, dest) {
  ensureDirectoryExists(dest);

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    log.info(`Removed directory: ${dirPath}`);
  }
}

async function createProductionDeployment() {
  try {
    log.header('Creating Production Environment Deployment Package');

    const deploymentDir = 'deploy';

    // Step 1: Create directory structure
    log.header('Creating Directory Structure');
    const requiredDirs = [
      'config',
      'scripts',
      'nginx',
      'systemd',
      'ssl',
      'backups',
      'logs',
      'docs'
    ];

    ensureDirectoryExists(deploymentDir);
    requiredDirs.forEach(dir => {
      ensureDirectoryExists(path.join(deploymentDir, dir));
    });

    // Step 2: Copy unified frontend build
    log.header('Copying Frontend Build');
    if (!fs.existsSync('dist')) {
      throw new Error('Unified build not found. Please run npm run build:unified first.');
    }
    copyDirectory('dist', path.join(deploymentDir, 'dist'));
    log.success('Copied unified frontend build');

    // Step 3: Copy backend server
    log.header('Copying Backend Server');
    copyDirectory('server', path.join(deploymentDir, 'server'));
    log.success('Copied backend server');

    // Step 4: Copy scripts
    log.header('Copying Scripts');
    copyDirectory('scripts', path.join(deploymentDir, 'scripts'));
    log.success('Copied deployment scripts');

    // Step 5: Create production-specific configurations
    log.header('Creating Production Environment Configurations');
    await createProductionConfigurations(deploymentDir);

    // Step 6: Create deployment scripts
    log.header('Creating Deployment Scripts');
    await createProductionDeploymentScripts(deploymentDir);

    // Step 7: Create package files
    log.header('Creating Package Files');
    await createProductionPackageFiles(deploymentDir);

    // Step 8: Create documentation
    log.header('Creating Documentation');
    await createProductionDocumentation(deploymentDir);

    // Step 9: Create security and validation scripts
    log.header('Creating Security and Validation Scripts');
    await createSecurityScripts(deploymentDir);

    log.header('Production Deployment Package Complete');
    log.success('Production environment deployment package created successfully!');
    log.info(`Package location: ${path.resolve(deploymentDir)}`);
    log.info(`Target domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com`);
    log.info(`Environment: Production`);

  } catch (error) {
    log.error(`Production deployment package creation failed: ${error.message}`);
    process.exit(1);
  }
}

async function createProductionConfigurations(deploymentDir) {
  // Create production environment configuration
  const envTemplate = `# Nirvana Organics E-commerce - Production Environment Configuration
# Target Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com

# Application Configuration
NODE_ENV=production
PORT=5000
APP_NAME="Nirvana Organics E-commerce"
APP_URL=https://shopnirvanaorganics.com

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_production
DB_USER=nirvana_prod_user
DB_PASSWORD=CHANGE_THIS_TO_A_VERY_SECURE_PASSWORD

# JWT Configuration (MUST BE CHANGED)
JWT_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_SECRET_KEY_AT_LEAST_64_CHARACTERS_LONG
JWT_REFRESH_SECRET=CHANGE_THIS_TO_ANOTHER_VERY_SECURE_SECRET_KEY_AT_LEAST_64_CHARACTERS_LONG
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
EMAIL_FROM="Nirvana Organics <<EMAIL>>"

# Square Payment Integration (Production)
SQUARE_ACCESS_TOKEN=your-square-production-access-token
SQUARE_APPLICATION_ID=your-square-production-application-id
SQUARE_ENVIRONMENT=production
SQUARE_WEBHOOK_SIGNATURE_KEY=your-production-webhook-signature-key

# File Upload Configuration
UPLOAD_PATH=/var/www/nirvana-backend/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_SESSION_SECRET_AT_LEAST_64_CHARACTERS
CORS_ORIGIN=https://shopnirvanaorganics.com,https://www.shopnirvanaorganics.com

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/shopnirvanaorganics.com.crt
SSL_KEY_PATH=/etc/ssl/private/shopnirvanaorganics.com.key

# Backup Configuration
BACKUP_PATH=/var/www/nirvana-backend/backups
BACKUP_RETENTION_DAYS=30

# Logging Configuration (Production level)
LOG_LEVEL=info
LOG_PATH=/var/www/nirvana-backend/logs

# Rate Limiting (Production security)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD
CACHE_TTL=3600

# Security Headers
HSTS_MAX_AGE=31536000
CSP_REPORT_URI=https://shopnirvanaorganics.com/api/csp-report

# Monitoring
HEALTH_CHECK_TOKEN=CHANGE_THIS_HEALTH_CHECK_TOKEN
ADMIN_NOTIFICATION_EMAIL=<EMAIL>`;

  fs.writeFileSync(path.join(deploymentDir, 'config', '.env.production.template'), envTemplate);
  log.success('Created production environment configuration template');

  // Create production nginx configuration with enhanced security
  const nginxConfig = `# Nirvana Organics E-commerce - Production Nginx Configuration
# Target Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com

# Rate limiting zones - PRODUCTION SECURITY
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=auth:10m rate=30r/m;
limit_req_zone $binary_remote_addr zone=admin:10m rate=60r/m;
limit_req_zone $binary_remote_addr zone=upload:10m rate=10r/m;

# Upstream backend servers
upstream nirvana_backend {
    least_conn;
    server 127.0.0.1:5000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;

    # Let's Encrypt challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://\\$server_name\\$request_uri;
    }
}

# Main HTTPS server block
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;

    # SSL Configuration (will be configured by Certbot)
    ssl_certificate /etc/ssl/certs/shopnirvanaorganics.com.crt;
    ssl_certificate_key /etc/ssl/private/shopnirvanaorganics.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers (Production)
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;

    # Hide Nginx version
    server_tokens off;

    # Root directory for unified frontend
    root /var/www/nirvana-backend/dist;
    index index.html;

    # Logging (production level)
    access_log /var/log/nginx/nirvana-production-access.log combined;
    error_log /var/log/nginx/nirvana-production-error.log warn;

    # Client settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;

    # API routes with production rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \\$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;
        proxy_set_header X-Forwarded-Host \\$host;
        proxy_set_header X-Forwarded-Port \\$server_port;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        proxy_cache_bypass \\$http_upgrade;
        proxy_hide_header X-Powered-By;
    }

    # Authentication routes with strict rate limiting
    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;
        limit_req_status 429;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # File upload routes
    location /api/upload/ {
        limit_req zone=upload burst=3 nodelay;
        limit_req_status 429;

        client_max_body_size 10M;
        client_body_timeout 120s;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;

        proxy_connect_timeout 60s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        proxy_request_buffering off;
    }

    # Admin API routes (enhanced security)
    location /api/admin/ {
        # Optional: IP whitelist for admin API access
        # allow ***********/24;  # Your office network
        # allow 10.0.0.0/8;      # Your VPN network
        # deny all;

        limit_req zone=admin burst=15 nodelay;
        limit_req_status 429;

        add_header X-Admin-API "true" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;

        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;
        proxy_set_header X-Admin-Request "true";

        proxy_connect_timeout 30s;
        proxy_send_timeout 45s;
        proxy_read_timeout 45s;

        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Admin panel static files (enhanced security)
    location /admin/ {
        # Optional: IP whitelist for admin access
        # allow ***********/24;  # Your office network
        # allow 10.0.0.0/8;      # Your VPN network
        # deny all;

        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header X-Robots-Tag "noindex, nofollow" always;

        limit_req zone=admin burst=10 nodelay;
        limit_req_status 429;

        try_files \\$uri \\$uri/ /admin/admin.html;

        location ~* ^/admin/.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)\\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
            add_header X-Content-Type-Options "nosniff";
            add_header X-Frame-Options "DENY";
        }

        location ~* ^/admin/.*\\.html\\$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header X-Frame-Options "DENY";
            add_header X-XSS-Protection "1; mode=block";
            add_header X-Content-Type-Options "nosniff";
            add_header X-Robots-Tag "noindex, nofollow";
        }
    }

    # Static file serving
    location /uploads/ {
        alias /var/www/nirvana-backend/uploads/;

        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        add_header X-Content-Type-Options "nosniff";

        location ~* \\.(php|pl|py|jsp|asp|sh|cgi)\\$ {
            deny all;
        }

        add_header Access-Control-Allow-Origin "https://shopnirvanaorganics.com";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Range";
    }

    # Static assets with long-term caching
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)\\$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";

        gzip_static on;

        add_header Access-Control-Allow-Origin "https://shopnirvanaorganics.com";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header X-Content-Type-Options "nosniff";

        try_files \\$uri \\$uri/ =404;
    }

    # Frontend SPA routing
    location / {
        try_files \\$uri \\$uri/ /index.html;

        location ~* \\.html\\$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
    }

    # Health check endpoint
    location /api/health {
        proxy_pass http://nirvana_backend;
        proxy_http_version 1.1;
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;

        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;

        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Robots.txt for production
    location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\\nDisallow: /admin/\\nDisallow: /api/\\nSitemap: https://shopnirvanaorganics.com/sitemap.xml\\n";
    }

    # Security: Block access to sensitive files
    location ~ /\\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \\.(env|log|sql|conf)\\$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~* \\.(php|asp|aspx|jsp)\\$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}`;

  fs.writeFileSync(path.join(deploymentDir, 'nginx', 'nirvana-production.conf'), nginxConfig);
  log.success('Created production nginx configuration');

  // Create PM2 ecosystem configuration for production
  const pm2Config = `module.exports = {
  apps: [
    {
      name: 'nirvana-production',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend',
      instances: 'max',
      exec_mode: 'cluster',

      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      env_file: '.env',

      max_memory_restart: '1G',
      node_args: [
        '--max-old-space-size=1024',
        '--optimize-for-size'
      ],

      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/production-error.log',
      out_file: './logs/production-out.log',
      log_file: './logs/production-combined.log',
      merge_logs: true,
      time: true,

      autorestart: true,
      max_restarts: 10,
      min_uptime: '30s',
      restart_delay: 5000,

      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'dist',
        '.git'
      ],

      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,

      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,

      instance_var: 'INSTANCE_ID',
      combine_logs: true,

      exp_backoff_restart_delay: 100,
      source_map_support: true,
      stop_exit_codes: [0]
    }
  ]
};`;

  fs.writeFileSync(path.join(deploymentDir, 'ecosystem.config.production.js'), pm2Config);
  log.success('Created PM2 production configuration');
}

async function createProductionDeploymentScripts(deploymentDir) {
  // Create main deployment script for production
  const deployScript = `#!/bin/bash

# Nirvana Organics E-commerce - Production Environment Deployment Script
# Target Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com

set -e

# Color codes
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
BLUE='\\033[0;34m'
NC='\\033[0m'

log_info() {
    echo -e "\${BLUE}[INFO]\${NC} \$1"
}

log_success() {
    echo -e "\${GREEN}[SUCCESS]\${NC} \$1"
}

log_warning() {
    echo -e "\${YELLOW}[WARNING]\${NC} \$1"
}

log_error() {
    echo -e "\${RED}[ERROR]\${NC} \$1"
}

log_header() {
    echo -e "\\n\${BLUE}======================================\${NC}"
    echo -e "\${BLUE}\$1\${NC}"
    echo -e "\${BLUE}======================================\${NC}"
}

# Configuration
APP_NAME="nirvana-organics-production"
APP_USER="nirvana"
APP_DIR="/var/www/nirvana-backend"
NGINX_CONF="/etc/nginx/sites-available/nirvana-production"
SYSTEMD_SERVICE="/etc/systemd/system/nirvana-production.service"

if [[ \$EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

log_header "🚀 Starting Nirvana Organics Production Environment Deployment"

# System preparation
log_header "📋 System Preparation"
apt update
apt install -y curl wget gnupg2 software-properties-common ufw fail2ban

# Install Node.js
log_info "Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# Install Nginx
log_info "Installing Nginx..."
apt install -y nginx

# Install MySQL
log_info "Installing MySQL..."
apt install -y mysql-server

# Install PM2
log_info "Installing PM2..."
npm install -g pm2

# Install Redis (for caching)
log_info "Installing Redis..."
apt install -y redis-server

# Configure firewall
log_header "🔒 Configuring Firewall"
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 'Nginx Full'
ufw --force enable

# Create application user
log_header "👤 Setting Up Application User"
if ! id "\$APP_USER" &>/dev/null; then
    log_info "Creating application user: \$APP_USER"
    useradd -m -s /bin/bash \$APP_USER
    usermod -aG sudo \$APP_USER
    log_success "Created application user: \$APP_USER"
else
    log_info "Application user already exists: \$APP_USER"
fi

# Create directory structure
log_header "📁 Creating Directory Structure"
mkdir -p \$APP_DIR/{logs,uploads,backups,config}
mkdir -p \$APP_DIR/uploads/{products,categories,banners,documents}

# Copy application files
log_header "📋 Copying Application Files"
cp -r server/ \$APP_DIR/
cp -r dist/ \$APP_DIR/
cp -r scripts/ \$APP_DIR/
cp -r config/ \$APP_DIR/
cp ecosystem.config.production.js \$APP_DIR/ecosystem.config.js

# Set permissions
log_header "🔒 Setting File Permissions"
chown -R \$APP_USER:\$APP_USER \$APP_DIR
chmod -R 755 \$APP_DIR
chmod 700 \$APP_DIR/config

# Install dependencies
log_header "📦 Installing Dependencies"
cd \$APP_DIR
sudo -u \$APP_USER npm ci --production --silent

# Configure environment
log_header "⚙️ Environment Configuration"
if [ ! -f "\$APP_DIR/.env" ]; then
    cp config/.env.production.template \$APP_DIR/.env
    chown \$APP_USER:\$APP_USER \$APP_DIR/.env
    chmod 600 \$APP_DIR/.env
    log_warning "IMPORTANT: Please edit \$APP_DIR/.env with your actual production values"
    log_warning "This includes database credentials, JWT secrets, and API keys"
    read -p "Press Enter after you have configured the .env file..."
fi

# Configure Nginx
log_header "🌐 Configuring Nginx"
cp nginx/nirvana-production.conf \$NGINX_CONF
ln -sf \$NGINX_CONF /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
systemctl enable nginx

# Setup database
log_header "🗄️ Database Setup"
sudo -u \$APP_USER node scripts/setup-database.js

# Start application with PM2
log_header "🚀 Starting Application"
sudo -u \$APP_USER pm2 start ecosystem.config.js
sudo -u \$APP_USER pm2 save
pm2 startup
sudo env PATH=\$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u \$APP_USER --hp /home/<USER>

log_header "🎉 Production Environment Deployment Complete!"
log_success "Nirvana Organics Production Environment deployed successfully!"
log_info "Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com"
log_info "Environment: Production"
log_info "Application Directory: \$APP_DIR"
log_warning "Next Steps:"
log_warning "1. Configure SSL certificates with Let's Encrypt"
log_warning "2. Update DNS records to point to this server"
log_warning "3. Test all functionality thoroughly"
log_warning "4. Set up monitoring and backups"`;

  fs.writeFileSync(path.join(deploymentDir, 'deploy-production.sh'), deployScript);
  log.success('Created production deployment script');
}

async function createProductionPackageFiles(deploymentDir) {
  // Create package.json for production deployment
  const packageJson = {
    name: "nirvana-organics-production",
    version: "1.0.0",
    description: "Nirvana Organics E-commerce Production Environment",
    main: "server/index.js",
    scripts: {
      start: "node server/index.js",
      "start:production": "NODE_ENV=production node server/index.js",
      "setup:database": "node scripts/setup-database.js",
      "create:tables": "node scripts/create-database-tables.js",
      "seed:database": "node scripts/seed-database.js",
      "test:database": "node scripts/test-database-connection.js",
      "backup:database": "node scripts/backup-database.js",
      "migrate": "node scripts/run-migrations.js",
      "status": "node scripts/system-status.js",
      "create:admin": "node scripts/create-default-admin.js",
      "deploy": "chmod +x deploy-production.sh && ./deploy-production.sh"
    },
    dependencies: {
      express: "^4.18.2",
      mysql2: "^3.6.0",
      sequelize: "^6.32.1",
      bcryptjs: "^2.4.3",
      jsonwebtoken: "^9.0.2",
      cors: "^2.8.5",
      helmet: "^7.0.0",
      "express-rate-limit": "^6.8.1",
      multer: "^1.4.5-lts.1",
      nodemailer: "^6.9.4",
      square: "^30.0.0",
      dotenv: "^16.3.1",
      "express-validator": "^7.0.1",
      winston: "^3.10.0",
      redis: "^4.6.7"
    },
    engines: {
      node: ">=18.0.0",
      npm: ">=8.0.0"
    },
    author: "Nirvana Organics Team",
    license: "MIT"
  };

  fs.writeFileSync(
    path.join(deploymentDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );
  log.success('Created production package.json');
}

async function createProductionDocumentation(deploymentDir) {
  const readmeContent = `# Nirvana Organics E-commerce - Production Environment

This package contains the production deployment for the Nirvana Organics E-commerce platform.

## 🎯 Production Environment Details

- **Target Domains**: shopnirvanaorganics.com, www.shopnirvanaorganics.com
- **Environment**: Production
- **Purpose**: Live e-commerce platform
- **Configuration**: Optimized logging, secure rate limits, enhanced security

## 🚀 Quick Deployment

1. **Upload this package to your VPS server**
2. **Extract and run the deployment script:**
   \`\`\`bash
   tar -xzf nirvana-production-deployment.tar.gz
   cd deploy
   chmod +x deploy-production.sh
   sudo ./deploy-production.sh
   \`\`\`
3. **Configure SSL certificates**
4. **Update DNS records**
5. **Test thoroughly before going live**

## 📋 Prerequisites

- Ubuntu 20.04+ or CentOS 8+ VPS server
- Root or sudo access
- Minimum 2GB RAM, 20GB storage
- Domains: shopnirvanaorganics.com, www.shopnirvanaorganics.com

## 🏗️ What's Included

### Frontend Applications
- **Main E-commerce Store**: Accessible at \`/\` (root path)
- **Admin Panel**: Accessible at \`/admin\` path with enhanced security
- **Unified Build**: Both applications served from single domain

### Backend Services
- **Node.js API Server**: Complete REST API with authentication
- **Database Integration**: MySQL with automated setup
- **Square Payment Integration**: Production environment
- **Email System**: SMTP configuration for notifications
- **Redis Caching**: Performance optimization

### Production-Specific Features
- **Enhanced Security**: Strict rate limits, security headers
- **SSL/HTTPS**: Full SSL configuration
- **Firewall Configuration**: UFW and fail2ban setup
- **Performance Optimization**: Clustering, caching, compression
- **Monitoring**: Health checks and logging

## ⚙️ Configuration

### Environment Variables
Edit \`/var/www/nirvana-backend/.env\` with your production values:

- **Database**: MySQL connection for production database
- **JWT Secrets**: Secure authentication tokens (64+ characters)
- **Email**: SMTP configuration for production notifications
- **Square**: Production payment processing credentials
- **Domains**: shopnirvanaorganics.com, www.shopnirvanaorganics.com
- **Security**: Session secrets, CORS origins

### SSL Certificates
For Let's Encrypt:
\`\`\`bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com
\`\`\`

## 🔧 Management Commands

### Application Management
\`\`\`bash
# Check application status
sudo pm2 status

# View application logs
sudo pm2 logs nirvana-production

# Restart application
sudo pm2 restart nirvana-production

# Stop application
sudo pm2 stop nirvana-production

# Monitor application
sudo pm2 monit
\`\`\`

### Database Management
\`\`\`bash
# Create database backup
cd /var/www/nirvana-backend
node scripts/backup-database.js

# Run database migrations
node scripts/run-migrations.js

# Create admin user
node scripts/create-default-admin.js

# Check database status
node scripts/test-database-connection.js
\`\`\`

### System Monitoring
\`\`\`bash
# Check system status
cd /var/www/nirvana-backend
node scripts/system-status.js

# Check API health
curl https://shopnirvanaorganics.com/api/health

# Monitor logs
sudo tail -f /var/log/nginx/nirvana-production-access.log
sudo pm2 logs nirvana-production --lines 100
\`\`\`

## 🔒 Security Features

- **Enhanced Rate Limiting**: Production-level API protection
- **Security Headers**: Comprehensive HTTP security headers
- **HTTPS Enforcement**: Automatic HTTP to HTTPS redirects
- **Admin Protection**: Enhanced security for admin panel
- **Firewall Configuration**: UFW and fail2ban protection
- **File Upload Security**: Restricted file types and sizes
- **IP Whitelisting**: Optional admin access restrictions

## 📊 Performance Features

- **PM2 Clustering**: Multi-core utilization
- **Redis Caching**: Database query optimization
- **Nginx Compression**: Gzip compression for assets
- **Static Asset Caching**: Long-term browser caching
- **CDN Ready**: Optimized for CDN integration

## 🆘 Troubleshooting

### Common Issues

**Application won't start:**
\`\`\`bash
# Check PM2 logs
sudo pm2 logs nirvana-production

# Verify environment file
cat /var/www/nirvana-backend/.env

# Test database connection
cd /var/www/nirvana-backend
node scripts/test-database-connection.js
\`\`\`

**SSL certificate issues:**
\`\`\`bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew

# Test SSL configuration
sudo nginx -t
\`\`\`

**Performance issues:**
\`\`\`bash
# Monitor system resources
htop
sudo pm2 monit

# Check Redis status
redis-cli ping

# Analyze logs
sudo tail -f /var/log/nginx/nirvana-production-error.log
\`\`\`

## 🔄 Updates and Maintenance

### Application Updates
1. Upload new deployment package
2. Stop the application: \`sudo pm2 stop nirvana-production\`
3. Backup current installation and database
4. Replace application files
5. Run migrations if needed
6. Start the application: \`sudo pm2 start nirvana-production\`

### Security Updates
\`\`\`bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js dependencies
cd /var/www/nirvana-backend
npm audit fix

# Update PM2
sudo npm update -g pm2
\`\`\`

### Backup Strategy
- **Database**: Daily automated backups
- **Files**: Weekly file system backups
- **Configuration**: Version control for configurations

---

**Environment**: Production
**Target Domains**: shopnirvanaorganics.com, www.shopnirvanaorganics.com
**Last Updated**: ${new Date().toISOString().split('T')[0]}
**Support**: Nirvana Organics Development Team`;

  fs.writeFileSync(path.join(deploymentDir, 'README.md'), readmeContent);
  log.success('Created production environment documentation');
}

async function createSecurityScripts(deploymentDir) {
  // Create security validation script
  const securityScript = `#!/usr/bin/env node

/**
 * Production Security Validation Script
 * Validates security configurations and settings
 */

const fs = require('fs');
const path = require('path');

const log = {
  info: (msg) => console.log(\`ℹ️  \${msg}\`),
  success: (msg) => console.log(\`✅ \${msg}\`),
  error: (msg) => console.error(\`❌ \${msg}\`),
  warning: (msg) => console.warn(\`⚠️  \${msg}\`)
};

function validateSecurity() {
  log.info('Running production security validation...');

  let issues = 0;

  // Check environment file
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');

    if (envContent.includes('CHANGE_THIS')) {
      log.error('Environment file contains default values that must be changed');
      issues++;
    }

    if (!envContent.includes('NODE_ENV=production')) {
      log.error('NODE_ENV is not set to production');
      issues++;
    }

    log.success('Environment file exists');
  } else {
    log.error('Environment file (.env) not found');
    issues++;
  }

  // Check file permissions
  try {
    const stats = fs.statSync('.env');
    const mode = stats.mode & parseInt('777', 8);
    if (mode !== parseInt('600', 8)) {
      log.warning('Environment file permissions should be 600');
    } else {
      log.success('Environment file permissions are secure');
    }
  } catch (error) {
    log.error('Could not check environment file permissions');
    issues++;
  }

  // Check SSL certificates
  if (fs.existsSync('/etc/ssl/certs/shopnirvanaorganics.com.crt')) {
    log.success('SSL certificate found');
  } else {
    log.warning('SSL certificate not found - configure SSL before going live');
  }

  if (issues === 0) {
    log.success('Security validation passed');
    process.exit(0);
  } else {
    log.error(\`Security validation failed with \${issues} issues\`);
    process.exit(1);
  }
}

validateSecurity();`;

  fs.writeFileSync(path.join(deploymentDir, 'scripts', 'validate-security.js'), securityScript);
  log.success('Created security validation script');

  // Create environment validation script
  const envValidationScript = `#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates all required environment variables are set
 */

const requiredVars = [
  'NODE_ENV',
  'PORT',
  'DB_HOST',
  'DB_NAME',
  'DB_USER',
  'DB_PASSWORD',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'EMAIL_USER',
  'EMAIL_PASS',
  'SQUARE_ACCESS_TOKEN',
  'SQUARE_APPLICATION_ID'
];

function validateEnvironment() {
  console.log('🔍 Validating environment configuration...');

  let missing = [];
  let hasDefaults = [];

  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      missing.push(varName);
    } else if (value.includes('CHANGE_THIS') || value.includes('your-')) {
      hasDefaults.push(varName);
    }
  });

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => console.error(\`   - \${varName}\`));
  }

  if (hasDefaults.length > 0) {
    console.error('❌ Environment variables with default values (must be changed):');
    hasDefaults.forEach(varName => console.error(\`   - \${varName}\`));
  }

  if (missing.length === 0 && hasDefaults.length === 0) {
    console.log('✅ Environment validation passed');
    process.exit(0);
  } else {
    console.error('❌ Environment validation failed');
    process.exit(1);
  }
}

validateEnvironment();`;

  fs.writeFileSync(path.join(deploymentDir, 'scripts', 'validate-environment.js'), envValidationScript);
  log.success('Created environment validation script');
}

// Run the deployment creation if this script is executed directly
if (require.main === module) {
  createProductionDeployment();
}

module.exports = { createProductionDeployment };
