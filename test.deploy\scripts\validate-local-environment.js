#!/usr/bin/env node

/**
 * Local Environment Validation Script
 * Validates all required environment variables and dependencies for local testing
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
};

async function validateEnvironment() {
  log.header('🔍 Validating Local Environment Configuration');

  let hasErrors = false;

  // Check if .env file exists
  const envPath = path.join(process.cwd(), '.env');
  if (!fs.existsSync(envPath)) {
    log.warning('.env file not found. Creating from template...');
    
    const templatePath = path.join(process.cwd(), '.env.local');
    if (fs.existsSync(templatePath)) {
      fs.copyFileSync(templatePath, envPath);
      log.success('.env file created from .env.local template');
      log.warning('Please update .env file with your actual configuration values');
    } else {
      log.error('.env.local template not found. Please create .env file manually');
      hasErrors = true;
    }
  } else {
    log.success('.env file found');
  }

  // Load environment variables
  require('dotenv').config();

  // Required environment variables
  const requiredVars = [
    'DB_HOST',
    'DB_PORT', 
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'PORT',
    'NODE_ENV'
  ];

  log.header('📋 Checking Required Environment Variables');
  
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      log.error(`Missing required environment variable: ${varName}`);
      hasErrors = true;
    } else {
      log.success(`${varName} is set`);
    }
  }

  // Test database connection
  log.header('🗄️ Testing Database Connection');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD
    });

    await connection.execute('SELECT 1');
    log.success('Database connection successful');

    // Check if database exists
    const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [process.env.DB_NAME]);
    if (databases.length === 0) {
      log.warning(`Database '${process.env.DB_NAME}' does not exist. It will be created during setup.`);
    } else {
      log.success(`Database '${process.env.DB_NAME}' exists`);
    }

    await connection.end();
  } catch (error) {
    log.error(`Database connection failed: ${error.message}`);
    hasErrors = true;
  }

  // Check Node.js version
  log.header('🟢 Checking Node.js Version');
  
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion >= 18) {
    log.success(`Node.js version ${nodeVersion} is compatible`);
  } else {
    log.error(`Node.js version ${nodeVersion} is not supported. Please upgrade to Node.js 18 or higher`);
    hasErrors = true;
  }

  // Check if required directories exist
  log.header('📁 Checking Directory Structure');
  
  const requiredDirs = [
    'uploads',
    'uploads/products',
    'uploads/categories', 
    'uploads/banners',
    'uploads/documents',
    'server/logs'
  ];

  for (const dir of requiredDirs) {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      log.warning(`Directory '${dir}' does not exist. Creating...`);
      fs.mkdirSync(dirPath, { recursive: true });
      log.success(`Created directory '${dir}'`);
    } else {
      log.success(`Directory '${dir}' exists`);
    }
  }

  // Check package.json dependencies
  log.header('📦 Checking Dependencies');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    log.success('package.json found');
    
    const nodeModulesPath = path.join(process.cwd(), 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
      log.warning('node_modules not found. Run "npm install" to install dependencies');
    } else {
      log.success('node_modules directory exists');
    }
  } else {
    log.error('package.json not found');
    hasErrors = true;
  }

  // Summary
  log.header('📊 Validation Summary');
  
  if (hasErrors) {
    log.error('Environment validation failed. Please fix the errors above before proceeding.');
    process.exit(1);
  } else {
    log.success('Environment validation passed! Ready for local testing.');
  }
}

// Run validation
validateEnvironment().catch(error => {
  log.error(`Validation failed: ${error.message}`);
  process.exit(1);
});
