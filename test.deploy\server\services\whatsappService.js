const axios = require('axios');
require('dotenv').config();

class WhatsAppService {
  constructor() {
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN;
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;
    this.businessAccountId = process.env.WHATSAPP_BUSINESS_ACCOUNT_ID;
    this.apiUrl = 'https://graph.facebook.com/v18.0';
    this.webhookVerifyToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN;
  }

  /**
   * Validate WhatsApp configuration
   */
  validateConfiguration() {
    const requiredFields = [
      'WHATSAPP_ACCESS_TOKEN',
      'WHATSAPP_PHONE_NUMBER_ID'
    ];

    const missingFields = requiredFields.filter(field => !process.env[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`Missing WhatsApp configuration: ${missingFields.join(', ')}`);
    }
    
    return true;
  }

  /**
   * Send a text message
   */
  async sendTextMessage(to, message) {
    try {
      this.validateConfiguration();

      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          to: to.replace(/[^0-9]/g, ''), // Remove non-numeric characters
          type: 'text',
          text: {
            body: message
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        messageId: response.data.messages[0].id,
        status: 'sent'
      };

    } catch (error) {
      console.error('WhatsApp send message error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Send order confirmation message
   */
  async sendOrderConfirmation(customerPhone, orderData) {
    const message = `🛒 *Order Confirmation - Nirvana Organics*

Order #: ${orderData.orderNumber}
Total: $${parseFloat(orderData.pricing.total || 0).toFixed(2)}

Items:
${orderData.items.map(item => `• ${item.productName} x${item.quantity} - $${parseFloat(item.price || 0).toFixed(2)}`).join('\n')}

📦 Shipping: ${orderData.shipping.method}
📍 Address: ${this.formatAddress(orderData.shipping.address)}

💳 Payment Status: ${orderData.payment.status}

We'll send you tracking information once your order ships!

Questions? Reply to this message or email <NAME_EMAIL>

Thank you for choosing Nirvana Organics! 🌿`;

    return await this.sendTextMessage(customerPhone, message);
  }

  /**
   * Send shipping notification
   */
  async sendShippingNotification(customerPhone, orderData, trackingNumber) {
    const message = `📦 *Your Order Has Shipped - Nirvana Organics*

Order #: ${orderData.orderNumber}
Tracking #: ${trackingNumber}

Your order is on its way! 🚚

Track your package: https://tools.usps.com/go/TrackConfirmAction?tLabels=${trackingNumber}

Estimated Delivery: ${orderData.estimatedDelivery ? new Date(orderData.estimatedDelivery).toLocaleDateString() : 'TBD'}

Questions? Reply to this message!

Thank you for choosing Nirvana Organics! 🌿`;

    return await this.sendTextMessage(customerPhone, message);
  }

  /**
   * Send delivery confirmation
   */
  async sendDeliveryConfirmation(customerPhone, orderData) {
    const message = `✅ *Delivered - Nirvana Organics*

Order #: ${orderData.orderNumber}

Your order has been delivered! 📦

We hope you love your products. If you have any questions or concerns, please don't hesitate to reach out.

Consider leaving us a review! Your feedback helps us serve you better.

Thank you for choosing Nirvana Organics! 🌿`;

    return await this.sendTextMessage(customerPhone, message);
  }

  /**
   * Send template message (for approved templates)
   */
  async sendTemplateMessage(to, templateName, languageCode = 'en', components = []) {
    try {
      this.validateConfiguration();

      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          to: to.replace(/[^0-9]/g, ''),
          type: 'template',
          template: {
            name: templateName,
            language: {
              code: languageCode
            },
            components: components
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        messageId: response.data.messages[0].id,
        status: 'sent'
      };

    } catch (error) {
      console.error('WhatsApp template message error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhook(mode, token, challenge) {
    if (mode === 'subscribe' && token === this.webhookVerifyToken) {
      return challenge;
    }
    return null;
  }

  /**
   * Process incoming webhook
   */
  processWebhook(body) {
    try {
      if (body.object === 'whatsapp_business_account') {
        const entries = body.entry || [];
        
        for (const entry of entries) {
          const changes = entry.changes || [];
          
          for (const change of changes) {
            if (change.field === 'messages') {
              const messages = change.value.messages || [];
              const contacts = change.value.contacts || [];
              
              for (const message of messages) {
                this.handleIncomingMessage(message, contacts);
              }
            }
          }
        }
      }
      
      return { success: true };
    } catch (error) {
      console.error('WhatsApp webhook processing error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle incoming message
   */
  async handleIncomingMessage(message, contacts) {
    try {
      const contact = contacts.find(c => c.wa_id === message.from);
      const customerName = contact?.profile?.name || 'Customer';
      
      console.log(`Incoming WhatsApp message from ${customerName} (${message.from}):`, message.text?.body);
      
      // Here you can implement auto-responses, order inquiries, etc.
      // For now, we'll just log the message
      
      // Example: Auto-response for order inquiries
      if (message.text?.body?.toLowerCase().includes('order')) {
        await this.sendTextMessage(
          message.from,
          `Hi ${customerName}! 👋 Thanks for reaching out about your order. Our team will get back to you shortly. For immediate assistance, you can also email <NAME_EMAIL>`
        );
      }
      
    } catch (error) {
      console.error('Error handling incoming message:', error);
    }
  }

  /**
   * Get message status
   */
  async getMessageStatus(messageId) {
    try {
      this.validateConfiguration();

      const response = await axios.get(
        `${this.apiUrl}/${messageId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );

      return {
        success: true,
        status: response.data
      };

    } catch (error) {
      console.error('WhatsApp message status error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Format address for messages
   */
  formatAddress(address) {
    if (!address) return 'Address not provided';
    
    return `${address.street || ''}, ${address.city || ''}, ${address.state || ''} ${address.zipCode || ''}`.trim();
  }

  /**
   * Send customer support message
   */
  async sendSupportMessage(customerPhone, message, orderNumber = null) {
    const supportMessage = orderNumber 
      ? `🛠️ *Support - Nirvana Organics*\n\nRe: Order #${orderNumber}\n\n${message}\n\nNeed more help? Reply to this message!`
      : `🛠️ *Support - Nirvana Organics*\n\n${message}\n\nNeed more help? Reply to this message!`;

    return await this.sendTextMessage(customerPhone, supportMessage);
  }

  /**
   * Test WhatsApp connection
   */
  async testConnection() {
    try {
      this.validateConfiguration();

      const response = await axios.get(
        `${this.apiUrl}/${this.phoneNumberId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );

      return {
        success: true,
        phoneNumber: response.data.display_phone_number,
        status: 'connected'
      };

    } catch (error) {
      console.error('WhatsApp connection test error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }
}

module.exports = new WhatsAppService();
