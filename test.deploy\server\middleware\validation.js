const { body, param, query } = require('express-validator');
const { formatValidationErrors, sendError } = require('./errorHandler');

// Middleware to handle validation results
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = formatValidationErrors(errors);
    return sendError(res, 'Validation failed', 400, formattedErrors, 'VALIDATION_ERROR');
  }
  next();
};

// User validation rules
const validateRegister = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date of birth')
    .custom((value) => {
      if (value) {
        const birthDate = new Date(value);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }

        if (age < 21) {
          throw new Error('You must be at least 21 years old to register');
        }
      }
      return true;
    })
];

const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const validateUpdateProfile = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date of birth')
];

const validatePasswordReset = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
];

const validateResetPassword = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number')
];

// Product validation rules
const validateProduct = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Product name must be between 2 and 200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10 })
    .withMessage('Product description must be at least 10 characters'),
  body('sku')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('SKU must be between 3 and 50 characters'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('quantity')
    .isInt({ min: 0 })
    .withMessage('Quantity must be a non-negative integer'),
  body('categoryId')
    .isInt({ min: 1 })
    .withMessage('Please provide a valid category ID'),
  body('cannabinoid')
    .isIn(['THC-A', 'CBD', 'Delta-8', 'Delta-9', 'THC-P'])
    .withMessage('Please select a valid cannabinoid'),
  body('strain')
    .optional()
    .isIn(['Sativa', 'Indica', 'Hybrid'])
    .withMessage('Please select a valid strain type')
];

// Category validation rules
const validateCategory = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Category name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('parent')
    .optional()
    .isMongoId()
    .withMessage('Please provide a valid parent category ID')
];

// Order validation rules (for guest orders with items in request body)
const validateOrder = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('items.*.productId')
    .isInt({ min: 1 })
    .withMessage('Please provide valid product IDs'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Item quantity must be at least 1'),
  body('billingAddress.firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing first name is required'),
  body('billingAddress.lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing last name is required'),
  body('billingAddress.address1')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Billing address is required'),
  body('billingAddress.city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing city is required'),
  body('billingAddress.state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing state is required'),
  body('billingAddress.zipCode')
    .trim()
    .isLength({ min: 5, max: 10 })
    .withMessage('Billing zip code is required'),
  body('shippingAddress.firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping first name is required'),
  body('shippingAddress.lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping last name is required'),
  body('shippingAddress.address1')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Shipping address is required'),
  body('shippingAddress.city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping city is required'),
  body('shippingAddress.state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping state is required'),
  body('shippingAddress.zipCode')
    .trim()
    .isLength({ min: 5, max: 10 })
    .withMessage('Shipping zip code is required')
];

// Cart-based order validation (for createOrder - gets items from user's cart)
const validateCartOrder = [
  body('billingAddress.firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing first name is required'),
  body('billingAddress.lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing last name is required'),
  body('billingAddress.address1')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Billing address is required'),
  body('billingAddress.city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing city is required'),
  body('billingAddress.state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Billing state is required'),
  body('billingAddress.zipCode')
    .trim()
    .isLength({ min: 5, max: 10 })
    .withMessage('Billing zip code is required'),
  body('shippingAddress.firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping first name is required'),
  body('shippingAddress.lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping last name is required'),
  body('shippingAddress.address1')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Shipping address is required'),
  body('shippingAddress.city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping city is required'),
  body('shippingAddress.state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Shipping state is required'),
  body('shippingAddress.zipCode')
    .trim()
    .isLength({ min: 5, max: 10 })
    .withMessage('Shipping zip code is required')
];

// Review validation rules
const validateReview = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Review title must be between 5 and 100 characters'),
  body('comment')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Review comment must be between 10 and 1000 characters')
];

// Contact form validation
const validateContact = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('phone')
    .optional()
    .trim()
    .isLength({ min: 10, max: 20 })
    .withMessage('Phone number must be between 10 and 20 characters'),
  body('subject')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Subject must be between 5 and 200 characters'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Message must be between 10 and 1000 characters'),
  body('inquiryType')
    .optional()
    .isIn(['general', 'product', 'order', 'shipping', 'return', 'wholesale', 'partnership'])
    .withMessage('Invalid inquiry type')
];

// Newsletter validation
const validateNewsletter = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
];

// Parameter validation for Sequelize integer IDs
const validateId = (field = 'id') => [
  param(field)
    .isInt({ min: 1 })
    .withMessage(`Please provide a valid ${field}`)
];

// Query validation
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

// Address validation
const validateAddress = [
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters'),
  body('address1')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Address line 1 is required and must be less than 100 characters'),
  body('address2')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Address line 2 must be less than 100 characters'),
  body('city')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('City is required and must be less than 50 characters'),
  body('state')
    .trim()
    .isLength({ min: 2, max: 2 })
    .withMessage('State must be a 2-character code'),
  body('zipCode')
    .trim()
    .matches(/^\d{5}(-\d{4})?$/)
    .withMessage('ZIP code must be in format 12345 or 12345-6789'),
  body('country')
    .optional()
    .trim()
    .isLength({ min: 2, max: 2 })
    .withMessage('Country must be a 2-character code')
];

// Cart item validation
const validateCartItem = [
  body('productId')
    .isInt({ min: 1 })
    .withMessage('Valid product ID is required'),
  body('quantity')
    .isInt({ min: 1, max: 100 })
    .withMessage('Quantity must be between 1 and 100'),
  body('variant')
    .optional()
    .isObject()
    .withMessage('Variant must be an object'),
  body('variant.size')
    .optional()
    .isString()
    .isLength({ min: 1, max: 20 })
    .withMessage('Variant size must be a string'),
  body('variant.color')
    .optional()
    .isString()
    .isLength({ min: 1, max: 20 })
    .withMessage('Variant color must be a string')
];

// Order status validation
const validateOrderStatus = [
  body('status')
    .isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
    .withMessage('Invalid order status'),
  body('trackingNumber')
    .optional()
    .isString()
    .isLength({ min: 1, max: 50 })
    .withMessage('Tracking number must be a string'),
  body('notes')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('Notes must be less than 500 characters')
];

// Change password validation
const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
];

// File upload validation
const validateFileUpload = [
  body('description')
    .optional()
    .isString()
    .isLength({ max: 200 })
    .withMessage('Description must be less than 200 characters')
];

// Bulk operations validation
const validateBulkOperation = [
  body('ids')
    .isArray({ min: 1 })
    .withMessage('IDs array is required'),
  body('ids.*')
    .isInt({ min: 1 })
    .withMessage('All IDs must be positive integers'),
  body('operation')
    .isIn(['activate', 'deactivate', 'delete', 'update'])
    .withMessage('Invalid operation type')
];

// Search validation
const validateSearch = [
  query('q')
    .optional()
    .isString()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters'),
  query('category')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Category must be a positive integer'),
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),
  query('sortBy')
    .optional()
    .isIn(['name', 'price', 'createdAt', 'averageRating', 'salesCount'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc')
];

module.exports = {
  validateRegister,
  validateLogin,
  validateUpdateProfile,
  validatePasswordReset,
  validateResetPassword,
  validateProduct,
  validateCategory,
  validateOrder,
  validateCartOrder,
  validateReview,
  validateContact,
  validateNewsletter,
  validateId,
  validatePagination,
  validateAddress,
  validateCartItem,
  validateOrderStatus,
  validateChangePassword,
  validateFileUpload,
  validateBulkOperation,
  validateSearch,
  handleValidationErrors
};
