/**
 * PM2 Ecosystem Configuration for Testing Environment
 * test.shopnirvanaorganics.com
 */

module.exports = {
  apps: [
    {
      // Main Application Server
      name: 'nirvana-test-main',
      script: './server/index.js',
      cwd: '/var/www/nirvana-test',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'testing',
        PORT: 5001,
        HTTPS_PORT: 5443,
        PM2_SERVE_PATH: './dist',
        PM2_SERVE_PORT: 3001,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/index.html'
      },
      env_testing: {
        NODE_ENV: 'testing',
        PORT: 5001,
        HTTPS_PORT: 5443
      },
      // Logging
      log_file: '/var/www/nirvana-test/logs/combined.log',
      out_file: '/var/www/nirvana-test/logs/out.log',
      error_file: '/var/www/nirvana-test/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads', '.git'],
      max_memory_restart: '512M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Advanced Options
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000,
      
      // Health Monitoring
      health_check_url: 'http://localhost:5001/api/health',
      health_check_grace_period: 3000
    },
    
    {
      // Admin Panel Server
      name: 'nirvana-test-admin',
      script: './server/admin-server.js',
      cwd: '/var/www/nirvana-test',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'testing',
        PORT: 5002,
        ADMIN_MODE: 'true'
      },
      env_testing: {
        NODE_ENV: 'testing',
        PORT: 5002,
        ADMIN_MODE: 'true'
      },
      // Logging
      log_file: '/var/www/nirvana-test/logs/admin-combined.log',
      out_file: '/var/www/nirvana-test/logs/admin-out.log',
      error_file: '/var/www/nirvana-test/logs/admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management
      watch: false,
      max_memory_restart: '256M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Health Monitoring
      health_check_url: 'http://localhost:5002/api/health',
      health_check_grace_period: 3000
    }
  ],

  // Deployment Configuration
  deploy: {
    testing: {
      user: 'nirvana',
      host: ['test.shopnirvanaorganics.com'],
      ref: 'origin/testing',
      repo: 'https://github.com/your-username/nirvana-organics.git',
      path: '/var/www/nirvana-test',
      'pre-deploy-local': '',
      'post-deploy': 'npm install --production && npm run build:test && npm run migrate:test && pm2 reload ecosystem.config.js --env testing',
      'pre-setup': 'mkdir -p /var/www/nirvana-test/logs /var/www/nirvana-test/uploads /var/www/nirvana-test/backups',
      'post-setup': 'ls -la /var/www/nirvana-test',
      env: {
        NODE_ENV: 'testing'
      }
    }
  }
};
