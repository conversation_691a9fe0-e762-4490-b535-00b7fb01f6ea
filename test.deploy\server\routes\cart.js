const express = require('express');
const router = express.Router();
const cartController = require('../controllers/cartController');
const { authenticate } = require('../middleware/auth');
const { body, param } = require('express-validator');

// Cart item validation
const validateCartItem = [
  body('productId')
    .isInt({ min: 1 })
    .withMessage('Please provide a valid product ID'),
  body('quantity')
    .isInt({ min: 1, max: 100 })
    .withMessage('Quantity must be between 1 and 100'),
  body('variant')
    .optional()
    .isObject()
    .withMessage('Variant must be an object')
];

const validateUpdateQuantity = [
  body('quantity')
    .isInt({ min: 0, max: 100 })
    .withMessage('Quantity must be between 0 and 100')
];

const validateCoupon = [
  body('couponCode')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Coupon code must be between 3 and 20 characters')
];

// @route   GET /api/cart
// @desc    Get user's cart
// @access  Private
router.get('/', authenticate, cartController.getCart);

// @route   GET /api/cart/summary
// @desc    Get cart summary (item count and total)
// @access  Private
router.get('/summary', authenticate, cartController.getCartSummary);

// @route   POST /api/cart/add
// @desc    Add item to cart
// @access  Private
router.post('/add', authenticate, validateCartItem, cartController.addToCart);

// @route   PUT /api/cart/items/:itemId
// @desc    Update cart item quantity
// @access  Private
router.put('/items/:itemId', authenticate, validateUpdateQuantity, cartController.updateCartItem);

// @route   PUT /api/cart/update
// @desc    Update cart item quantity by productId (alternative endpoint)
// @access  Private
router.put('/update', authenticate, cartController.updateCartItemByProduct);

// @route   DELETE /api/cart/items/:itemId
// @desc    Remove item from cart
// @access  Private
router.delete('/items/:itemId', authenticate, cartController.removeFromCart);

// @route   DELETE /api/cart/remove/:productId
// @desc    Remove item from cart by productId (alternative endpoint)
// @access  Private
router.delete('/remove/:productId', authenticate, cartController.removeFromCartByProduct);

// @route   DELETE /api/cart
// @desc    Clear entire cart
// @access  Private
router.delete('/', authenticate, cartController.clearCart);

// @route   POST /api/cart/coupon
// @desc    Apply coupon to cart
// @access  Private
router.post('/coupon', authenticate, validateCoupon, cartController.applyCoupon);

// @route   DELETE /api/cart/coupon
// @desc    Remove coupon from cart
// @access  Private
router.delete('/coupon', authenticate, cartController.removeCoupon);

module.exports = router;
