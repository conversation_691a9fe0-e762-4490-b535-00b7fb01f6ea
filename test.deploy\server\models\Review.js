const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Review = sequelize.define('Review', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'product_id',
    references: {
      model: 'products',
      key: 'id'
    }
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'order_id',
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 1,
      max: 5
    }
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  comment: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  helpful: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    defaultValue: 'pending'
  },
  adminResponse: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'admin_response'
  },
  adminResponseDate: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'admin_response_date'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  }
}, {
  tableName: 'reviews',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'product_id']
    }
  ],
  hooks: {
    beforeCreate: async (review) => {
      // Check if user purchased the product
      const Order = require('./Order');
      const order = await Order.findOne({
        where: {
          userId: review.userId,
          status: 'delivered'
        }
      });

      if (order) {
        const items = order.items || [];
        const purchasedProduct = items.find(item => item.productId === review.productId);
        if (purchasedProduct) {
          review.verified = true;
          review.orderId = order.id;
        }
      }
    },
    afterCreate: async (review) => {
      if (review.status === 'approved') {
        await Review.updateProductRating(review.productId);
      }
    },
    afterUpdate: async (review) => {
      if (review.changed('status') && review.status === 'approved') {
        await Review.updateProductRating(review.productId);
      }
    },
    afterDestroy: async (review) => {
      await Review.updateProductRating(review.productId);
    }
  }
});

// Instance methods
Review.prototype.markHelpful = async function() {
  this.helpful += 1;
  return await this.save();
};

Review.prototype.getRatingStars = function() {
  return '★'.repeat(this.rating) + '☆'.repeat(5 - this.rating);
};

// Static methods
Review.updateProductRating = async function(productId) {
  const Product = require('./Product');

  const stats = await Review.findAll({
    where: { productId, status: 'approved' },
    attributes: [
      [sequelize.fn('AVG', sequelize.col('rating')), 'averageRating'],
      [sequelize.fn('COUNT', sequelize.col('id')), 'reviewCount']
    ]
  });

  if (stats.length > 0 && stats[0].dataValues.reviewCount > 0) {
    await Product.update({
      averageRating: Math.round(stats[0].dataValues.averageRating * 10) / 10,
      reviewCount: stats[0].dataValues.reviewCount
    }, {
      where: { id: productId }
    });
  } else {
    await Product.update({
      averageRating: 0,
      reviewCount: 0
    }, {
      where: { id: productId }
    });
  }
};

Review.getProductStats = async function(productId) {
  return await Review.findAll({
    where: { productId, status: 'approved' },
    attributes: [
      'rating',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['rating'],
    order: [['rating', 'DESC']]
  });
};

module.exports = Review;
