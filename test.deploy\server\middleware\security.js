const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const { body, validationResult } = require('express-validator');
const xss = require('xss');
const DOMPurify = require('isomorphic-dompurify');
const helmet = require('helmet');

// Rate limiting configurations
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message: message || 'Too many requests, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      res.status(429).json({
        success: false,
        message: message || 'Too many requests, please try again later.',
        retryAfter: Math.round(windowMs / 1000)
      });
    }
  });
};

// General rate limiter
const generalLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100, // limit each IP to 100 requests per windowMs
  'Too many requests from this IP, please try again later.'
);

// Strict rate limiter for auth endpoints
const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // limit each IP to 5 requests per windowMs
  'Too many authentication attempts, please try again later.'
);

// Lenient rate limiter for admin authentication
const adminAuthLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  50, // limit each IP to 50 requests per windowMs (much more lenient for admin)
  'Too many admin authentication attempts, please try again later.'
);

// Smart auth limiter that applies different limits based on request
const smartAuthLimiter = (req, res, next) => {
  // Check if this is an OAuth endpoint or admin-related request
  const isOAuthEndpoint = req.path?.includes('/google/') ||
                         req.path?.includes('/facebook/') ||
                         req.path?.includes('/oauth/');

  const email = req.body?.email;
  const isAdminLogin = email?.includes('admin@') ||
                      email?.includes('no-reply@') ||
                      email === '<EMAIL>' ||
                      req.path?.includes('admin');

  // Debug logging
  console.log(`🔍 Smart Auth Limiter - Email: ${email}, IsAdmin: ${isAdminLogin}, Path: ${req.path}, IsOAuth: ${isOAuthEndpoint}`);

  if (isAdminLogin || isOAuthEndpoint) {
    // Skip rate limiting for admin users and OAuth endpoints completely
    console.log('📈 Skipping rate limiting for admin/OAuth');
    return next();
  } else {
    // Use strict limiter for regular users
    console.log('📉 Using strict limiter for regular user');
    return authLimiter(req, res, next);
  }
};

// API rate limiter
const apiLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  1000, // limit each IP to 1000 requests per windowMs
  'API rate limit exceeded, please try again later.'
);

// Payment rate limiter
const paymentLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  10, // limit each IP to 10 payment attempts per hour
  'Too many payment attempts, please try again later.'
);

// Speed limiter for repeated requests
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per 15 minutes, then...
  delayMs: () => 500, // begin adding 500ms of delay per request above 50
  maxDelayMs: 20000, // maximum delay of 20 seconds
});

// Input validation middleware
const validateInput = (validations) => {
  return async (req, res, next) => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    next();
  };
};

// XSS protection middleware
const sanitizeInput = (req, res, next) => {
  const sanitizeObject = (obj) => {
    if (typeof obj === 'string') {
      return DOMPurify.sanitize(xss(obj));
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          sanitized[key] = sanitizeObject(obj[key]);
        }
      }
      return sanitized;
    }
    
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }
  
  if (req.params) {
    req.params = sanitizeObject(req.params);
  }

  next();
};

// Common validation rules
const commonValidations = {
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
    
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
  name: body(['firstName', 'lastName'])
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Name must be between 1 and 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Name can only contain letters, spaces, hyphens, and apostrophes'),
    
  phone: body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
    
  price: body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
    
  quantity: body('quantity')
    .isInt({ min: 0 })
    .withMessage('Quantity must be a non-negative integer'),
    
  sku: body('sku')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('SKU must be between 1 and 50 characters')
    .matches(/^[A-Z0-9-_]+$/)
    .withMessage('SKU can only contain uppercase letters, numbers, hyphens, and underscores'),
    
  slug: body('slug')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Slug must be between 1 and 100 characters')
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Slug can only contain lowercase letters, numbers, and hyphens'),
    
  url: body(['url', 'image', 'website'])
    .optional()
    .isURL()
    .withMessage('Please provide a valid URL'),
    
  zipCode: body('zipCode')
    .matches(/^\d{5}(-\d{4})?$/)
    .withMessage('Please provide a valid ZIP code'),
    
  state: body('state')
    .isLength({ min: 2, max: 2 })
    .withMessage('State must be a 2-letter code')
    .matches(/^[A-Z]{2}$/)
    .withMessage('State must be uppercase letters only'),
    
  country: body('country')
    .isLength({ min: 2, max: 2 })
    .withMessage('Country must be a 2-letter code')
    .matches(/^[A-Z]{2}$/)
    .withMessage('Country must be uppercase letters only')
};

// Specific validation sets
const validationSets = {
  register: [
    commonValidations.email,
    commonValidations.password,
    commonValidations.name,
    commonValidations.phone
  ],
  
  login: [
    commonValidations.email,
    body('password').notEmpty().withMessage('Password is required')
  ],
  
  product: [
    body('name').trim().isLength({ min: 1, max: 200 }).withMessage('Product name is required and must be less than 200 characters'),
    body('description').trim().isLength({ min: 1 }).withMessage('Product description is required'),
    commonValidations.price,
    commonValidations.sku,
    commonValidations.slug,
    body('cannabinoid').isIn(['THC-A', 'CBD', 'Delta-8', 'Delta-9', 'THC-P']).withMessage('Invalid cannabinoid type'),
    body('strain').optional().isIn(['Sativa', 'Indica', 'Hybrid']).withMessage('Invalid strain type'),
    body('status').isIn(['active', 'draft', 'archived']).withMessage('Invalid status')
  ],
  
  address: [
    commonValidations.name,
    body('address1').trim().isLength({ min: 1, max: 100 }).withMessage('Address line 1 is required'),
    body('address2').optional().isLength({ max: 100 }).withMessage('Address line 2 must be less than 100 characters'),
    body('city').trim().isLength({ min: 1, max: 50 }).withMessage('City is required'),
    commonValidations.state,
    commonValidations.zipCode,
    commonValidations.country
  ],
  
  order: [
    body('items').isArray({ min: 1 }).withMessage('Order must contain at least one item'),
    body('items.*.productId').isInt({ min: 1 }).withMessage('Invalid product ID'),
    body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
    body('paymentMethod').isIn(['square', 'paypal', 'stripe']).withMessage('Invalid payment method')
  ]
};

// Security headers middleware
const securityHeaders = (req, res, next) => {
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // Content Security Policy
  res.setHeader('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://sandbox.web.squarecdn.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://connect.squareup.com https://connect.squareupsandbox.com",
    "frame-src 'self' https://js.squareup.com https://sandbox.web.squarecdn.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; '));
  
  next();
};

// IP whitelist middleware (for admin routes)
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    if (allowedIPs.length === 0) {
      return next(); // No IP restrictions if no IPs specified
    }
    
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    
    if (!allowedIPs.includes(clientIP)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied from this IP address'
      });
    }
    
    next();
  };
};

// Request size limiter
const requestSizeLimiter = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('content-length'));
    const maxSizeBytes = parseInt(maxSize) * 1024 * 1024; // Convert MB to bytes
    
    if (contentLength > maxSizeBytes) {
      return res.status(413).json({
        success: false,
        message: 'Request entity too large'
      });
    }
    
    next();
  };
};

// Enhanced Helmet configuration for production
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "https://js.squareup.com", "https://sandbox.web.squarecdn.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https://connect.squareup.com", "https://connect.squareupsandbox.com"],
      frameSrc: ["'self'", "https://js.squareup.com", "https://sandbox.web.squarecdn.com"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
};

// File upload validation middleware
const validateFileUpload = (req, res, next) => {
  if (!req.file && !req.files) {
    return next();
  }

  // Get allowed file types from environment
  const allowedTypesString = process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,webp,gif';
  const allowedExtensions = allowedTypesString.split(',').map(ext => ext.trim());
  const allowedTypes = allowedExtensions.map(ext => {
    switch(ext.toLowerCase()) {
      case 'jpg': case 'jpeg': return 'image/jpeg';
      case 'png': return 'image/png';
      case 'webp': return 'image/webp';
      case 'gif': return 'image/gif';
      case 'pdf': return 'application/pdf';
      case 'doc': return 'application/msword';
      case 'docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default: return `image/${ext}`;
    }
  });

  const maxSize = parseInt(process.env.MAX_FILE_SIZE) || (10 * 1024 * 1024); // Use env var or default to 10MB
  const maxSizeMB = Math.round(maxSize / (1024 * 1024));

  const files = req.files || [req.file];

  for (const file of files) {
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid file type',
        allowedTypes: allowedExtensions.map(ext => ext.toUpperCase())
      });
    }

    if (file.size > maxSize) {
      return res.status(400).json({
        success: false,
        error: 'File too large',
        maxSize: `${maxSizeMB}MB`
      });
    }
  }

  next();
};

// CORS configuration for production
const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
};

module.exports = {
  generalLimiter,
  authLimiter,
  adminAuthLimiter,
  smartAuthLimiter,
  apiLimiter,
  paymentLimiter,
  speedLimiter,
  validateInput,
  sanitizeInput,
  commonValidations,
  validationSets,
  securityHeaders,
  ipWhitelist,
  requestSizeLimiter,
  helmet: helmet(helmetConfig),
  validateFileUpload,
  corsOptions
};
