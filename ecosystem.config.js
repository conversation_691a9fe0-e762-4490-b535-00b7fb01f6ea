module.exports = {
  apps: [
    {
      name: 'nirvana-backend',
      script: './server/index.js',
      instances: 1, // Single instance for development
      exec_mode: 'fork',
      
      // Environment configuration
      env: {
        NODE_ENV: 'development',
        PORT: 5000
      },
      env_file: '.env',
      
      // Development settings
      watch: true,
      watch_delay: 1000,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'dist',
        '.git',
        '*.log'
      ],
      
      // Performance settings (lighter for development)
      max_memory_restart: '512M',
      
      // Logging configuration
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/dev-error.log',
      out_file: './logs/dev-out.log',
      log_file: './logs/dev-combined.log',
      merge_logs: true,
      time: true,
      
      // Restart configuration
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      restart_delay: 1000,
      
      // Development-specific settings
      kill_timeout: 3000,
      wait_ready: false,
      listen_timeout: 5000
    }
  ]
};
