#!/usr/bin/env node

/**
 * Enhanced Square Inventory Integration
 * 
 * This script provides comprehensive Square inventory management functionality
 * including inventory sync, product management, and automated updates.
 */

const { Client, Environment } = require('square');
require('dotenv').config();

// Initialize Square client
const squareClient = new Client({
  accessToken: process.env.SQUARE_ACCESS_TOKEN,
  environment: process.env.SQUARE_ENVIRONMENT === 'production' ? Environment.Production : Environment.Sandbox
});

const catalogApi = squareClient.catalogApi;
const inventoryApi = squareClient.inventoryApi;
const locationsApi = squareClient.locationsApi;

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(60));
    console.log(`🔄 ${msg}`);
    console.log('='.repeat(60));
  }
};

class EnhancedSquareInventory {
  constructor() {
    this.locations = [];
    this.products = [];
    this.inventoryItems = [];
  }

  // Initialize the service
  async initialize() {
    try {
      log.header('Initializing Square Inventory Service');
      
      // Get Square locations
      await this.getLocations();
      
      // Get catalog items
      await this.getCatalogItems();
      
      log.success('Square Inventory Service initialized successfully');
      return true;
    } catch (error) {
      log.error(`Initialization failed: ${error.message}`);
      return false;
    }
  }

  // Get all Square locations
  async getLocations() {
    try {
      log.info('Fetching Square locations...');
      const response = await locationsApi.listLocations();
      
      if (response.result && response.result.locations) {
        this.locations = response.result.locations.map(location => ({
          id: location.id,
          name: location.name,
          address: location.address,
          status: location.status,
          type: location.type
        }));
        
        log.success(`Found ${this.locations.length} locations`);
        this.locations.forEach(location => {
          log.info(`  • ${location.name} (${location.id})`);
        });
      }
      
      return this.locations;
    } catch (error) {
      log.error(`Failed to get locations: ${error.message}`);
      throw error;
    }
  }

  // Get all catalog items from Square
  async getCatalogItems() {
    try {
      log.info('Fetching Square catalog items...');
      const response = await catalogApi.listCatalog({
        types: 'ITEM,ITEM_VARIATION'
      });
      
      if (response.result && response.result.objects) {
        const items = response.result.objects.filter(obj => obj.type === 'ITEM');
        const variations = response.result.objects.filter(obj => obj.type === 'ITEM_VARIATION');
        
        this.products = items.map(item => {
          const itemVariations = variations.filter(v => v.itemVariationData.itemId === item.id);
          
          return {
            id: item.id,
            name: item.itemData.name,
            description: item.itemData.description,
            category: item.itemData.categoryId,
            variations: itemVariations.map(variation => ({
              id: variation.id,
              name: variation.itemVariationData.name,
              sku: variation.itemVariationData.sku,
              price: variation.itemVariationData.priceMoney,
              trackInventory: variation.itemVariationData.trackInventory
            }))
          };
        });
        
        log.success(`Found ${this.products.length} products with ${variations.length} variations`);
      }
      
      return this.products;
    } catch (error) {
      log.error(`Failed to get catalog items: ${error.message}`);
      throw error;
    }
  }

  // Get inventory counts for all items
  async getInventoryCounts() {
    try {
      log.info('Fetching inventory counts...');
      
      if (this.locations.length === 0) {
        await this.getLocations();
      }
      
      const inventoryData = [];
      
      for (const location of this.locations) {
        try {
          const response = await inventoryApi.batchRetrieveInventoryCounts({
            locationIds: [location.id]
          });
          
          if (response.result && response.result.counts) {
            response.result.counts.forEach(count => {
              inventoryData.push({
                catalogObjectId: count.catalogObjectId,
                locationId: count.locationId,
                locationName: location.name,
                quantity: count.quantity,
                state: count.state
              });
            });
          }
        } catch (error) {
          log.warning(`Failed to get inventory for location ${location.name}: ${error.message}`);
        }
      }
      
      this.inventoryItems = inventoryData;
      log.success(`Retrieved inventory data for ${inventoryData.length} items`);
      
      return inventoryData;
    } catch (error) {
      log.error(`Failed to get inventory counts: ${error.message}`);
      throw error;
    }
  }

  // Sync inventory with local database
  async syncInventoryWithDatabase(database) {
    try {
      log.header('Syncing Square Inventory with Local Database');
      
      // Get current inventory from Square
      await this.getInventoryCounts();
      
      let syncedCount = 0;
      let errorCount = 0;
      
      for (const inventoryItem of this.inventoryItems) {
        try {
          // Find the product variation in our local database
          const product = await database.Product.findOne({
            where: { squareItemId: inventoryItem.catalogObjectId }
          });
          
          if (product) {
            // Update local inventory
            await product.update({
              stockQuantity: parseInt(inventoryItem.quantity) || 0,
              lastSquareSync: new Date()
            });
            
            syncedCount++;
            log.info(`Synced ${product.name}: ${inventoryItem.quantity} units`);
          } else {
            log.warning(`Product not found in local database: ${inventoryItem.catalogObjectId}`);
          }
        } catch (error) {
          log.error(`Failed to sync item ${inventoryItem.catalogObjectId}: ${error.message}`);
          errorCount++;
        }
      }
      
      log.success(`Inventory sync completed: ${syncedCount} items synced, ${errorCount} errors`);
      
      return {
        synced: syncedCount,
        errors: errorCount,
        total: this.inventoryItems.length
      };
    } catch (error) {
      log.error(`Inventory sync failed: ${error.message}`);
      throw error;
    }
  }

  // Update Square inventory from local database
  async updateSquareInventory(database) {
    try {
      log.header('Updating Square Inventory from Local Database');
      
      // Get products that need inventory updates
      const products = await database.Product.findAll({
        where: {
          squareItemId: { [database.Sequelize.Op.not]: null },
          needsSquareSync: true
        }
      });
      
      let updatedCount = 0;
      let errorCount = 0;
      
      for (const product of products) {
        try {
          if (this.locations.length === 0) {
            await this.getLocations();
          }
          
          // Update inventory for each location
          for (const location of this.locations) {
            const adjustmentRequest = {
              idempotencyKey: `${product.id}-${location.id}-${Date.now()}`,
              changes: [{
                type: 'ADJUSTMENT',
                adjustment: {
                  catalogObjectId: product.squareItemId,
                  locationId: location.id,
                  quantity: product.stockQuantity.toString(),
                  fromState: 'IN_STOCK',
                  toState: 'IN_STOCK'
                }
              }]
            };
            
            await inventoryApi.batchChangeInventory(adjustmentRequest);
          }
          
          // Mark as synced
          await product.update({
            needsSquareSync: false,
            lastSquareSync: new Date()
          });
          
          updatedCount++;
          log.info(`Updated Square inventory for ${product.name}: ${product.stockQuantity} units`);
          
        } catch (error) {
          log.error(`Failed to update Square inventory for ${product.name}: ${error.message}`);
          errorCount++;
        }
      }
      
      log.success(`Square inventory update completed: ${updatedCount} items updated, ${errorCount} errors`);
      
      return {
        updated: updatedCount,
        errors: errorCount,
        total: products.length
      };
    } catch (error) {
      log.error(`Square inventory update failed: ${error.message}`);
      throw error;
    }
  }

  // Create a new product in Square catalog
  async createSquareProduct(productData) {
    try {
      log.info(`Creating Square product: ${productData.name}`);
      
      const catalogObject = {
        type: 'ITEM',
        id: `#${productData.name.replace(/\s+/g, '_').toLowerCase()}`,
        itemData: {
          name: productData.name,
          description: productData.description,
          variations: [{
            type: 'ITEM_VARIATION',
            id: `#${productData.name.replace(/\s+/g, '_').toLowerCase()}_variation`,
            itemVariationData: {
              name: 'Regular',
              sku: productData.sku,
              priceMoney: {
                amount: Math.round(productData.price * 100), // Convert to cents
                currency: 'USD'
              },
              trackInventory: true
            }
          }]
        }
      };
      
      const response = await catalogApi.upsertCatalogObject({
        idempotencyKey: `create-${productData.sku}-${Date.now()}`,
        object: catalogObject
      });
      
      if (response.result && response.result.catalogObject) {
        const createdItem = response.result.catalogObject;
        log.success(`Created Square product: ${createdItem.itemData.name} (${createdItem.id})`);
        
        return {
          squareItemId: createdItem.id,
          squareVariationId: createdItem.itemData.variations[0].id
        };
      }
      
      throw new Error('Failed to create Square product');
    } catch (error) {
      log.error(`Failed to create Square product: ${error.message}`);
      throw error;
    }
  }

  // Generate inventory report
  async generateInventoryReport() {
    try {
      log.header('Generating Square Inventory Report');
      
      await this.getInventoryCounts();
      
      const report = {
        timestamp: new Date().toISOString(),
        locations: this.locations.length,
        totalItems: this.inventoryItems.length,
        lowStockItems: this.inventoryItems.filter(item => parseInt(item.quantity) < 10),
        outOfStockItems: this.inventoryItems.filter(item => parseInt(item.quantity) === 0),
        summary: {}
      };
      
      // Group by location
      this.locations.forEach(location => {
        const locationItems = this.inventoryItems.filter(item => item.locationId === location.id);
        report.summary[location.name] = {
          totalItems: locationItems.length,
          totalQuantity: locationItems.reduce((sum, item) => sum + parseInt(item.quantity), 0),
          lowStock: locationItems.filter(item => parseInt(item.quantity) < 10).length,
          outOfStock: locationItems.filter(item => parseInt(item.quantity) === 0).length
        };
      });
      
      log.success('Inventory report generated');
      log.info(`Total items: ${report.totalItems}`);
      log.info(`Low stock items: ${report.lowStockItems.length}`);
      log.info(`Out of stock items: ${report.outOfStockItems.length}`);
      
      return report;
    } catch (error) {
      log.error(`Failed to generate inventory report: ${error.message}`);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const inventory = new EnhancedSquareInventory();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'init':
      await inventory.initialize();
      break;
      
    case 'sync':
      // This would require database connection
      log.info('Sync command requires database connection - use in application context');
      break;
      
    case 'report':
      const report = await inventory.generateInventoryReport();
      console.log(JSON.stringify(report, null, 2));
      break;
      
    case 'locations':
      await inventory.getLocations();
      break;
      
    case 'products':
      await inventory.getCatalogItems();
      break;
      
    default:
      console.log('Usage: node enhanced-square-inventory.js [init|sync|report|locations|products]');
      console.log('');
      console.log('Commands:');
      console.log('  init      - Initialize Square connection and fetch basic data');
      console.log('  sync      - Sync inventory with local database');
      console.log('  report    - Generate inventory report');
      console.log('  locations - List Square locations');
      console.log('  products  - List Square catalog items');
  }
}

// Run CLI if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    log.error(`Command failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = EnhancedSquareInventory;
