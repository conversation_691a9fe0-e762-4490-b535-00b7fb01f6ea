module.exports = {
  apps: [
    {
      name: 'nirvana-production',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend',
      instances: 'max',
      exec_mode: 'cluster',

      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      env_file: '.env',

      max_memory_restart: '1G',
      node_args: [
        '--max-old-space-size=1024',
        '--optimize-for-size'
      ],

      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/production-error.log',
      out_file: './logs/production-out.log',
      log_file: './logs/production-combined.log',
      merge_logs: true,
      time: true,

      autorestart: true,
      max_restarts: 10,
      min_uptime: '30s',
      restart_delay: 5000,

      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'dist',
        '.git'
      ],

      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,

      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,

      instance_var: 'INSTANCE_ID',
      combine_logs: true,

      exp_backoff_restart_delay: 100,
      source_map_support: true,
      stop_exit_codes: [0]
    }
  ]
};