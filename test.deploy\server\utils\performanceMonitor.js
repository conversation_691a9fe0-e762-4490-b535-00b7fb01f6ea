/**
 * Performance Monitoring Utilities for Hostinger Hosting
 * Monitors application performance, database queries, and system resources
 */

const os = require('os');
const fs = require('fs').promises;
const path = require('path');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requests: 0,
      errors: 0,
      responseTime: [],
      dbQueries: 0,
      dbQueryTime: [],
      memoryUsage: [],
      cpuUsage: [],
      emailsSent: 0,
      emailErrors: 0,
      emailSendTime: []
    };
    
    this.startTime = Date.now();
    this.isMonitoring = process.env.NODE_ENV === 'production';
    
    // Start monitoring if enabled
    if (this.isMonitoring) {
      this.startSystemMonitoring();
    }
  }

  /**
   * Start system resource monitoring
   */
  startSystemMonitoring() {
    // Monitor system resources every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Log performance summary every 5 minutes
    setInterval(() => {
      this.logPerformanceSummary();
    }, 300000);
  }

  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    try {
      // Memory usage
      const memUsage = process.memoryUsage();
      this.metrics.memoryUsage.push({
        timestamp: Date.now(),
        rss: memUsage.rss,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      });

      // CPU usage
      const cpuUsage = process.cpuUsage();
      this.metrics.cpuUsage.push({
        timestamp: Date.now(),
        user: cpuUsage.user,
        system: cpuUsage.system
      });

      // Keep only last 100 entries to prevent memory leaks
      if (this.metrics.memoryUsage.length > 100) {
        this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-100);
      }
      if (this.metrics.cpuUsage.length > 100) {
        this.metrics.cpuUsage = this.metrics.cpuUsage.slice(-100);
      }

    } catch (error) {
      console.error('Error collecting system metrics:', error);
    }
  }

  /**
   * Middleware to track request performance
   */
  trackRequest() {
    return (req, res, next) => {
      const startTime = Date.now();
      
      // Track request count
      this.metrics.requests++;

      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = (...args) => {
        const responseTime = Date.now() - startTime;
        
        // Track response time
        this.metrics.responseTime.push(responseTime);
        
        // Keep only last 1000 response times
        if (this.metrics.responseTime.length > 1000) {
          this.metrics.responseTime = this.metrics.responseTime.slice(-1000);
        }

        // Track errors
        if (res.statusCode >= 400) {
          this.metrics.errors++;
        }

        // Log slow requests (> 2 seconds)
        if (responseTime > 2000) {
          console.warn(`Slow request detected: ${req.method} ${req.path} - ${responseTime}ms`);
        }

        originalEnd.apply(res, args);
      };

      next();
    };
  }

  /**
   * Track database query performance
   */
  trackDbQuery(queryTime) {
    this.metrics.dbQueries++;
    this.metrics.dbQueryTime.push(queryTime);

    // Keep only last 1000 query times
    if (this.metrics.dbQueryTime.length > 1000) {
      this.metrics.dbQueryTime = this.metrics.dbQueryTime.slice(-1000);
    }

    // Log slow queries (> 1 second)
    if (queryTime > 1000) {
      console.warn(`Slow database query detected: ${queryTime}ms`);
    }
  }

  /**
   * Track Gmail email sending performance
   */
  trackEmailSent(sendTime, success = true) {
    if (success) {
      this.metrics.emailsSent++;
      this.metrics.emailSendTime.push(sendTime);

      // Keep only last 1000 send times
      if (this.metrics.emailSendTime.length > 1000) {
        this.metrics.emailSendTime = this.metrics.emailSendTime.slice(-1000);
      }

      // Log slow email sending (> 5 seconds)
      if (sendTime > 5000) {
        console.warn(`Slow Gmail email sending detected: ${sendTime}ms`);
      }
    } else {
      this.metrics.emailErrors++;
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics() {
    const now = Date.now();
    const uptime = now - this.startTime;

    // Calculate averages
    const avgResponseTime = this.metrics.responseTime.length > 0 
      ? this.metrics.responseTime.reduce((a, b) => a + b, 0) / this.metrics.responseTime.length 
      : 0;

    const avgDbQueryTime = this.metrics.dbQueryTime.length > 0
      ? this.metrics.dbQueryTime.reduce((a, b) => a + b, 0) / this.metrics.dbQueryTime.length
      : 0;

    const avgEmailSendTime = this.metrics.emailSendTime.length > 0
      ? this.metrics.emailSendTime.reduce((a, b) => a + b, 0) / this.metrics.emailSendTime.length
      : 0;

    // Get current memory usage
    const memUsage = process.memoryUsage();
    const systemMemory = {
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem()
    };

    return {
      uptime: uptime,
      uptimeFormatted: this.formatUptime(uptime),
      requests: {
        total: this.metrics.requests,
        errors: this.metrics.errors,
        errorRate: this.metrics.requests > 0 ? (this.metrics.errors / this.metrics.requests * 100).toFixed(2) : 0,
        avgResponseTime: Math.round(avgResponseTime)
      },
      database: {
        totalQueries: this.metrics.dbQueries,
        avgQueryTime: Math.round(avgDbQueryTime)
      },
      email: {
        totalSent: this.metrics.emailsSent,
        totalErrors: this.metrics.emailErrors,
        errorRate: this.metrics.emailsSent > 0 ? (this.metrics.emailErrors / (this.metrics.emailsSent + this.metrics.emailErrors) * 100).toFixed(2) : 0,
        avgSendTime: Math.round(avgEmailSendTime),
        provider: 'Gmail'
      },
      memory: {
        process: {
          rss: this.formatBytes(memUsage.rss),
          heapUsed: this.formatBytes(memUsage.heapUsed),
          heapTotal: this.formatBytes(memUsage.heapTotal),
          external: this.formatBytes(memUsage.external)
        },
        system: {
          total: this.formatBytes(systemMemory.total),
          free: this.formatBytes(systemMemory.free),
          used: this.formatBytes(systemMemory.used),
          usagePercent: ((systemMemory.used / systemMemory.total) * 100).toFixed(2)
        }
      },
      cpu: {
        loadAverage: os.loadavg(),
        cores: os.cpus().length
      }
    };
  }

  /**
   * Log performance summary
   */
  logPerformanceSummary() {
    const metrics = this.getMetrics();
    
    console.log('📊 Performance Summary:');
    console.log(`   Uptime: ${metrics.uptimeFormatted}`);
    console.log(`   Requests: ${metrics.requests.total} (${metrics.requests.errors} errors, ${metrics.requests.errorRate}% error rate)`);
    console.log(`   Avg Response Time: ${metrics.requests.avgResponseTime}ms`);
    console.log(`   DB Queries: ${metrics.database.totalQueries} (avg: ${metrics.database.avgQueryTime}ms)`);
    console.log(`   Memory Usage: ${metrics.memory.process.heapUsed} / ${metrics.memory.process.heapTotal}`);
    console.log(`   System Memory: ${metrics.memory.system.usagePercent}% used`);
  }

  /**
   * Check system health
   */
  getHealthStatus() {
    const metrics = this.getMetrics();
    const issues = [];

    // Check error rate
    if (parseFloat(metrics.requests.errorRate) > 5) {
      issues.push(`High error rate: ${metrics.requests.errorRate}%`);
    }

    // Check response time
    if (metrics.requests.avgResponseTime > 1000) {
      issues.push(`Slow response time: ${metrics.requests.avgResponseTime}ms`);
    }

    // Check memory usage
    if (parseFloat(metrics.memory.system.usagePercent) > 90) {
      issues.push(`High memory usage: ${metrics.memory.system.usagePercent}%`);
    }

    // Check database performance
    if (metrics.database.avgQueryTime > 500) {
      issues.push(`Slow database queries: ${metrics.database.avgQueryTime}ms`);
    }

    return {
      status: issues.length === 0 ? 'healthy' : 'warning',
      issues: issues,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Save performance report to file
   */
  async savePerformanceReport() {
    try {
      const metrics = this.getMetrics();
      const health = this.getHealthStatus();
      
      const report = {
        timestamp: new Date().toISOString(),
        metrics: metrics,
        health: health,
        environment: process.env.NODE_ENV,
        nodeVersion: process.version,
        platform: os.platform(),
        hostname: os.hostname()
      };

      const reportsDir = path.join(__dirname, '../../logs/performance');
      await fs.mkdir(reportsDir, { recursive: true });

      const filename = `performance-${new Date().toISOString().split('T')[0]}.json`;
      const filepath = path.join(reportsDir, filename);

      await fs.writeFile(filepath, JSON.stringify(report, null, 2));
      
      console.log(`📈 Performance report saved: ${filepath}`);
      return filepath;
    } catch (error) {
      console.error('Failed to save performance report:', error);
      return null;
    }
  }

  /**
   * Format uptime in human readable format
   */
  formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  /**
   * Reset metrics (useful for testing)
   */
  reset() {
    this.metrics = {
      requests: 0,
      errors: 0,
      responseTime: [],
      dbQueries: 0,
      dbQueryTime: [],
      memoryUsage: [],
      cpuUsage: []
    };
    this.startTime = Date.now();
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
