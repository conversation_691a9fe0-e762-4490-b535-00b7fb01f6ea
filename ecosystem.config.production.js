module.exports = {
  apps: [
    {
      name: 'nirvana-backend',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend',
      instances: 'max', // Use all available CPU cores
      exec_mode: 'cluster',
      
      // Environment configuration
      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      env_file: '.env',
      
      // Performance settings
      max_memory_restart: '1G',
      node_args: [
        '--max-old-space-size=1024',
        '--optimize-for-size'
      ],
      
      // Logging configuration
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      merge_logs: true,
      time: true,
      
      // Restart configuration
      autorestart: true,
      max_restarts: 10,
      min_uptime: '30s',
      restart_delay: 5000,
      
      // Monitoring
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'dist',
        '.git'
      ],
      
      // Health monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Process management
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      
      // Advanced settings for VPS
      instance_var: 'INSTANCE_ID',
      combine_logs: true,
      
      // Error handling
      exp_backoff_restart_delay: 100,
      
      // Source map support for better error tracking
      source_map_support: true,
      
      // Disable automatic restart on specific exit codes
      stop_exit_codes: [0]
    },
    
    // Optional: Separate admin process if needed
    {
      name: 'nirvana-admin',
      script: './server/admin-server.js',
      cwd: '/var/www/nirvana-backend',
      instances: 1,
      exec_mode: 'fork',
      
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      env_file: '.env',
      
      // Lower memory limit for admin interface
      max_memory_restart: '512M',
      node_args: '--max-old-space-size=512',
      
      // Logging
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/admin-error.log',
      out_file: './logs/admin-out.log',
      log_file: './logs/admin-combined.log',
      merge_logs: true,
      time: true,
      
      // Restart settings
      autorestart: true,
      max_restarts: 5,
      min_uptime: '30s',
      restart_delay: 3000,
      
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'dist',
        '.git'
      ],
      
      kill_timeout: 3000,
      wait_ready: true,
      listen_timeout: 8000
    }
  ],
  
  // Deployment configuration
  deploy: {
    production: {
      user: 'nirvana',
      host: ['your-vps-ip'],
      ref: 'origin/main',
      repo: 'https://github.com/your-username/nirvana-organics.git',
      path: '/var/www/nirvana-backend',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.production.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    }
  }
};
