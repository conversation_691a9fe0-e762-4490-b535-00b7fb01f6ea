#!/bin/bash

# Production Deployment Script for Nirvana Organics
# shopnirvanaorganics.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="nirvana-backend"
PROJECT_PATH="/var/www/nirvana-backend"
NGINX_CONFIG="/etc/nginx/sites-available/shopnirvanaorganics.com"
NGINX_ENABLED="/etc/nginx/sites-enabled/shopnirvanaorganics.com"
DOMAIN="shopnirvanaorganics.com"
WWW_DOMAIN="www.shopnirvanaorganics.com"
NODE_VERSION="18"

echo -e "${BLUE}🚀 Deploying Nirvana Organics Production Environment${NC}"
echo "=================================================="
echo -e "Domain: ${GREEN}$DOMAIN${NC}"
echo -e "Project Path: ${GREEN}$PROJECT_PATH${NC}"

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# System update and security hardening
system_update() {
    log_info "Updating system packages..."
    
    # Update package list
    apt update && apt upgrade -y
    
    # Install security updates
    apt install -y unattended-upgrades apt-listchanges
    
    # Configure automatic security updates
    echo 'Unattended-Upgrade::Automatic-Reboot "false";' >> /etc/apt/apt.conf.d/50unattended-upgrades
    
    log_info "System updated and security configured"
}

# Install system dependencies with production optimizations
install_dependencies() {
    log_info "Installing system dependencies..."
    
    # Install required packages
    apt install -y \
        curl wget git nginx mysql-server \
        build-essential python3-dev \
        ufw fail2ban logrotate \
        htop iotop nethogs \
        certbot python3-certbot-nginx \
        redis-server \
        supervisor
    
    # Install Node.js
    if ! command -v node &> /dev/null || [[ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt $NODE_VERSION ]]; then
        log_info "Installing Node.js $NODE_VERSION..."
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
        apt install -y nodejs
    fi
    
    # Install PM2 globally
    npm install -g pm2@latest
    
    # Install PM2 log rotate
    pm2 install pm2-logrotate
    
    log_info "System dependencies installed"
}

# Configure firewall
configure_firewall() {
    log_info "Configuring firewall..."
    
    # Reset UFW to defaults
    ufw --force reset
    
    # Default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH (adjust port if needed)
    ufw allow 22/tcp
    
    # Allow HTTP and HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Allow MySQL (only from localhost)
    ufw allow from 127.0.0.1 to any port 3306
    
    # Enable firewall
    ufw --force enable
    
    log_info "Firewall configured"
}

# Configure fail2ban
configure_fail2ban() {
    log_info "Configuring fail2ban..."
    
    # Create custom jail configuration
    cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 2

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 2

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
logpath = /var/log/nginx/access.log
maxretry = 2
EOF
    
    # Restart fail2ban
    systemctl restart fail2ban
    systemctl enable fail2ban
    
    log_info "Fail2ban configured"
}

# Create project user and directories
setup_project_structure() {
    log_info "Setting up project structure..."
    
    # Create nirvana user if it doesn't exist
    if ! id "nirvana" &>/dev/null; then
        useradd -m -s /bin/bash nirvana
        usermod -aG www-data nirvana
        usermod -aG sudo nirvana
    fi
    
    # Create project directories with proper permissions
    mkdir -p $PROJECT_PATH/{logs,uploads,backups,dist,ssl}
    
    # Set ownership and permissions
    chown -R nirvana:www-data $PROJECT_PATH
    chmod -R 755 $PROJECT_PATH
    chmod -R 775 $PROJECT_PATH/{logs,uploads,backups}
    chmod -R 700 $PROJECT_PATH/ssl
    
    # Create log directories
    mkdir -p /var/log/nirvana
    chown nirvana:www-data /var/log/nirvana
    chmod 755 /var/log/nirvana
    
    log_info "Project structure created"
}

# Setup database with production optimizations
setup_database() {
    log_info "Setting up production database..."
    
    # Secure MySQL installation
    mysql_secure_installation
    
    # Run database setup script
    if [ -f "./deploy/database-setup.sql" ]; then
        mysql -u root -p < ./deploy/database-setup.sql
        log_info "Database setup completed"
    else
        log_error "Database setup script not found!"
        exit 1
    fi
    
    # Configure MySQL for production
    cat >> /etc/mysql/mysql.conf.d/mysqld.cnf << 'EOF'

# Production optimizations
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 1
innodb_file_per_table = 1
innodb_flush_method = O_DIRECT
max_connections = 200
query_cache_type = 1
query_cache_size = 64M
slow_query_log = 1
long_query_time = 2
log_queries_not_using_indexes = 1
EOF
    
    # Restart MySQL
    systemctl restart mysql
    
    log_info "Database configured for production"
}

# Clone or update repository
setup_repository() {
    log_info "Setting up repository..."
    
    if [ -d "$PROJECT_PATH/.git" ]; then
        log_info "Repository exists, pulling latest changes..."
        cd $PROJECT_PATH
        sudo -u nirvana git pull origin main
    else
        log_info "Cloning repository..."
        sudo -u nirvana git clone -b main https://github.com/your-username/nirvana-organics.git $PROJECT_PATH
        cd $PROJECT_PATH
    fi
    
    # Set proper ownership
    chown -R nirvana:www-data $PROJECT_PATH
    
    log_info "Repository setup completed"
}

# Install application dependencies with production optimizations
install_app_dependencies() {
    log_info "Installing application dependencies..."
    
    cd $PROJECT_PATH
    
    # Install backend dependencies
    sudo -u nirvana npm ci --production --no-audit
    
    # Install frontend dependencies and build
    sudo -u nirvana npm run build:prod
    
    # Clear npm cache
    sudo -u nirvana npm cache clean --force
    
    log_info "Application dependencies installed"
}

# Setup environment configuration
setup_environment() {
    log_info "Setting up environment configuration..."
    
    # Copy environment file
    if [ -f "./deploy/.env.production" ]; then
        cp ./deploy/.env.production $PROJECT_PATH/.env
        chown nirvana:www-data $PROJECT_PATH/.env
        chmod 600 $PROJECT_PATH/.env
        log_info "Environment file copied"
    else
        log_error "Environment file not found!"
        exit 1
    fi
    
    # Copy PM2 ecosystem file
    if [ -f "./deploy/ecosystem.config.js" ]; then
        cp ./deploy/ecosystem.config.js $PROJECT_PATH/
        chown nirvana:www-data $PROJECT_PATH/ecosystem.config.js
        log_info "PM2 ecosystem file copied"
    fi
    
    # Copy backup scripts
    if [ -f "./deploy/backup-scripts.sh" ]; then
        cp ./deploy/backup-scripts.sh /usr/local/bin/nirvana-backup
        chmod +x /usr/local/bin/nirvana-backup
        log_info "Backup scripts installed"
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    cd $PROJECT_PATH
    sudo -u nirvana npm run migrate:prod
    
    log_info "Database migrations completed"
}

# Setup Nginx with production optimizations
setup_nginx() {
    log_info "Setting up Nginx configuration..."
    
    # Copy Nginx configuration
    if [ -f "./deploy/nginx.conf" ]; then
        cp ./deploy/nginx.conf $NGINX_CONFIG
        
        # Create symbolic link to enable site
        ln -sf $NGINX_CONFIG $NGINX_ENABLED
        
        # Remove default site
        rm -f /etc/nginx/sites-enabled/default
        
        # Test Nginx configuration
        nginx -t
        
        if [ $? -eq 0 ]; then
            log_info "Nginx configuration is valid"
        else
            log_error "Nginx configuration is invalid"
            exit 1
        fi
    else
        log_error "Nginx configuration file not found!"
        exit 1
    fi
    
    # Configure Nginx for production
    sed -i 's/# server_tokens off;/server_tokens off;/' /etc/nginx/nginx.conf
    
    log_info "Nginx configured for production"
}

# Setup SSL certificates
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    if [ -f "./deploy/ssl-setup.sh" ]; then
        chmod +x ./deploy/ssl-setup.sh
        ./deploy/ssl-setup.sh
    else
        log_error "SSL setup script not found!"
        exit 1
    fi
}

# Setup Redis
setup_redis() {
    log_info "Setting up Redis..."
    
    # Configure Redis for production
    sed -i 's/# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
    sed -i 's/# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf
    
    # Enable Redis
    systemctl enable redis-server
    systemctl start redis-server
    
    log_info "Redis configured"
}

# Start services
start_services() {
    log_info "Starting services..."
    
    cd $PROJECT_PATH
    
    # Start PM2 processes
    sudo -u nirvana pm2 start ecosystem.config.js --env production
    
    # Save PM2 configuration
    sudo -u nirvana pm2 save
    
    # Setup PM2 startup script
    pm2 startup systemd -u nirvana --hp /home/<USER>
    
    # Start and enable services
    systemctl start nginx
    systemctl enable nginx
    systemctl enable mysql
    systemctl enable redis-server
    
    log_info "Services started successfully"
}

# Setup monitoring and logging
setup_monitoring() {
    log_info "Setting up monitoring and logging..."
    
    # Setup log rotation for application logs
    cat > /etc/logrotate.d/nirvana-backend << 'EOF'
/var/www/nirvana-backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nirvana www-data
    postrotate
        pm2 reload all
    endscript
}
EOF
    
    # Setup system monitoring script
    cat > /usr/local/bin/monitor-nirvana.sh << 'EOF'
#!/bin/bash

# System monitoring script for Nirvana Organics
LOG_FILE="/var/log/nirvana/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check PM2 processes
if ! sudo -u nirvana pm2 list | grep -q "online"; then
    echo "[$DATE] WARNING: PM2 processes not running, restarting..." >> $LOG_FILE
    cd /var/www/nirvana-backend
    sudo -u nirvana pm2 restart all
fi

# Check disk space
DISK_USAGE=$(df /var/www/nirvana-backend | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 85 ]; then
    echo "[$DATE] WARNING: Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $MEMORY_USAGE -gt 90 ]; then
    echo "[$DATE] WARNING: Memory usage is ${MEMORY_USAGE}%" >> $LOG_FILE
fi

# Check services
for service in nginx mysql redis-server; do
    if ! systemctl is-active --quiet $service; then
        echo "[$DATE] ERROR: $service is not running" >> $LOG_FILE
        systemctl restart $service
    fi
done
EOF
    
    chmod +x /usr/local/bin/monitor-nirvana.sh
    
    # Add monitoring to cron
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/monitor-nirvana.sh") | crontab -
    
    # Setup backup cron job
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/nirvana-backup full >> /var/log/nirvana/backup.log 2>&1") | crontab -
    
    log_info "Monitoring and logging setup completed"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    local verification_failed=0
    
    # Check services
    for service in nginx mysql redis-server; do
        if systemctl is-active --quiet $service; then
            log_info "✅ $service is running"
        else
            log_error "❌ $service is not running"
            verification_failed=1
        fi
    done
    
    # Check PM2 processes
    cd $PROJECT_PATH
    if sudo -u nirvana pm2 list | grep -q "online"; then
        log_info "✅ PM2 processes are running"
    else
        log_error "❌ PM2 processes are not running"
        verification_failed=1
    fi
    
    # Test API endpoints
    sleep 10
    if curl -f -s https://$DOMAIN/api/health > /dev/null; then
        log_info "✅ API is responding"
    else
        log_warning "⚠️ API is not responding (may need time to start)"
    fi
    
    # Test SSL
    if curl -f -s https://$DOMAIN > /dev/null; then
        log_info "✅ HTTPS is working"
    else
        log_warning "⚠️ HTTPS is not working"
    fi
    
    return $verification_failed
}

# Main deployment function
main() {
    log_info "Starting production deployment process..."
    
    check_root
    system_update
    install_dependencies
    configure_firewall
    configure_fail2ban
    setup_project_structure
    setup_database
    setup_repository
    install_app_dependencies
    setup_environment
    run_migrations
    setup_nginx
    setup_ssl
    setup_redis
    start_services
    setup_monitoring
    
    if verify_deployment; then
        echo ""
        echo -e "${GREEN}🎉 Production Deployment Completed Successfully!${NC}"
        echo "=================================================="
        echo -e "Production URL: ${BLUE}https://$DOMAIN${NC}"
        echo -e "Admin Panel: ${BLUE}https://$DOMAIN/admin${NC}"
        echo -e "API Health: ${BLUE}https://$DOMAIN/api/health${NC}"
        echo ""
        echo -e "${YELLOW}Important Next Steps:${NC}"
        echo "1. Update DNS records to point $DOMAIN to this server"
        echo "2. Test all functionality thoroughly"
        echo "3. Monitor logs: tail -f $PROJECT_PATH/logs/combined.log"
        echo "4. Check PM2 status: pm2 status"
        echo "5. Run backup test: /usr/local/bin/nirvana-backup quick"
        echo "6. Monitor system: /usr/local/bin/monitor-nirvana.sh"
        echo ""
        echo -e "${GREEN}Production environment is ready! 🚀${NC}"
    else
        log_error "Deployment completed with errors. Please check the logs."
        exit 1
    fi
}

# Run main function
main "$@"
