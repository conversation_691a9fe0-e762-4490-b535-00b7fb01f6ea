#!/usr/bin/env node

/**
 * Validation Script for Unified Deployment Fixes
 * 
 * This script validates that all the reported issues have been resolved:
 * 1. Security headers moved from HTML meta tags to Nginx HTTP headers
 * 2. Asset path issues fixed for admin panel
 * 3. Unified build configuration working correctly
 */

const fs = require('fs');
const path = require('path');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(60));
    console.log(`🔍 ${msg}`);
    console.log('='.repeat(60));
  }
};

function validateSecurityHeadersFix() {
  log.header('Validating Security Headers Fix');
  
  let allPassed = true;
  
  // Check admin.html for removed security headers
  const adminHtmlPath = 'dist/admin/admin.html';
  if (fs.existsSync(adminHtmlPath)) {
    const content = fs.readFileSync(adminHtmlPath, 'utf8');
    
    // Check that problematic meta tags are removed
    const problematicHeaders = [
      'X-Frame-Options',
      'Content-Security-Policy',
      'frame-ancestors'
    ];
    
    let foundProblematicHeaders = [];
    problematicHeaders.forEach(header => {
      if (content.includes(`http-equiv="${header}"`)) {
        foundProblematicHeaders.push(header);
      }
    });
    
    if (foundProblematicHeaders.length === 0) {
      log.success('Security headers removed from admin.html meta tags');
    } else {
      log.error(`Found problematic security headers in admin.html: ${foundProblematicHeaders.join(', ')}`);
      allPassed = false;
    }
    
    // Check for security comment
    if (content.includes('Security headers are now handled by Nginx')) {
      log.success('Security headers comment found in admin.html');
    } else {
      log.warning('Security headers comment not found in admin.html');
    }
  } else {
    log.error('admin.html not found');
    allPassed = false;
  }
  
  // Check Nginx configuration for proper security headers
  const nginxConfigPath = 'deployment/nginx/nirvana-organics.conf';
  if (fs.existsSync(nginxConfigPath)) {
    const content = fs.readFileSync(nginxConfigPath, 'utf8');
    
    // Check for admin-specific security headers
    if (content.includes('add_header X-Frame-Options "DENY"') && 
        content.includes('add_header Content-Security-Policy') &&
        content.includes('frame-ancestors \'none\'')) {
      log.success('Admin-specific security headers found in Nginx configuration');
    } else {
      log.error('Admin-specific security headers missing from Nginx configuration');
      allPassed = false;
    }
    
    // Check for unified server structure (no separate admin subdomain)
    if (!content.includes('admin.yourdomain.com')) {
      log.success('Unified server configuration (no separate admin subdomain)');
    } else {
      log.error('Old separate admin subdomain configuration still present');
      allPassed = false;
    }
  } else {
    log.error('Nginx configuration not found');
    allPassed = false;
  }
  
  return allPassed;
}

function validateAssetPathsFix() {
  log.header('Validating Asset Paths Fix');
  
  let allPassed = true;
  
  // Check admin.html for correct asset paths
  const adminHtmlPath = 'dist/admin/admin.html';
  if (fs.existsSync(adminHtmlPath)) {
    const content = fs.readFileSync(adminHtmlPath, 'utf8');
    
    // Check for correct asset paths
    const assetReferences = [
      '/admin/assets/admin-IQ9dKFRc.js',
      '/admin/assets/admin-DxwklLpA.css',
      '/admin/assets/vendor-DavUf6mE.js'
    ];
    
    let foundAssets = [];
    assetReferences.forEach(asset => {
      if (content.includes(asset)) {
        foundAssets.push(asset);
      }
    });
    
    if (foundAssets.length >= 2) { // At least main JS and CSS should be found
      log.success(`Correct asset paths found in admin.html: ${foundAssets.length} assets`);
    } else {
      log.error('Correct asset paths not found in admin.html');
      allPassed = false;
    }
    
    // Check for base href
    if (content.includes('<base href="/admin/">')) {
      log.success('Base href correctly set for admin panel');
    } else {
      log.error('Base href not correctly set for admin panel');
      allPassed = false;
    }
  } else {
    log.error('admin.html not found');
    allPassed = false;
  }
  
  // Check that admin assets actually exist
  const adminAssetsPath = 'dist/admin/assets';
  if (fs.existsSync(adminAssetsPath)) {
    const assets = fs.readdirSync(adminAssetsPath);
    const jsAssets = assets.filter(asset => asset.endsWith('.js'));
    const cssAssets = assets.filter(asset => asset.endsWith('.css'));
    
    if (jsAssets.length > 0 && cssAssets.length > 0) {
      log.success(`Admin assets exist: ${jsAssets.length} JS files, ${cssAssets.length} CSS files`);
    } else {
      log.error('Admin assets missing or incomplete');
      allPassed = false;
    }
  } else {
    log.error('Admin assets directory not found');
    allPassed = false;
  }
  
  return allPassed;
}

function validateUnifiedBuildConfiguration() {
  log.header('Validating Unified Build Configuration');
  
  let allPassed = true;
  
  // Check Vite unified config
  const viteConfigPath = 'vite.unified.config.ts';
  if (fs.existsSync(viteConfigPath)) {
    const content = fs.readFileSync(viteConfigPath, 'utf8');
    
    // Check for base path configuration
    if (content.includes("base: isAdminBuild ? '/admin/' : '/'")) {
      log.success('Base path configuration found in Vite config');
    } else {
      log.error('Base path configuration missing from Vite config');
      allPassed = false;
    }
    
    // Check for admin build target
    if (content.includes("process.env.VITE_BUILD_TARGET === 'admin'")) {
      log.success('Admin build target configuration found');
    } else {
      log.error('Admin build target configuration missing');
      allPassed = false;
    }
  } else {
    log.error('Vite unified config not found');
    allPassed = false;
  }
  
  // Check build script
  const buildScriptPath = 'scripts/build-unified.js';
  if (fs.existsSync(buildScriptPath)) {
    const content = fs.readFileSync(buildScriptPath, 'utf8');
    
    // Check for asset path fixing function
    if (content.includes('updateHtmlForAdmin') && 
        content.includes('/admin/assets/')) {
      log.success('Asset path fixing function found in build script');
    } else {
      log.error('Asset path fixing function missing or incorrect');
      allPassed = false;
    }
  } else {
    log.error('Build unified script not found');
    allPassed = false;
  }
  
  return allPassed;
}

function validateDeploymentPackage() {
  log.header('Validating Deployment Package');
  
  let allPassed = true;
  
  // Check deployment structure
  const deploymentPath = 'deployment';
  const requiredFiles = [
    'frontend/index.html',
    'frontend/admin/admin.html',
    'nginx/nirvana-organics.conf',
    'server/index.js',
    'deploy.sh'
  ];
  
  requiredFiles.forEach(file => {
    const filePath = path.join(deploymentPath, file);
    if (fs.existsSync(filePath)) {
      log.success(`Deployment file exists: ${file}`);
    } else {
      log.error(`Deployment file missing: ${file}`);
      allPassed = false;
    }
  });
  
  // Check that deployment frontend has unified structure
  const deploymentFrontendPath = 'deployment/frontend';
  if (fs.existsSync(deploymentFrontendPath)) {
    const adminPath = path.join(deploymentFrontendPath, 'admin');
    if (fs.existsSync(adminPath)) {
      log.success('Deployment package has unified frontend structure');
    } else {
      log.error('Deployment package missing admin panel in unified structure');
      allPassed = false;
    }
  }
  
  return allPassed;
}

async function runValidation() {
  try {
    log.header('Starting Fix Validation');
    
    const results = {
      securityHeaders: validateSecurityHeadersFix(),
      assetPaths: validateAssetPathsFix(),
      buildConfig: validateUnifiedBuildConfiguration(),
      deploymentPackage: validateDeploymentPackage()
    };
    
    // Summary
    log.header('Validation Summary');
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log.success('🎉 All fixes validated successfully!');
      log.info('✅ Security headers moved to Nginx HTTP headers');
      log.info('✅ Asset path issues resolved for admin panel');
      log.info('✅ Unified build configuration working correctly');
      log.info('✅ Deployment package ready for production');
      
      log.info('\n📋 Next Steps:');
      log.info('1. Test the applications in browser at:');
      log.info('   - Main frontend: http://localhost:8080/');
      log.info('   - Admin panel: http://localhost:8080/admin/');
      log.info('2. Deploy to VPS using the deployment package');
      log.info('3. Configure domain and SSL certificates');
      
    } else {
      log.error('❌ Some validation checks failed:');
      Object.entries(results).forEach(([check, passed]) => {
        if (!passed) {
          log.error(`  - ${check} validation failed`);
        }
      });
      process.exit(1);
    }
    
  } catch (error) {
    log.error(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  runValidation();
}

module.exports = { runValidation };
