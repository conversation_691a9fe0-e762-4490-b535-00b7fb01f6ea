'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create data_environments table
    await queryInterface.createTable('data_environments', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      mode: {
        type: Sequelize.ENUM('mock', 'real'),
        allowNull: false,
        defaultValue: 'real'
      },
      session_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      last_switched: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{}'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Create mock_data table
    await queryInterface.createTable('mock_data', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      entity_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      entity_id: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      mock_id: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true
      },
      data: {
        type: Sequelize.JSON,
        allowNull: false
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '[]'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{}'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for data_environments table
    await queryInterface.addIndex('data_environments', {
      fields: ['user_id', 'session_id'],
      unique: true,
      name: 'idx_data_environments_user_session'
    });

    await queryInterface.addIndex('data_environments', {
      fields: ['mode'],
      name: 'idx_data_environments_mode'
    });

    await queryInterface.addIndex('data_environments', {
      fields: ['is_active'],
      name: 'idx_data_environments_active'
    });

    // Add indexes for mock_data table
    await queryInterface.addIndex('mock_data', {
      fields: ['entity_type'],
      name: 'idx_mock_data_entity_type'
    });

    await queryInterface.addIndex('mock_data', {
      fields: ['created_by'],
      name: 'idx_mock_data_created_by'
    });

    await queryInterface.addIndex('mock_data', {
      fields: ['is_active'],
      name: 'idx_mock_data_active'
    });

    await queryInterface.addIndex('mock_data', {
      fields: ['entity_type', 'is_active'],
      name: 'idx_mock_data_entity_active'
    });

    await queryInterface.addIndex('mock_data', {
      fields: ['mock_id'],
      unique: true,
      name: 'idx_mock_data_mock_id'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex('data_environments', 'idx_data_environments_user_session');
    await queryInterface.removeIndex('data_environments', 'idx_data_environments_mode');
    await queryInterface.removeIndex('data_environments', 'idx_data_environments_active');
    
    await queryInterface.removeIndex('mock_data', 'idx_mock_data_entity_type');
    await queryInterface.removeIndex('mock_data', 'idx_mock_data_created_by');
    await queryInterface.removeIndex('mock_data', 'idx_mock_data_active');
    await queryInterface.removeIndex('mock_data', 'idx_mock_data_entity_active');
    await queryInterface.removeIndex('mock_data', 'idx_mock_data_mock_id');

    // Drop tables
    await queryInterface.dropTable('mock_data');
    await queryInterface.dropTable('data_environments');
  }
};
