const express = require('express');
const router = express.Router();
const orderVerificationController = require('../controllers/orderVerificationController');
const { authenticate, requireAdmin, requireManagerOrAdmin, auditAdminAction } = require('../middleware/auth');
const { validateId, validatePagination } = require('../middleware/validation');
const { body } = require('express-validator');

// Validation middleware for verification
const validateVerification = [
  body('verificationMethod')
    .isIn(['phone', 'email', 'whatsapp', 'manual'])
    .withMessage('Invalid verification method'),
  body('approved')
    .isBoolean()
    .withMessage('Approved must be a boolean'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be less than 500 characters')
];

const validateContact = [
  body('method')
    .isIn(['whatsapp', 'email', 'phone'])
    .withMessage('Invalid contact method'),
  body('message')
    .isLength({ min: 10, max: 500 })
    .withMessage('Message must be between 10 and 500 characters'),
  body('phoneNumber')
    .optional()
    .isMobilePhone()
    .withMessage('Invalid phone number format')
];

const validateShippingLabel = [
  body('shippingMethod')
    .optional()
    .isIn(['GROUND_ADVANTAGE', 'PRIORITY', 'PRIORITY_EXPRESS'])
    .withMessage('Invalid shipping method'),
  body('weight')
    .optional()
    .isFloat({ min: 0.1, max: 70 })
    .withMessage('Weight must be between 0.1 and 70 pounds'),
  body('dimensions')
    .optional()
    .isObject()
    .withMessage('Dimensions must be an object with length, width, height')
];

// @route   GET /api/admin/order-verification/pending
// @desc    Get orders pending verification
// @access  Private (Admin/Manager)
router.get('/pending', 
  authenticate, 
  requireManagerOrAdmin, 
  validatePagination,
  auditAdminAction('VIEW_PENDING_ORDERS', 'ORDER'),
  orderVerificationController.getPendingVerificationOrders
);

// @route   PUT /api/admin/order-verification/:orderId/verify
// @desc    Verify customer details for an order
// @access  Private (Admin/Manager)
router.put('/:orderId/verify',
  authenticate,
  requireManagerOrAdmin,
  validateId('orderId'),
  validateVerification,
  auditAdminAction('VERIFY_ORDER', 'ORDER'),
  orderVerificationController.verifyCustomerDetails
);

// @route   POST /api/admin/order-verification/:orderId/contact
// @desc    Contact customer for verification
// @access  Private (Admin/Manager)
router.post('/:orderId/contact',
  authenticate,
  requireManagerOrAdmin,
  validateId('orderId'),
  validateContact,
  auditAdminAction('CONTACT_CUSTOMER', 'ORDER'),
  orderVerificationController.contactCustomerForVerification
);

// @route   POST /api/admin/order-verification/:orderId/shipping-label
// @desc    Generate shipping label for verified order
// @access  Private (Admin/Manager)
router.post('/:orderId/shipping-label',
  authenticate,
  requireManagerOrAdmin,
  validateId('orderId'),
  validateShippingLabel,
  auditAdminAction('GENERATE_SHIPPING_LABEL', 'ORDER'),
  orderVerificationController.generateShippingLabel
);

module.exports = router;
