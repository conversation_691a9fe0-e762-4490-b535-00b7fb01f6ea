const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Banner = sequelize.define('Banner', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: true
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('info', 'success', 'warning', 'error', 'promotion'),
    allowNull: false,
    defaultValue: 'info'
  },
  backgroundColor: {
    type: DataTypes.STRING,
    defaultValue: '#3B82F6'
  },
  textColor: {
    type: DataTypes.STRING,
    defaultValue: '#FFFFFF'
  },
  linkUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  linkText: {
    type: DataTypes.STRING,
    allowNull: true
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 1
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  startDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  targetPages: {
    type: DataTypes.JSON,
    defaultValue: ['all']
  },
  targetUserSegments: {
    type: DataTypes.JSON,
    defaultValue: ['all']
  },
  displayRules: {
    type: DataTypes.JSON,
    defaultValue: {
      showOnce: false,
      dismissible: true,
      autoHide: false,
      autoHideDelay: 5000
    }
  },
  analytics: {
    type: DataTypes.JSON,
    defaultValue: {
      views: 0,
      clicks: 0,
      dismissals: 0
    }
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  }
}, {
  tableName: 'banners',
  timestamps: true,
  indexes: [
    {
      fields: ['isActive']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['expiresAt']
    },
    {
      fields: ['createdBy']
    }
  ]
});

// Associations
Banner.associate = (models) => {
  Banner.belongsTo(models.User, {
    foreignKey: 'createdBy',
    as: 'creator'
  });
  
  Banner.belongsTo(models.User, {
    foreignKey: 'updatedBy',
    as: 'updater'
  });
};

module.exports = Banner;
