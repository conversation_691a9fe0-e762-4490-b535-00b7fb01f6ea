const jwt = require('jsonwebtoken');
const authService = require('../services/authService');

/**
 * Authentication middleware to verify JWT tokens
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database
    const userId = decoded.userId || decoded.id; // Support both formats
    const user = await authService.getUserById(userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token. User not found.'
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated.'
      });
    }

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired.'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Authentication error.',
      error: error.message
    });
  }
};

/**
 * Authorization middleware to check user roles (legacy support)
 */
const authorize = (...roles) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    try {
      // Get user's role information
      const role = await req.user.getRole();
      if (!role) {
        return res.status(403).json({
          success: false,
          message: 'User role not found.'
        });
      }

      // Check if user's role is in the allowed roles
      if (!roles.includes(role.name)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions.'
        });
      }

      next();
    } catch (error) {
      console.error('Authorization error:', error);
      return res.status(500).json({
        success: false,
        message: 'Authorization error.'
      });
    }
  };
};

/**
 * Permission-based authorization middleware
 * @param {string} category - Permission category (e.g., 'products', 'users')
 * @param {string} action - Permission action (e.g., 'view', 'create', 'edit', 'delete')
 */
const requirePermission = (category, action) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    try {
      const hasPermission = await req.user.hasPermission(category, action);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `Insufficient permissions. Required: ${category}.${action}`
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Permission check failed.'
      });
    }
  };
};

/**
 * Optional authentication middleware (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await authService.getUserById(decoded.id);

    if (user && user.isActive) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Continue without authentication on error
    next();
  }
};

/**
 * Age verification middleware for cannabis products
 */
const verifyAge = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required for age verification.'
    });
  }

  if (!req.user.dateOfBirth) {
    return res.status(400).json({
      success: false,
      message: 'Date of birth required for age verification.'
    });
  }

  const age = Math.floor((Date.now() - new Date(req.user.dateOfBirth)) / (365.25 * 24 * 60 * 60 * 1000));
  if (age < 21) {
    return res.status(403).json({
      success: false,
      message: 'You must be at least 21 years old to access this content.'
    });
  }

  next();
};

/**
 * Admin only middleware
 */
const requireAdmin = authorize('admin');

/**
 * Manager or Admin middleware
 */
const requireManagerOrAdmin = authorize('manager', 'admin');

/**
 * Customer or higher middleware
 */
const requireCustomerOrAdmin = authorize('customer', 'manager', 'admin');

/**
 * Specific permission middleware functions for common operations
 */
const requireProductManagement = requirePermission('products', 'edit');
const requireProductView = requirePermission('products', 'view');
const requireUserManagement = requirePermission('users', 'create');
const requireUserView = requirePermission('users', 'view');
const requireOrderManagement = requirePermission('orders', 'manage');
const requireSystemAccess = requirePermission('system', 'accessSettings');
const requireAuditAccess = requirePermission('system', 'viewAuditLogs');

/**
 * Middleware to check if user can invite managers
 */
const requireManagerInvitation = requirePermission('users', 'inviteManagers');

/**
 * Middleware to check if user can manage roles
 */
const requireRoleManagement = requirePermission('system', 'manageRoles');

/**
 * Audit logging middleware for admin actions
 */
const auditAdminAction = (action, entityType) => {
  return async (req, res, next) => {
    // Store audit info in request for later use
    req.auditInfo = {
      action,
      entityType,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID
    };

    // Override res.json to log audit after successful response
    const originalJson = res.json;
    res.json = function(data) {
      // Only log if the response was successful
      if (data && data.success !== false && res.statusCode < 400) {
        // Log audit action asynchronously
        setImmediate(async () => {
          try {
            // For now, just log to console since AuditLog model doesn't exist
            console.log('🔍 AUDIT LOG:', {
              userId: req.user?.id,
              action,
              entityType,
              entityId: data.data?.id || req.params.id || req.params.userId,
              ipAddress: req.auditInfo.ipAddress,
              userAgent: req.auditInfo.userAgent,
              sessionId: req.auditInfo.sessionId,
              endpoint: req.originalUrl,
              method: req.method,
              responseStatus: res.statusCode,
              timestamp: new Date().toISOString()
            });
          } catch (error) {
            console.error('Audit logging error:', error);
          }
        });
      }
      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Helper function to determine severity based on action
 */
const getSeverityForAction = (action) => {
  const highSeverityActions = [
    'DELETE_PRODUCT', 'DELETE_USER', 'UPDATE_USER_ROLE',
    'INVITE_MANAGER', 'BULK_UPDATE_PRODUCTS', 'DELETE_BANNER'
  ];
  const mediumSeverityActions = [
    'CREATE_PRODUCT', 'UPDATE_PRODUCT', 'CREATE_USER',
    'UPDATE_CUSTOMER', 'SEND_PROMOTIONAL_EMAIL'
  ];

  if (highSeverityActions.includes(action)) return 'high';
  if (mediumSeverityActions.includes(action)) return 'medium';
  return 'low';
};

module.exports = {
  authenticate,
  authorize,
  requirePermission,
  optionalAuth,
  verifyAge,
  requireAdmin,
  requireManagerOrAdmin,
  requireCustomerOrAdmin,
  requireProductManagement,
  requireProductView,
  requireUserManagement,
  requireUserView,
  requireOrderManagement,
  requireSystemAccess,
  requireAuditAccess,
  requireManagerInvitation,
  requireRoleManagement,
  auditAdminAction,
  getSeverityForAction
};
