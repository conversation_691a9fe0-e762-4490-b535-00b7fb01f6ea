const axios = require('axios');
const xml2js = require('xml2js');
require('dotenv').config();

class USPSService {
  constructor() {
    this.userId = process.env.USPS_USER_ID;
    this.apiUrl = process.env.USPS_API_URL || 'https://secure.shippingapis.com/ShippingAPI.dll';
    this.parser = new xml2js.Parser({ explicitArray: false });
    this.builder = new xml2js.Builder({ rootName: 'TrackRequest' });
  }

  /**
   * Validate USPS configuration
   */
  validateConfiguration() {
    if (!this.userId) {
      throw new Error('USPS_USER_ID is required for USPS API integration');
    }
    return true;
  }

  /**
   * Track a package using USPS tracking number
   */
  async trackPackage(trackingNumber) {
    try {
      this.validateConfiguration();

      const trackRequest = {
        $: { USERID: this.userId },
        TrackID: {
          $: { ID: trackingNumber }
        }
      };

      const xmlRequest = this.builder.buildObject(trackRequest);
      
      const response = await axios.get(this.apiUrl, {
        params: {
          API: 'TrackV2',
          XML: xmlRequest
        },
        timeout: 10000
      });

      const result = await this.parser.parseStringPromise(response.data);
      
      if (result.TrackResponse && result.TrackResponse.TrackInfo) {
        const trackInfo = result.TrackResponse.TrackInfo;
        
        return {
          success: true,
          trackingNumber: trackingNumber,
          status: this.parseTrackingStatus(trackInfo.TrackSummary),
          statusHistory: this.parseTrackingHistory(trackInfo.TrackDetail),
          estimatedDelivery: trackInfo.ExpectedDeliveryDate || null,
          lastUpdate: new Date().toISOString(),
          carrier: 'USPS',
          rawData: trackInfo
        };
      } else if (result.Error) {
        throw new Error(`USPS API Error: ${result.Error.Description}`);
      } else {
        throw new Error('Invalid response from USPS API');
      }

    } catch (error) {
      console.error('USPS tracking error:', error);
      return {
        success: false,
        error: error.message,
        trackingNumber: trackingNumber
      };
    }
  }

  /**
   * Parse USPS tracking status into standardized format
   */
  parseTrackingStatus(summary) {
    if (!summary) return 'unknown';

    const status = summary.toLowerCase();
    
    if (status.includes('delivered')) return 'delivered';
    if (status.includes('out for delivery')) return 'out_for_delivery';
    if (status.includes('in transit')) return 'in_transit';
    if (status.includes('departed') || status.includes('arrived')) return 'in_transit';
    if (status.includes('accepted') || status.includes('picked up')) return 'picked_up';
    if (status.includes('pre-shipment')) return 'label_created';
    if (status.includes('exception') || status.includes('notice left')) return 'exception';
    
    return 'unknown';
  }

  /**
   * Parse tracking history into standardized format
   */
  parseTrackingHistory(trackDetails) {
    if (!trackDetails) return [];

    const details = Array.isArray(trackDetails) ? trackDetails : [trackDetails];
    
    return details.map(detail => ({
      date: detail.EventDate,
      time: detail.EventTime,
      status: this.parseTrackingStatus(detail.Event),
      location: `${detail.EventCity || ''}, ${detail.EventState || ''}`.trim(),
      description: detail.Event,
      rawData: detail
    })).sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));
  }

  /**
   * Calculate shipping rates (requires different USPS API endpoint)
   */
  async calculateShippingRates(fromZip, toZip, weight, service = 'PRIORITY') {
    try {
      this.validateConfiguration();

      const rateRequest = {
        $: { USERID: this.userId },
        Package: {
          $: { ID: '1' },
          Service: service,
          ZipOrigination: fromZip,
          ZipDestination: toZip,
          Pounds: Math.floor(weight),
          Ounces: Math.round((weight % 1) * 16),
          Container: 'VARIABLE',
          Size: 'REGULAR',
          Machinable: 'true'
        }
      };

      const xmlRequest = new xml2js.Builder({ rootName: 'RateV4Request' }).buildObject(rateRequest);
      
      const response = await axios.get(this.apiUrl, {
        params: {
          API: 'RateV4',
          XML: xmlRequest
        },
        timeout: 10000
      });

      const result = await this.parser.parseStringPromise(response.data);
      
      if (result.RateV4Response && result.RateV4Response.Package) {
        const packageInfo = result.RateV4Response.Package;
        
        return {
          success: true,
          service: packageInfo.Postage.MailService,
          rate: parseFloat(packageInfo.Postage.Rate),
          deliveryDays: packageInfo.Postage.CommitmentName || null,
          rawData: packageInfo
        };
      } else if (result.Error) {
        throw new Error(`USPS API Error: ${result.Error.Description}`);
      } else {
        throw new Error('Invalid response from USPS API');
      }

    } catch (error) {
      console.error('USPS rate calculation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create shipping label (requires USPS eVS account and different API)
   */
  async createShippingLabel(shipmentData) {
    // Note: This requires USPS eVS (Electronic Verification System) account
    // and uses a different API endpoint. Implementation would depend on
    // specific USPS eVS integration requirements.
    
    console.log('USPS shipping label creation requires eVS account setup');
    return {
      success: false,
      error: 'USPS eVS integration required for label creation'
    };
  }

  /**
   * Validate tracking number format
   */
  validateTrackingNumber(trackingNumber) {
    if (!trackingNumber) return false;
    
    // USPS tracking number patterns
    const patterns = [
      /^(94|93|92|94|95)\d{20}$/, // Standard tracking
      /^(EA|EC|CP|RA)\d{9}US$/, // International
      /^(70|14|23|03)\d{14}$/, // Certified mail, etc.
      /^[A-Z]{2}\d{9}[A-Z]{2}$/ // International format
    ];
    
    return patterns.some(pattern => pattern.test(trackingNumber.replace(/\s/g, '')));
  }

  /**
   * Get delivery confirmation
   */
  async getDeliveryConfirmation(trackingNumber) {
    const trackingResult = await this.trackPackage(trackingNumber);
    
    if (trackingResult.success && trackingResult.status === 'delivered') {
      return {
        success: true,
        delivered: true,
        deliveryDate: trackingResult.statusHistory[0]?.date,
        deliveryTime: trackingResult.statusHistory[0]?.time,
        deliveryLocation: trackingResult.statusHistory[0]?.location,
        signedBy: trackingResult.rawData.DeliveredTo || null
      };
    }
    
    return {
      success: true,
      delivered: false,
      currentStatus: trackingResult.status,
      estimatedDelivery: trackingResult.estimatedDelivery
    };
  }

  /**
   * Batch track multiple packages
   */
  async batchTrackPackages(trackingNumbers) {
    const results = [];
    
    for (const trackingNumber of trackingNumbers) {
      try {
        const result = await this.trackPackage(trackingNumber);
        results.push(result);
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        results.push({
          success: false,
          trackingNumber: trackingNumber,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

module.exports = new USPSService();
