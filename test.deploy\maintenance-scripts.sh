#!/bin/bash

# Maintenance Scripts for Testing Environment
# test.shopnirvanaorganics.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_PATH="/var/www/nirvana-test"
DOMAIN="test.shopnirvanaorganics.com"
DB_NAME="nirvana_organics_testing"
DB_USER="nirvana_test_user"

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# System status check
check_system_status() {
    log_info "Checking system status for testing environment..."
    
    echo -e "${BLUE}=== System Information ===${NC}"
    echo "Hostname: $(hostname)"
    echo "Uptime: $(uptime -p)"
    echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
    echo "Memory Usage: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')"
    echo "Disk Usage: $(df -h / | tail -1 | awk '{print $5 " used of " $2}')"
    
    echo -e "\n${BLUE}=== Service Status ===${NC}"
    for service in nginx mysql pm2; do
        if systemctl is-active --quiet $service 2>/dev/null || pgrep -f $service >/dev/null; then
            echo -e "$service: ${GREEN}Running${NC}"
        else
            echo -e "$service: ${RED}Stopped${NC}"
        fi
    done
    
    echo -e "\n${BLUE}=== PM2 Processes ===${NC}"
    sudo -u nirvana pm2 list 2>/dev/null || echo "PM2 not running or no processes"
    
    echo -e "\n${BLUE}=== Application Health ===${NC}"
    if curl -f -s https://$DOMAIN/api/health >/dev/null; then
        echo -e "API Health: ${GREEN}OK${NC}"
    else
        echo -e "API Health: ${RED}Failed${NC}"
    fi
    
    if curl -f -s https://$DOMAIN >/dev/null; then
        echo -e "Frontend: ${GREEN}OK${NC}"
    else
        echo -e "Frontend: ${RED}Failed${NC}"
    fi
}

# Update application
update_application() {
    log_info "Updating testing application..."
    
    cd $PROJECT_PATH
    
    # Backup current version
    log_info "Creating backup before update..."
    sudo -u nirvana git stash push -m "Pre-update backup $(date)"
    
    # Pull latest changes
    log_info "Pulling latest changes from testing branch..."
    sudo -u nirvana git pull origin testing
    
    # Install dependencies
    log_info "Installing dependencies..."
    sudo -u nirvana npm install --production
    
    # Build frontend
    log_info "Building frontend..."
    sudo -u nirvana npm run build:test
    
    # Run migrations
    log_info "Running database migrations..."
    sudo -u nirvana npm run migrate:test
    
    # Reload PM2 processes
    log_info "Reloading PM2 processes..."
    sudo -u nirvana pm2 reload all
    
    # Wait for services to start
    sleep 10
    
    # Verify update
    if curl -f -s https://$DOMAIN/api/health >/dev/null; then
        log_info "✅ Application update completed successfully"
    else
        log_error "❌ Application update failed - API not responding"
        return 1
    fi
}

# Clean up logs and temporary files
cleanup_system() {
    log_info "Cleaning up system..."
    
    # Clean application logs older than 7 days
    find $PROJECT_PATH/logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # Clean PM2 logs
    sudo -u nirvana pm2 flush
    
    # Clean npm cache
    sudo -u nirvana npm cache clean --force
    
    # Clean system logs
    sudo journalctl --vacuum-time=7d
    
    # Clean package cache
    sudo apt autoremove -y
    sudo apt autoclean
    
    # Clean temporary files
    sudo find /tmp -type f -atime +7 -delete 2>/dev/null || true
    
    log_info "System cleanup completed"
}

# Restart all services
restart_services() {
    log_info "Restarting all services..."
    
    # Restart PM2 processes
    sudo -u nirvana pm2 restart all
    
    # Restart system services
    sudo systemctl restart nginx
    sudo systemctl restart mysql
    
    # Wait for services to start
    sleep 15
    
    # Verify services
    check_system_status
    
    log_info "Services restart completed"
}

# Database maintenance
maintain_database() {
    log_info "Performing database maintenance..."
    
    # Optimize tables
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "
        SELECT CONCAT('OPTIMIZE TABLE ', table_name, ';') 
        FROM information_schema.tables 
        WHERE table_schema='$DB_NAME';" | grep -v CONCAT > /tmp/optimize.sql
    
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < /tmp/optimize.sql
    rm /tmp/optimize.sql
    
    # Analyze tables
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "
        SELECT CONCAT('ANALYZE TABLE ', table_name, ';') 
        FROM information_schema.tables 
        WHERE table_schema='$DB_NAME';" | grep -v CONCAT > /tmp/analyze.sql
    
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < /tmp/analyze.sql
    rm /tmp/analyze.sql
    
    log_info "Database maintenance completed"
}

# SSL certificate check and renewal
check_ssl() {
    log_info "Checking SSL certificate..."
    
    local cert_file="/etc/ssl/certs/$DOMAIN.crt"
    
    if [ -f "$cert_file" ]; then
        local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( ($expiry_timestamp - $current_timestamp) / 86400 ))
        
        echo "SSL Certificate expires in $days_until_expiry days"
        
        if [ $days_until_expiry -lt 30 ]; then
            log_warning "SSL certificate expires soon, attempting renewal..."
            sudo certbot renew --quiet
            
            if [ $? -eq 0 ]; then
                log_info "SSL certificate renewed successfully"
                sudo systemctl reload nginx
            else
                log_error "SSL certificate renewal failed"
            fi
        else
            log_info "SSL certificate is valid"
        fi
    else
        log_error "SSL certificate file not found"
    fi
}

# Performance monitoring
monitor_performance() {
    log_info "Monitoring system performance..."
    
    echo -e "${BLUE}=== CPU Usage ===${NC}"
    top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}'
    
    echo -e "\n${BLUE}=== Memory Usage ===${NC}"
    free -h
    
    echo -e "\n${BLUE}=== Disk I/O ===${NC}"
    iostat -x 1 1 | tail -n +4
    
    echo -e "\n${BLUE}=== Network Connections ===${NC}"
    ss -tuln | grep -E ':(80|443|3306|5001|5002)'
    
    echo -e "\n${BLUE}=== Top Processes ===${NC}"
    ps aux --sort=-%cpu | head -10
    
    echo -e "\n${BLUE}=== PM2 Monitoring ===${NC}"
    sudo -u nirvana pm2 monit --no-interaction || echo "PM2 monitoring not available"
}

# Security audit
security_audit() {
    log_info "Performing security audit..."
    
    echo -e "${BLUE}=== Failed Login Attempts ===${NC}"
    sudo grep "Failed password" /var/log/auth.log | tail -10 || echo "No recent failed attempts"
    
    echo -e "\n${BLUE}=== Firewall Status ===${NC}"
    sudo ufw status
    
    echo -e "\n${BLUE}=== Fail2ban Status ===${NC}"
    sudo fail2ban-client status || echo "Fail2ban not configured"
    
    echo -e "\n${BLUE}=== Open Ports ===${NC}"
    sudo netstat -tuln | grep LISTEN
    
    echo -e "\n${BLUE}=== System Updates ===${NC}"
    apt list --upgradable 2>/dev/null | wc -l | awk '{print $1 " packages can be upgraded"}'
}

# Generate maintenance report
generate_report() {
    local report_file="$PROJECT_PATH/logs/maintenance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "Generating maintenance report..."
    
    {
        echo "Nirvana Organics Testing Environment Maintenance Report"
        echo "======================================================"
        echo "Generated: $(date)"
        echo "Server: $(hostname)"
        echo ""
        
        echo "SYSTEM STATUS:"
        check_system_status
        echo ""
        
        echo "PERFORMANCE METRICS:"
        monitor_performance
        echo ""
        
        echo "SECURITY AUDIT:"
        security_audit
        echo ""
        
        echo "DISK USAGE:"
        df -h
        echo ""
        
        echo "LOG FILE SIZES:"
        find $PROJECT_PATH/logs -name "*.log" -exec ls -lh {} \; 2>/dev/null || echo "No log files found"
        
    } > "$report_file"
    
    chown nirvana:www-data "$report_file"
    chmod 644 "$report_file"
    
    log_info "Maintenance report generated: $report_file"
}

# Main script logic
case "${1:-status}" in
    "status")
        check_system_status
        ;;
    "update")
        update_application
        ;;
    "cleanup")
        cleanup_system
        ;;
    "restart")
        restart_services
        ;;
    "database")
        maintain_database
        ;;
    "ssl")
        check_ssl
        ;;
    "monitor")
        monitor_performance
        ;;
    "security")
        security_audit
        ;;
    "report")
        generate_report
        ;;
    "full")
        log_info "Running full maintenance cycle..."
        check_system_status
        cleanup_system
        maintain_database
        check_ssl
        generate_report
        log_info "Full maintenance cycle completed"
        ;;
    *)
        echo "Usage: $0 {status|update|cleanup|restart|database|ssl|monitor|security|report|full}"
        echo ""
        echo "Commands:"
        echo "  status    - Check system and application status"
        echo "  update    - Update application from git repository"
        echo "  cleanup   - Clean up logs and temporary files"
        echo "  restart   - Restart all services"
        echo "  database  - Perform database maintenance"
        echo "  ssl       - Check and renew SSL certificates"
        echo "  monitor   - Show performance monitoring"
        echo "  security  - Perform security audit"
        echo "  report    - Generate comprehensive maintenance report"
        echo "  full      - Run complete maintenance cycle"
        exit 1
        ;;
esac
