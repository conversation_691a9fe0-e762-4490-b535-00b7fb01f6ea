#!/bin/bash

# Nirvana Organics E-commerce - Production Setup Script
# This script sets up the production environment with all security and performance optimizations

set -e  # Exit on any error

echo "🚀 Starting Nirvana Organics E-commerce Production Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if we're in the right directory
if [[ ! -f "package.json" ]]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

print_step "1. Installing Node.js dependencies..."
npm install --production
print_status "Dependencies installed successfully"

print_step "2. Installing additional security dependencies..."
npm install winston helmet express-rate-limit express-validator compression
print_status "Security dependencies installed"

print_step "3. Creating required directories..."
mkdir -p logs uploads/products uploads/categories uploads/banners uploads/documents backups
print_status "Directories created"

print_step "4. Setting up file permissions..."
chmod 755 logs uploads backups
chmod -R 755 uploads/
print_status "File permissions set"

print_step "5. Checking environment configuration..."
if [[ ! -f ".env" ]]; then
    print_warning ".env file not found. Creating from template..."
    cp .env.template .env
    print_warning "Please edit .env file with your production values before starting the server"
else
    print_status ".env file exists"
fi

# Set secure permissions for .env file
chmod 600 .env
print_status "Environment file permissions secured"

print_step "6. Validating environment variables..."
node -e "
require('dotenv').config();
const requiredVars = ['NODE_ENV', 'JWT_SECRET', 'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
const missing = requiredVars.filter(v => !process.env[v] || process.env[v].includes('your-'));
if (missing.length > 0) {
    console.log('❌ Missing or template values in environment variables:', missing.join(', '));
    console.log('Please update your .env file with actual production values');
    process.exit(1);
} else {
    console.log('✅ Environment variables validation passed');
}
"

print_step "7. Testing database connection..."
node -e "
require('dotenv').config();
const mysql = require('mysql2/promise');
async function testDB() {
    try {
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME
        });
        await connection.execute('SELECT 1');
        await connection.end();
        console.log('✅ Database connection successful');
    } catch (error) {
        console.log('❌ Database connection failed:', error.message);
        process.exit(1);
    }
}
testDB();
"

print_step "8. Building frontend for production..."
if [[ -f "vite.config.ts" ]]; then
    npm run build
    print_status "Frontend built successfully"
else
    print_warning "Vite config not found, skipping frontend build"
fi

print_step "9. Setting up PM2 configuration..."
if command -v pm2 &> /dev/null; then
    print_status "PM2 is already installed"
else
    print_warning "PM2 not found. Installing globally..."
    sudo npm install -g pm2
fi

# Copy production PM2 config
cp ecosystem.config.production.js ecosystem.config.js
print_status "PM2 configuration set for production"

print_step "10. Setting up log rotation..."
cat > logrotate.conf << 'EOF'
/var/www/nirvana-backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nirvana nirvana
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
print_status "Log rotation configuration created"

print_step "11. Creating backup script..."
cat > backup-database.sh << 'EOF'
#!/bin/bash
source .env
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/nirvana_backup_$DATE.sql"

mkdir -p $BACKUP_DIR
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_FILE
gzip $BACKUP_FILE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Database backup completed: $BACKUP_FILE.gz"
EOF
chmod +x backup-database.sh
print_status "Database backup script created"

print_step "12. Creating health check script..."
cat > health-check.sh << 'EOF'
#!/bin/bash
source .env

echo "=== Nirvana Organics Health Check ==="
echo "Timestamp: $(date)"
echo

# Check PM2 status
echo "PM2 Status:"
pm2 status

echo
# Check API health
echo "API Health Check:"
curl -s http://localhost:$PORT/api/health | jq . || echo "API not responding"

echo
# Check disk space
echo "Disk Space:"
df -h /

echo
# Check memory usage
echo "Memory Usage:"
free -h

echo
# Check database connection
echo "Database Connection:"
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "SELECT 1 as test;" 2>/dev/null && echo "✅ Database OK" || echo "❌ Database Failed"
EOF
chmod +x health-check.sh
print_status "Health check script created"

print_step "13. Final security check..."
# Check for any remaining template values in .env
if grep -q "your-" .env; then
    print_warning "Template values still found in .env file. Please update with actual production values."
fi

# Check file permissions
print_status "File permissions summary:"
ls -la .env
ls -la logs/
ls -la uploads/

echo
print_status "🎉 Production setup completed successfully!"
echo
echo "Next steps:"
echo "1. Update .env file with your actual production values"
echo "2. Start the application: pm2 start ecosystem.config.js"
echo "3. Save PM2 configuration: pm2 save"
echo "4. Set up PM2 startup: pm2 startup"
echo "5. Configure Nginx (see nginx.production.conf)"
echo "6. Set up SSL certificates with certbot"
echo
echo "Useful commands:"
echo "- Check health: ./health-check.sh"
echo "- Backup database: ./backup-database.sh"
echo "- View logs: pm2 logs"
echo "- Restart app: pm2 restart nirvana-backend"
echo
print_status "Setup complete! 🚀"
