const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');

/**
 * Security configuration for dual-environment authentication system
 */

// Rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting in development
    return process.env.NODE_ENV === 'development';
  }
});

// Stricter rate limiting for admin authentication
const adminAuthLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Limit each IP to 3 admin login attempts per windowMs
  message: {
    success: false,
    message: 'Too many admin authentication attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting in development
    return process.env.NODE_ENV === 'development';
  }
});

// Rate limiting for data environment operations
const dataEnvironmentLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Limit each IP to 20 data environment operations per windowMs
  message: {
    success: false,
    message: 'Too many data environment operations, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting in development
    return process.env.NODE_ENV === 'development';
  }
});

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'https://test.shopnirvanaorganics.com',
      'https://shopnirvanaorganics.com',
      process.env.FRONTEND_URL
    ].filter(Boolean);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Session-Id',
    'X-Data-Mode'
  ]
};

// Helmet security configuration
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.square.com", "https://connect.squareup.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
    }
  },
  crossOriginEmbedderPolicy: false, // Disable for compatibility
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
};

// Security headers middleware
const securityHeaders = (req, res, next) => {
  // Add custom security headers
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // Add data environment security headers
  if (req.path.includes('/admin/data-environment')) {
    res.setHeader('X-Data-Environment-Protected', 'true');
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
  }
  
  next();
};

// IP whitelist for admin operations (optional)
const adminIPWhitelist = (req, res, next) => {
  // Skip in development
  if (process.env.NODE_ENV === 'development') {
    return next();
  }

  const allowedIPs = process.env.ADMIN_IP_WHITELIST?.split(',').map(ip => ip.trim()).filter(ip => ip) || [];

  if (allowedIPs.length === 0) {
    return next(); // No IP restrictions if not configured
  }

  const clientIP = req.ip || req.connection.remoteAddress;

  if (allowedIPs.includes(clientIP)) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: 'Access denied from this IP address'
    });
  }
};

// Session validation for data environment operations
const validateDataEnvironmentSession = (req, res, next) => {
  const sessionId = req.headers['x-session-id'];
  
  if (!sessionId) {
    return res.status(400).json({
      success: false,
      message: 'Session ID required for data environment operations'
    });
  }

  // Validate session ID format
  const sessionIdPattern = /^session_\d+_[a-z0-9]{9}$/;
  if (!sessionIdPattern.test(sessionId)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid session ID format'
    });
  }

  next();
};

// Audit logging for sensitive operations
const auditDataEnvironmentOperation = (operation) => {
  return (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log the operation
      console.log('Data Environment Audit Log:', {
        operation,
        userId: req.user?.id,
        userEmail: req.user?.email,
        sessionId: req.headers['x-session-id'],
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString(),
        success: res.statusCode < 400,
        statusCode: res.statusCode
      });
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

// Environment-specific security middleware
const environmentSecurity = (req, res, next) => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDataEnvironmentRoute = req.path.includes('/admin/data-environment');
  
  if (isProduction && isDataEnvironmentRoute) {
    // Additional security checks for production
    const userAgent = req.get('User-Agent');
    
    // Block suspicious user agents
    if (!userAgent || userAgent.includes('bot') || userAgent.includes('crawler')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    // Require HTTPS in production
    if (!req.secure && req.get('X-Forwarded-Proto') !== 'https') {
      return res.status(403).json({
        success: false,
        message: 'HTTPS required for data environment operations'
      });
    }
  }
  
  next();
};

module.exports = {
  authLimiter,
  adminAuthLimiter,
  dataEnvironmentLimiter,
  corsOptions,
  helmetConfig,
  securityHeaders,
  adminIPWhitelist,
  validateDataEnvironmentSession,
  auditDataEnvironmentOperation,
  environmentSecurity,
  
  // Apply all security middleware
  applySecurity: (app) => {
    app.use(helmet(helmetConfig));
    app.use(cors(corsOptions));
    app.use(securityHeaders);
    app.use(environmentSecurity);
  }
};
