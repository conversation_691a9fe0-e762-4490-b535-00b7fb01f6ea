#!/usr/bin/env node

/**
 * Admin Panel Testing Script for Nirvana Organics E-commerce
 * Validates that the admin panel is properly built, deployed, and accessible
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}[STEP]${colors.reset} ${msg}`)
};

// Configuration
const config = {
  domain: process.env.APP_URL || 'https://test.shopnirvanaorganics.com',
  timeout: 10000,
  adminPath: '/admin',
  apiPath: '/api/admin'
};

let testScore = 0;
let totalTests = 0;
const issues = [];

function addTest(passed, message, severity = 'medium') {
  totalTests++;
  if (passed) {
    testScore++;
    log.success(message);
  } else {
    issues.push({ message, severity });
    if (severity === 'critical') {
      log.error(message);
    } else {
      log.warning(message);
    }
  }
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const timeout = options.timeout || config.timeout;
    
    const req = protocol.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          responseTime: Date.now() - startTime
        });
      });
    });
    
    const startTime = Date.now();
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.setTimeout(timeout);
  });
}

async function testLocalBuildStructure() {
  log.step('1. Testing Local Build Structure...');
  
  // Check if dist directory exists
  const distExists = fs.existsSync('dist');
  addTest(distExists, 'Main dist directory exists', 'critical');
  
  if (distExists) {
    // Check main frontend files
    const mainIndexExists = fs.existsSync('dist/index.html');
    addTest(mainIndexExists, 'Main frontend index.html exists', 'critical');
    
    // Check admin directory
    const adminDirExists = fs.existsSync('dist/admin');
    addTest(adminDirExists, 'Admin directory exists in dist', 'critical');
    
    if (adminDirExists) {
      // Check admin HTML file
      const adminHtmlExists = fs.existsSync('dist/admin/admin.html');
      addTest(adminHtmlExists, 'Admin HTML file exists', 'critical');
      
      // Check admin assets
      const adminAssetsExists = fs.existsSync('dist/admin/assets');
      addTest(adminAssetsExists, 'Admin assets directory exists', 'high');
      
      if (adminHtmlExists) {
        // Check admin HTML content
        const adminHtmlContent = fs.readFileSync('dist/admin/admin.html', 'utf8');
        addTest(
          adminHtmlContent.includes('base href="/admin/"'),
          'Admin HTML has correct base href',
          'high'
        );
        
        addTest(
          adminHtmlContent.includes('Nirvana') || adminHtmlContent.includes('Admin'),
          'Admin HTML contains expected content',
          'medium'
        );
      }
      
      // Check routing configuration
      const routingConfigExists = fs.existsSync('dist/routing-config.json');
      if (routingConfigExists) {
        try {
          const routingConfig = JSON.parse(fs.readFileSync('dist/routing-config.json', 'utf8'));
          addTest(
            routingConfig.admin && routingConfig.admin.path === '/admin',
            'Routing configuration includes admin path',
            'medium'
          );
        } catch (error) {
          addTest(false, 'Routing configuration is valid JSON', 'medium');
        }
      }
    }
  }
}

async function testDeploymentPackages() {
  log.step('2. Testing Deployment Packages...');
  
  const environments = ['testing-environment', 'production-environment'];
  
  for (const env of environments) {
    const packagePath = `deployment-packages/${env}`;
    const packageExists = fs.existsSync(packagePath);
    addTest(packageExists, `${env} package exists`, 'high');
    
    if (packageExists) {
      // Check admin files in deployment package
      const adminInPackage = fs.existsSync(`${packagePath}/dist/admin`);
      addTest(adminInPackage, `Admin files included in ${env} package`, 'critical');
      
      if (adminInPackage) {
        const adminHtmlInPackage = fs.existsSync(`${packagePath}/dist/admin/admin.html`);
        addTest(adminHtmlInPackage, `Admin HTML included in ${env} package`, 'critical');
        
        const adminAssetsInPackage = fs.existsSync(`${packagePath}/dist/admin/assets`);
        addTest(adminAssetsInPackage, `Admin assets included in ${env} package`, 'high');
      }
      
      // Check Nginx configuration includes admin routes
      const nginxConfigExists = fs.existsSync(`${packagePath}/nginx.production.conf`);
      if (nginxConfigExists) {
        const nginxContent = fs.readFileSync(`${packagePath}/nginx.production.conf`, 'utf8');
        addTest(
          nginxContent.includes('location /admin/'),
          `Nginx config includes admin routes in ${env}`,
          'high'
        );
        
        addTest(
          nginxContent.includes('location /api/admin/'),
          `Nginx config includes admin API routes in ${env}`,
          'high'
        );
      }
    }
  }
}

async function testAdminPanelAccess() {
  log.step('3. Testing Admin Panel Access...');
  
  try {
    const adminUrl = `${config.domain}${config.adminPath}/`;
    const response = await makeRequest(adminUrl);
    
    addTest(
      response.statusCode === 200,
      `Admin panel responds with status ${response.statusCode}`,
      'critical'
    );
    
    if (response.statusCode === 200) {
      addTest(
        response.data.includes('<html') || response.data.includes('<!DOCTYPE'),
        'Admin panel returns HTML content',
        'high'
      );
      
      addTest(
        response.data.includes('admin') || response.data.includes('Admin'),
        'Admin panel contains admin-related content',
        'medium'
      );
      
      addTest(
        response.responseTime < 3000,
        `Admin panel response time: ${response.responseTime}ms`,
        'medium'
      );
    }
    
  } catch (error) {
    addTest(false, `Admin panel accessible: ${error.message}`, 'critical');
  }
}

async function testAdminSecurityHeaders() {
  log.step('4. Testing Admin Security Headers...');
  
  try {
    const adminUrl = `${config.domain}${config.adminPath}/`;
    const response = await makeRequest(adminUrl);
    const headers = response.headers;
    
    // Check for admin-specific security headers
    addTest(
      headers['x-frame-options'] === 'DENY',
      `X-Frame-Options header set to DENY: ${headers['x-frame-options'] || 'missing'}`,
      'high'
    );
    
    addTest(
      !!headers['x-xss-protection'],
      `X-XSS-Protection header present: ${headers['x-xss-protection'] || 'missing'}`,
      'high'
    );
    
    addTest(
      !!headers['x-content-type-options'],
      `X-Content-Type-Options header present: ${headers['x-content-type-options'] || 'missing'}`,
      'high'
    );
    
    addTest(
      !!headers['x-robots-tag'],
      `X-Robots-Tag header present: ${headers['x-robots-tag'] || 'missing'}`,
      'medium'
    );
    
    // Check cache control for admin panel
    addTest(
      !headers['cache-control'] || headers['cache-control'].includes('no-cache'),
      'Admin panel has appropriate cache control',
      'medium'
    );
    
  } catch (error) {
    addTest(false, `Admin security headers check failed: ${error.message}`, 'medium');
  }
}

async function testAdminAPIRoutes() {
  log.step('5. Testing Admin API Routes...');
  
  const adminApiRoutes = [
    '/api/admin/health',
    '/api/admin/dashboard',
    '/api/admin/users'
  ];
  
  for (const route of adminApiRoutes) {
    try {
      const url = `${config.domain}${route}`;
      const response = await makeRequest(url);
      
      // Admin API routes should require authentication, so 401 or 403 is expected
      const expectedStatuses = [200, 401, 403];
      addTest(
        expectedStatuses.includes(response.statusCode),
        `${route} responds appropriately (${response.statusCode})`,
        'medium'
      );
      
      // Check for admin API headers
      if (response.headers['x-admin-api']) {
        addTest(true, `${route} has admin API header`, 'low');
      }
      
    } catch (error) {
      // Some admin routes might not be accessible without authentication
      log.info(`${route} not accessible (expected for protected routes): ${error.message}`);
    }
  }
}

async function testAdminAssets() {
  log.step('6. Testing Admin Assets...');
  
  try {
    // Test admin CSS and JS assets (if we can determine their names)
    const adminUrl = `${config.domain}${config.adminPath}/`;
    const response = await makeRequest(adminUrl);
    
    if (response.statusCode === 200) {
      // Extract asset URLs from HTML
      const cssMatches = response.data.match(/href="([^"]*\.css[^"]*)"/g);
      const jsMatches = response.data.match(/src="([^"]*\.js[^"]*)"/g);
      
      if (cssMatches && cssMatches.length > 0) {
        addTest(true, `Admin CSS assets found: ${cssMatches.length} files`, 'medium');
        
        // Test first CSS file
        const firstCss = cssMatches[0].match(/href="([^"]*)"/)[1];
        const cssUrl = firstCss.startsWith('http') ? firstCss : `${config.domain}${firstCss}`;
        
        try {
          const cssResponse = await makeRequest(cssUrl);
          addTest(
            cssResponse.statusCode === 200,
            `Admin CSS asset loads correctly (${cssResponse.statusCode})`,
            'medium'
          );
        } catch (cssError) {
          addTest(false, `Admin CSS asset accessible: ${cssError.message}`, 'medium');
        }
      }
      
      if (jsMatches && jsMatches.length > 0) {
        addTest(true, `Admin JS assets found: ${jsMatches.length} files`, 'medium');
        
        // Test first JS file
        const firstJs = jsMatches[0].match(/src="([^"]*)"/)[1];
        const jsUrl = firstJs.startsWith('http') ? firstJs : `${config.domain}${firstJs}`;
        
        try {
          const jsResponse = await makeRequest(jsUrl);
          addTest(
            jsResponse.statusCode === 200,
            `Admin JS asset loads correctly (${jsResponse.statusCode})`,
            'medium'
          );
        } catch (jsError) {
          addTest(false, `Admin JS asset accessible: ${jsError.message}`, 'medium');
        }
      }
    }
    
  } catch (error) {
    addTest(false, `Admin assets test failed: ${error.message}`, 'medium');
  }
}

function generateTestReport() {
  log.step('7. Generating Test Report...');
  
  const percentage = Math.round((testScore / totalTests) * 100);
  const grade = percentage >= 90 ? 'A' : percentage >= 80 ? 'B' : percentage >= 70 ? 'C' : percentage >= 60 ? 'D' : 'F';
  
  console.log('\n' + '='.repeat(60));
  console.log(`${colors.bright}ADMIN PANEL TEST REPORT${colors.reset}`);
  console.log('='.repeat(60));
  console.log(`Test Score: ${colors.bright}${testScore}/${totalTests} (${percentage}%)${colors.reset}`);
  console.log(`Test Grade: ${colors.bright}${grade}${colors.reset}`);
  console.log(`Domain: ${colors.cyan}${config.domain}${colors.reset}`);
  console.log(`Admin URL: ${colors.cyan}${config.domain}${config.adminPath}${colors.reset}`);
  console.log('='.repeat(60));
  
  if (issues.length > 0) {
    console.log(`\n${colors.red}ISSUES FOUND:${colors.reset}`);
    
    const criticalIssues = issues.filter(i => i.severity === 'critical');
    const highIssues = issues.filter(i => i.severity === 'high');
    const mediumIssues = issues.filter(i => i.severity === 'medium');
    
    if (criticalIssues.length > 0) {
      console.log(`\n${colors.red}CRITICAL (${criticalIssues.length}):${colors.reset}`);
      criticalIssues.forEach(issue => console.log(`  ❌ ${issue.message}`));
    }
    
    if (highIssues.length > 0) {
      console.log(`\n${colors.yellow}HIGH (${highIssues.length}):${colors.reset}`);
      highIssues.forEach(issue => console.log(`  ⚠️  ${issue.message}`));
    }
    
    if (mediumIssues.length > 0) {
      console.log(`\n${colors.blue}MEDIUM (${mediumIssues.length}):${colors.reset}`);
      mediumIssues.forEach(issue => console.log(`  ℹ️  ${issue.message}`));
    }
  } else {
    console.log(`\n${colors.green}✅ No issues found!${colors.reset}`);
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (percentage < 70) {
    console.log(`${colors.red}❌ ADMIN PANEL TESTS FAILED${colors.reset}`);
    console.log('Please address the critical and high-priority issues above.');
  } else if (percentage < 85) {
    console.log(`${colors.yellow}⚠️  ADMIN PANEL PARTIALLY READY${colors.reset}`);
    console.log('Consider addressing the remaining issues for better reliability.');
  } else {
    console.log(`${colors.green}✅ ADMIN PANEL TESTS PASSED!${colors.reset}`);
    console.log('Your admin panel is properly configured and ready for use.');
  }
  
  return percentage >= 70;
}

// Main execution
async function main() {
  console.log(`${colors.bright}Nirvana Organics Admin Panel Testing${colors.reset}\n`);
  console.log(`Target Domain: ${colors.cyan}${config.domain}${colors.reset}`);
  console.log(`Admin Panel URL: ${colors.cyan}${config.domain}${config.adminPath}${colors.reset}\n`);
  
  try {
    await testLocalBuildStructure();
    await testDeploymentPackages();
    await testAdminPanelAccess();
    await testAdminSecurityHeaders();
    await testAdminAPIRoutes();
    await testAdminAssets();
    
    const passed = generateTestReport();
    
    process.exit(passed ? 0 : 1);
  } catch (error) {
    log.error(`Admin panel testing failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.length > 2) {
  config.domain = process.argv[2];
}

main();
