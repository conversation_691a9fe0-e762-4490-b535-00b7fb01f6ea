<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Update - {{orderNumber}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .header.processing {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
        .header.shipped {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        .header.delivered {
            background: linear-gradient(135deg, #28a745, #155724);
            color: white;
        }
        .header.cancelled {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        .status-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .order-number {
            font-size: 24px;
            font-weight: bold;
            margin: 15px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            margin: 10px 0;
        }
        .status-processing {
            background-color: #17a2b8;
            color: white;
        }
        .status-shipped {
            background-color: #28a745;
            color: white;
        }
        .status-delivered {
            background-color: #155724;
            color: white;
        }
        .status-cancelled {
            background-color: #dc3545;
            color: white;
        }
        .message-section {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin: 25px 0;
            border-left: 4px solid #28a745;
        }
        .message-section.cancelled {
            border-left-color: #dc3545;
        }
        .message-text {
            font-size: 16px;
            margin: 0;
            color: #333;
        }
        .tracking-section {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
            text-align: center;
        }
        .tracking-number {
            font-size: 20px;
            font-weight: bold;
            color: #1976d2;
            margin: 10px 0;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border: 2px dashed #1976d2;
        }
        .track-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #1976d2;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 15px 0;
        }
        .track-button:hover {
            background-color: #1565c0;
        }
        .delivery-info {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .delivery-info h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #c8e6c9;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .contact-section {
            background-color: #fff3e0;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .contact-section h3 {
            margin-top: 0;
            color: #f57c00;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
        }
        .logo {
            font-size: 20px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .info-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header {{newStatus}}">
            <div class="status-icon">
                {{#if (eq newStatus 'processing')}}⚙️{{/if}}
                {{#if (eq newStatus 'shipped')}}🚚{{/if}}
                {{#if (eq newStatus 'delivered')}}📦{{/if}}
                {{#if (eq newStatus 'cancelled')}}❌{{/if}}
            </div>
            <h1>Order Update</h1>
            <div class="order-number">Order #{{orderNumber}}</div>
            <span class="status-badge status-{{newStatus}}">{{newStatus}}</span>
        </div>

        <!-- Customer Greeting -->
        <div style="margin-bottom: 30px;">
            <h2>Hello {{customerName}},</h2>
            <p>We have an update on your recent order. Here are the latest details:</p>
        </div>

        <!-- Status Message -->
        <div class="message-section {{#if (eq newStatus 'cancelled')}}cancelled{{/if}}">
            <p class="message-text">{{statusMessage}}</p>
        </div>

        <!-- Tracking Information (if available) -->
        {{#if trackingNumber}}
        <div class="tracking-section">
            <h3>📍 Track Your Package</h3>
            <p>Your order is on its way! Use the tracking number below to monitor your shipment:</p>
            <div class="tracking-number">{{trackingNumber}}</div>
            {{#if trackingUrl}}
            <a href="{{trackingUrl}}" class="track-button">Track Your Order</a>
            {{/if}}
        </div>
        {{/if}}

        <!-- Delivery Information -->
        {{#if estimatedDelivery}}
        <div class="delivery-info">
            <h3>🚚 Delivery Information</h3>
            <div class="info-row">
                <span>Estimated Delivery:</span>
                <span><strong>{{estimatedDelivery}}</strong></span>
            </div>
            <div class="info-row">
                <span>Order Status:</span>
                <span><strong>{{newStatus}}</strong></span>
            </div>
            {{#if trackingNumber}}
            <div class="info-row">
                <span>Tracking Number:</span>
                <span><strong>{{trackingNumber}}</strong></span>
            </div>
            {{/if}}
        </div>
        {{/if}}

        <!-- Status-specific content -->
        {{#if (eq newStatus 'processing')}}
        <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #1976d2; margin-top: 0;">⚙️ Order Processing</h3>
            <p>Your order is currently being prepared for shipment. Our team is carefully packaging your items to ensure they arrive in perfect condition.</p>
            <p><strong>What happens next:</strong></p>
            <ul>
                <li>Quality check and packaging</li>
                <li>Shipping label creation</li>
                <li>Handoff to shipping carrier</li>
                <li>Tracking information will be provided</li>
            </ul>
        </div>
        {{/if}}

        {{#if (eq newStatus 'shipped')}}
        <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #2e7d32; margin-top: 0;">🚚 Order Shipped</h3>
            <p>Great news! Your order has been shipped and is on its way to you. You should receive it within the estimated delivery timeframe.</p>
            <p><strong>Shipping details:</strong></p>
            <ul>
                <li>Package has left our fulfillment center</li>
                <li>Tracking information is now active</li>
                <li>You'll receive delivery notifications</li>
                <li>No signature required unless specified</li>
            </ul>
        </div>
        {{/if}}

        {{#if (eq newStatus 'delivered')}}
        <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #155724; margin-top: 0;">📦 Order Delivered</h3>
            <p>Wonderful! Your order has been successfully delivered. We hope you love your Nirvana Organics products!</p>
            <p><strong>What's next:</strong></p>
            <ul>
                <li>Enjoy your premium organic products</li>
                <li>Share your experience with a review</li>
                <li>Contact us if you have any concerns</li>
                <li>Check out our latest products for your next order</li>
            </ul>
        </div>
        {{/if}}

        {{#if (eq newStatus 'cancelled')}}
        <div style="background-color: #ffebee; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #c62828; margin-top: 0;">❌ Order Cancelled</h3>
            <p>Your order has been cancelled as requested. If this was not intentional or if you have any questions, please contact us immediately.</p>
            <p><strong>Next steps:</strong></p>
            <ul>
                <li>Refund will be processed within 3-5 business days</li>
                <li>You'll receive a separate refund confirmation email</li>
                <li>Contact us if you need assistance with a new order</li>
                <li>Check our current promotions and deals</li>
            </ul>
        </div>
        {{/if}}

        <!-- Contact Information -->
        <div class="contact-section">
            <h3>📞 Need Help?</h3>
            <p>If you have any questions about your order or need assistance, we're here to help:</p>
            <p>
                <strong>Email:</strong> <a href="mailto:{{supportEmail}}">{{supportEmail}}</a><br>
                <strong>Phone:</strong> 1-800-NIRVANA<br>
                <strong>Hours:</strong> Monday - Friday, 9 AM - 6 PM EST
            </p>
            <p>When contacting us, please reference your order number: <strong>{{orderNumber}}</strong></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="logo">🌿 {{companyName}}</div>
            <p>Thank you for choosing {{companyName}}!</p>
            <p>🌿 Premium organic products for your wellness journey 🌿</p>
            <p style="font-size: 12px; color: #999; margin-top: 20px;">
                This email was sent regarding order #{{orderNumber}}. 
                If you have questions, reply to this email or contact us at {{supportEmail}}.
            </p>
        </div>
    </div>
</body>
</html>
