# Nirvana Organics E-commerce - Final VPS Deployment Guide

This comprehensive guide provides step-by-step instructions for manually deploying the Nirvana Organics Node.js e-commerce website to your VPS server using Microsoft Remote Desktop Connection to a Linux environment.

## 🖥️ Remote Desktop Environment Overview

You'll be working in a Linux desktop environment accessed through Remote Desktop. This guide assumes you have:
- Active Remote Desktop connection to your VPS
- Access to terminal/command line
- File manager for GUI-based file operations
- Text editor (nano, gedit, or similar)

## ✅ Step 1: Prerequisites Verification

### 1.1 Connect to Your VPS
1. Open **Microsoft Remote Desktop Connection**
2. Connect to your VPS server
3. Log in with your credentials
4. Open a **Terminal** window (usually found in Applications menu or right-click desktop)

### 1.2 Verify System Information
```bash
# Check operating system
cat /etc/os-release

# Check available disk space (should have at least 10GB free)
df -h

# Check memory (should have at least 2GB)
free -h

# Check if you have sudo privileges
sudo whoami
```

**Expected Results:**
- Ubuntu 20.04+ or similar Linux distribution
- At least 10GB free disk space
- At least 2GB RAM
- Sudo access confirmed

### 1.3 Update System Packages
```bash
# Update package lists
sudo apt update

# Upgrade existing packages
sudo apt upgrade -y

# Install essential tools
sudo apt install -y curl wget unzip git nano htop
```

**Why this step:** Ensures your system has the latest security updates and essential tools.

## 📁 Step 2: File Transfer and Organization

### 2.1 Create Working Directory
```bash
# Create a directory for deployment files
mkdir ~/nirvana-deployment
cd ~/nirvana-deployment
```

### 2.2 Transfer Deployment Package

**Method 1: Using File Manager (GUI)**
1. Open **File Manager** from the desktop
2. Navigate to your home directory (`/home/<USER>/`)
3. Create a folder called `nirvana-deployment`
4. Use the file manager to upload your deployment package:
   - For testing: Upload `test.deploy.tar.gz` or the `test.deploy` folder
   - For production: Upload `deploy.tar.gz` or the `deploy` folder

**Method 2: Using Terminal (if you have the files on a network location)**
```bash
# If files are on a network share or you can download them
cd ~/nirvana-deployment

# Download or copy your deployment package here
# Example: wget http://your-server/deploy.tar.gz
```

### 2.3 Extract Deployment Package
```bash
# Navigate to your deployment directory
cd ~/nirvana-deployment

# Extract the package (adjust filename as needed)
# For production deployment:
tar -xzf deploy.tar.gz
mv deploy nirvana-app

# For testing deployment:
# tar -xzf test.deploy.tar.gz
# mv test.deploy nirvana-app

# Verify extraction
ls -la nirvana-app/
```

**Expected Result:** You should see directories like `server/`, `dist/`, `scripts/`, `config/`, etc.

## 🔧 Step 3: Install Required Software

### 3.1 Install Node.js
```bash
# Download and install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version
npm --version
```

**Expected Output:** Node.js version 18.x and npm version 8.x or higher

### 3.2 Install MySQL Database
```bash
# Install MySQL server
sudo apt install -y mysql-server

# Start MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql

# Check MySQL status
sudo systemctl status mysql
```

**Expected Result:** MySQL service should be active and running

### 3.3 Secure MySQL Installation
```bash
# Run MySQL security script
sudo mysql_secure_installation
```

**Follow these prompts:**
- Set root password: **Yes** (choose a strong password)
- Remove anonymous users: **Yes**
- Disallow root login remotely: **Yes**
- Remove test database: **Yes**
- Reload privilege tables: **Yes**

### 3.4 Install Nginx Web Server
```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

**Expected Result:** Nginx service should be active and running

### 3.5 Install PM2 Process Manager
```bash
# Install PM2 globally
sudo npm install -g pm2

# Verify installation
pm2 --version
```

**Expected Result:** PM2 version number displayed

## 🏗️ Step 4: Application Setup

### 4.1 Create Application Directory
```bash
# Create application directory
sudo mkdir -p /var/www/nirvana-backend

# Copy application files
sudo cp -r ~/nirvana-deployment/nirvana-app/* /var/www/nirvana-backend/

# Create application user
sudo adduser --system --group --home /var/www/nirvana-backend nirvana

# Set ownership
sudo chown -R nirvana:nirvana /var/www/nirvana-backend

# Set permissions
sudo chmod -R 755 /var/www/nirvana-backend
```

### 4.2 Install Application Dependencies
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Install Node.js dependencies
sudo -u nirvana npm ci --production

# Verify installation
sudo -u nirvana npm list --depth=0
```

**Expected Result:** All dependencies installed without errors

## 🗄️ Step 5: Database Configuration

### 5.1 Create Database and User
```bash
# Connect to MySQL as root
sudo mysql -u root -p
```

**In MySQL prompt, execute these commands:**
```sql
-- Create database (choose appropriate name)
CREATE DATABASE nirvana_organics_production;
-- For testing: CREATE DATABASE nirvana_organics_testing;

-- Create database user
CREATE USER 'nirvana_user'@'localhost' IDENTIFIED BY 'YourSecurePassword123!';

-- Grant privileges
GRANT ALL PRIVILEGES ON nirvana_organics_production.* TO 'nirvana_user'@'localhost';
-- For testing: GRANT ALL PRIVILEGES ON nirvana_organics_testing.* TO 'nirvana_user'@'localhost';

-- Apply changes
FLUSH PRIVILEGES;

-- Exit MySQL
EXIT;
```

### 5.2 Initialize Database Schema
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Run database setup (as nirvana user)
sudo -u nirvana node scripts/setup-database.js

# Create database tables
sudo -u nirvana node scripts/create-database-tables.js

# Run migrations
sudo -u nirvana node scripts/run-migrations.js
```

**Expected Result:** Database tables created successfully

### 5.3 Create Admin User
```bash
# Create default admin user
sudo -u nirvana node scripts/create-default-admin.js
```

**Important:** Note the admin credentials displayed - you'll need these to log into the admin panel.

## ⚙️ Step 6: Environment Configuration

### 6.1 Create Environment File
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Copy environment template
sudo -u nirvana cp config/.env.production.template .env
# For testing: sudo -u nirvana cp config/.env.testing.template .env
```

### 6.2 Edit Environment Configuration

**Using Terminal Text Editor:**
```bash
# Edit environment file with nano
sudo -u nirvana nano .env
```

**Using GUI Text Editor:**
1. Open **File Manager**
2. Navigate to `/var/www/nirvana-backend/`
3. Right-click on `.env` file
4. Select "Open with Text Editor"
5. You may need to use `sudo` to edit: `sudo gedit /var/www/nirvana-backend/.env`

### 6.3 Configure Critical Environment Variables

**Update these values in the .env file:**
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_production
DB_USER=nirvana_user
DB_PASSWORD=YourSecurePassword123!

# Application Configuration
NODE_ENV=production
PORT=5000
APP_URL=https://shopnirvanaorganics.com

# JWT Secrets (generate strong 64-character strings)
JWT_SECRET=your-very-long-secure-jwt-secret-key-at-least-64-characters-long
JWT_REFRESH_SECRET=your-very-long-secure-refresh-secret-key-at-least-64-characters

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-specific-password
EMAIL_FROM="Nirvana Organics <<EMAIL>>"

# Square Payment Configuration
SQUARE_ACCESS_TOKEN=your-square-production-access-token
SQUARE_APPLICATION_ID=your-square-production-application-id
SQUARE_ENVIRONMENT=production

# Security
SESSION_SECRET=your-very-long-secure-session-secret-at-least-64-characters
CORS_ORIGIN=https://shopnirvanaorganics.com
```

**For Testing Environment, use these instead:**
```bash
NODE_ENV=testing
APP_URL=https://test.shopnirvanaorganics.com
SQUARE_ENVIRONMENT=sandbox
CORS_ORIGIN=https://test.shopnirvanaorganics.com
DB_NAME=nirvana_organics_testing
```

### 6.4 Secure Environment File
```bash
# Set secure permissions
sudo chmod 600 /var/www/nirvana-backend/.env
sudo chown nirvana:nirvana /var/www/nirvana-backend/.env
```

## 🌐 Step 7: Nginx Configuration

### 7.1 Create Nginx Site Configuration
```bash
# Create Nginx configuration file
sudo nano /etc/nginx/sites-available/nirvana-organics
```

### 7.2 Add Nginx Configuration

**Copy the nginx configuration from your deployment package:**
```bash
# Copy the appropriate nginx config
sudo cp /var/www/nirvana-backend/nginx/nirvana-production.conf /etc/nginx/sites-available/nirvana-organics
# For testing: sudo cp /var/www/nirvana-backend/nginx/nirvana-testing.conf /etc/nginx/sites-available/nirvana-organics
```

### 7.3 Enable the Site
```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/nirvana-organics /etc/nginx/sites-enabled/

# Remove default site
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

**Expected Result:** "nginx: configuration file test is successful"

## 🔒 Step 8: SSL Certificate Setup

### 8.1 Install Certbot
```bash
# Install Certbot for Let's Encrypt
sudo apt install -y certbot python3-certbot-nginx
```

### 8.2 Obtain SSL Certificate

**Important:** Ensure your domain DNS is pointing to your VPS IP before running this command.

```bash
# For production (multiple domains)
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com

# For testing (single domain)
sudo certbot --nginx -d test.shopnirvanaorganics.com
```

**Follow the prompts:**
- Enter email address for notifications
- Agree to terms of service
- Choose whether to share email with EFF
- Select redirect option (recommended: redirect HTTP to HTTPS)

### 8.3 Test SSL Renewal
```bash
# Test automatic renewal
sudo certbot renew --dry-run
```

**Expected Result:** "Congratulations, all renewals succeeded"

## 🚀 Step 9: Start the Application

### 9.1 Start Application with PM2
```bash
# Navigate to application directory
cd /var/www/nirvana-backend

# Start the application
sudo -u nirvana pm2 start ecosystem.config.production.js
# For testing: sudo -u nirvana pm2 start ecosystem.config.testing.js

# Save PM2 configuration
sudo -u nirvana pm2 save

# Setup PM2 startup script
sudo -u nirvana pm2 startup
```

**Follow the instructions provided by the startup command** (usually involves running a command as root)

### 9.2 Verify Application Status
```bash
# Check PM2 status
sudo -u nirvana pm2 status

# View application logs
sudo -u nirvana pm2 logs

# Monitor application (press Ctrl+C to exit)
sudo -u nirvana pm2 monit
```

**Expected Result:** Application should show as "online" in PM2 status

## ✅ Step 10: Testing and Verification

### 10.1 Test Website Access

**Using Terminal:**
```bash
# Test local access
curl -I http://localhost

# Test domain access (replace with your domain)
curl -I https://shopnirvanaorganics.com

# Test API health endpoint
curl https://shopnirvanaorganics.com/api/health
```

**Using Web Browser in Remote Desktop:**
1. Open **Firefox** or **Chrome** from the desktop
2. Navigate to your domain: `https://shopnirvanaorganics.com`
3. Verify the website loads correctly
4. Test the admin panel: `https://shopnirvanaorganics.com/admin`

### 10.2 Test Admin Panel Access
1. Navigate to: `https://shopnirvanaorganics.com/admin`
2. Log in with the admin credentials created earlier
3. **Immediately change the admin password** for security
4. Verify admin panel functionality

### 10.3 Run System Status Check
```bash
# Check overall system status
cd /var/www/nirvana-backend
sudo -u nirvana node scripts/system-status.js
```

**Expected Result:** All systems should report as healthy

## 🔧 Step 11: Post-Deployment Security

### 11.1 Configure Firewall
```bash
# Install and configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Check firewall status
sudo ufw status
```

### 11.2 Set Up Database Backups
```bash
# Create backup directory
sudo mkdir -p /var/backups/nirvana
sudo chown nirvana:nirvana /var/backups/nirvana

# Test backup script
cd /var/www/nirvana-backend
sudo -u nirvana node scripts/backup-database.js

# Set up daily backup cron job
sudo -u nirvana crontab -e
```

**Add this line to crontab for daily backups at 2 AM:**
```
0 2 * * * cd /var/www/nirvana-backend && node scripts/backup-database.js
```

## 🆘 Troubleshooting Common Issues

### Issue 1: Application Won't Start

**Symptoms:** PM2 shows application as "errored"

**Diagnosis:**
```bash
# Check PM2 logs for errors
sudo -u nirvana pm2 logs --err

# Check if environment file exists
ls -la /var/www/nirvana-backend/.env

# Test database connection
cd /var/www/nirvana-backend
mysql -u nirvana_user -p nirvana_organics_production
```

**Solutions:**
1. Verify all environment variables are set correctly
2. Check database credentials and connection
3. Ensure all file permissions are correct
4. Restart the application: `sudo -u nirvana pm2 restart all`

### Issue 2: Website Not Accessible

**Symptoms:** Browser shows "This site can't be reached"

**Diagnosis:**
```bash
# Check Nginx status
sudo systemctl status nginx

# Test Nginx configuration
sudo nginx -t

# Check if application is running
sudo -u nirvana pm2 status

# Check firewall
sudo ufw status
```

**Solutions:**
1. Restart Nginx: `sudo systemctl restart nginx`
2. Check domain DNS configuration
3. Verify SSL certificate: `sudo certbot certificates`
4. Check firewall rules

### Issue 3: Database Connection Errors

**Symptoms:** Application logs show database connection failures

**Diagnosis:**
```bash
# Check MySQL status
sudo systemctl status mysql

# Test database connection manually
mysql -u nirvana_user -p nirvana_organics_production

# Check MySQL error logs
sudo tail -f /var/log/mysql/error.log
```

**Solutions:**
1. Restart MySQL: `sudo systemctl restart mysql`
2. Verify database user permissions
3. Check environment file database configuration
4. Recreate database user if necessary

### Issue 4: Admin Panel Shows 404

**Symptoms:** Admin panel URL returns 404 error

**Diagnosis:**
```bash
# Check if admin files exist
ls -la /var/www/nirvana-backend/dist/admin/

# Check Nginx configuration
sudo grep -n "location /admin" /etc/nginx/sites-available/nirvana-organics

# Check Nginx access logs
sudo tail -f /var/log/nginx/access.log
```

**Solutions:**
1. Verify admin build files are present in `dist/admin/`
2. Check Nginx configuration for admin routing
3. Restart Nginx: `sudo systemctl reload nginx`
4. Clear browser cache

### Issue 5: SSL Certificate Problems

**Symptoms:** Browser shows "Not secure" or certificate errors

**Diagnosis:**
```bash
# Check certificate status
sudo certbot certificates

# Test SSL
openssl s_client -connect shopnirvanaorganics.com:443
```

**Solutions:**
1. Renew certificate: `sudo certbot renew`
2. Restart Nginx: `sudo systemctl restart nginx`
3. Check domain DNS configuration

## 📊 Monitoring and Maintenance

### Daily Monitoring (via Remote Desktop)

**Using Terminal:**
```bash
# Check application status
sudo -u nirvana pm2 status

# View recent logs
sudo -u nirvana pm2 logs --lines 50

# Check system resources
htop

# Check disk space
df -h
```

**Using GUI Tools:**
1. Open **System Monitor** to check CPU, memory, and disk usage
2. Use **File Manager** to check log files in `/var/www/nirvana-backend/logs/`
3. Use web browser to test website functionality

### Weekly Maintenance Tasks
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Check SSL certificate status
sudo certbot certificates

# Review application logs for errors
sudo -u nirvana pm2 logs --err --lines 100

# Check backup integrity
ls -la /var/backups/nirvana/
```

## 📞 Getting Help

### Log Collection for Support
```bash
# Create support log directory
mkdir ~/support-logs

# Collect application logs
sudo -u nirvana pm2 logs --lines 200 > ~/support-logs/application.log

# Collect system logs
sudo cp /var/log/nginx/access.log ~/support-logs/
sudo cp /var/log/nginx/error.log ~/support-logs/

# Collect system information
uname -a > ~/support-logs/system-info.txt
df -h >> ~/support-logs/system-info.txt
free -h >> ~/support-logs/system-info.txt

# Create archive
tar -czf ~/support-logs.tar.gz ~/support-logs/
```

### Important File Locations
- **Application Files:** `/var/www/nirvana-backend/`
- **Environment Config:** `/var/www/nirvana-backend/.env`
- **Nginx Config:** `/etc/nginx/sites-available/nirvana-organics`
- **Application Logs:** Check with `sudo -u nirvana pm2 logs`
- **Nginx Logs:** `/var/log/nginx/`
- **Database Backups:** `/var/backups/nirvana/`

---

**Deployment Guide Version:** 1.0.0  
**Last Updated:** ${new Date().toISOString().split('T')[0]}  
**Environment:** Remote Desktop VPS Deployment  
**Target:** Manual Step-by-Step Deployment
