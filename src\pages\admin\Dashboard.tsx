import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppSelector } from '../../hooks/redux';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminPageHeader from '../../components/admin/AdminPageHeader';
import AdminDataTable, { TableColumn } from '../../components/admin/AdminDataTable';
import LowStockWidget from '../../components/admin/LowStockWidget';
import DataModeIndicator from '../../components/admin/DataModeIndicator';
import MockDataManager from '../../components/admin/MockDataManager';
import { useDataEnvironment } from '../../contexts/DataEnvironmentContext';
import {
  ChartBarIcon,
  ShoppingBagIcon,
  UserGroupIcon,
  CubeIcon,
  EyeIcon,
  PlusIcon,
  ArrowPathIcon,
  GiftIcon,
  ShareIcon,
  TruckIcon,
  StarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalProducts: number;
  totalUsers: number;
  pendingOrders: number;
  lowStockProducts: number;
  revenueGrowth: number;
  orderGrowth: number;
  // Enhanced stats
  activeCoupons: number;
  totalReferrals: number;
  pendingReviews: number;
  shippingIssues: number;
  socialShares: number;
  inventorySyncStatus: 'synced' | 'syncing' | 'error';
  lastSyncTime: string;
}

interface RecentOrder {
  id: string;
  customer: string;
  amount: number;
  status: string;
  date: string;
}

interface LowStockProduct {
  id: number;
  name: string;
  stock: number;
  threshold: number;
}

const AdminDashboard: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const { isMockMode } = useDataEnvironment();
  const [stats] = useState<DashboardStats>({
    totalRevenue: 45678.90,
    totalOrders: 1234,
    totalProducts: 156,
    totalUsers: 2890,
    pendingOrders: 23,
    lowStockProducts: 8,
    revenueGrowth: 12.5,
    orderGrowth: 8.3,
    // Enhanced stats
    activeCoupons: 12,
    totalReferrals: 89,
    pendingReviews: 15,
    shippingIssues: 3,
    socialShares: 234,
    inventorySyncStatus: 'synced',
    lastSyncTime: new Date().toISOString()
  });
  const [loading] = useState(false);

  // Sample data for recent orders
  const [recentOrders] = useState<RecentOrder[]>([
    {
      id: 'ORD-001',
      customer: 'John Doe',
      amount: 129.99,
      status: 'pending',
      date: '2024-01-15T10:30:00Z'
    },
    {
      id: 'ORD-002',
      customer: 'Jane Smith',
      amount: 89.50,
      status: 'processing',
      date: '2024-01-15T09:15:00Z'
    },
    {
      id: 'ORD-003',
      customer: 'Mike Johnson',
      amount: 199.99,
      status: 'shipped',
      date: '2024-01-14T16:45:00Z'
    },
    {
      id: 'ORD-004',
      customer: 'Sarah Wilson',
      amount: 75.25,
      status: 'delivered',
      date: '2024-01-14T14:20:00Z'
    }
  ]);

  // Sample data for low stock products
  const [lowStockProducts] = useState<LowStockProduct[]>([
    {
      id: 1,
      name: 'Organic Turmeric Powder',
      stock: 3,
      threshold: 10
    },
    {
      id: 2,
      name: 'Himalayan Pink Salt',
      stock: 7,
      threshold: 15
    },
    {
      id: 3,
      name: 'Raw Honey',
      stock: 2,
      threshold: 8
    },
    {
      id: 4,
      name: 'Coconut Oil',
      stock: 5,
      threshold: 12
    }
  ]);

  // Table columns for recent orders
  const orderColumns: TableColumn<RecentOrder>[] = [
    {
      key: 'id',
      title: 'Order ID',
      render: (value) => (
        <Link to={`/admin/orders/${value}`} className="text-indigo-600 hover:text-indigo-900 font-medium">
          {value}
        </Link>
      )
    },
    {
      key: 'customer',
      title: 'Customer',
      render: (value) => <span className="font-medium text-gray-900">{value}</span>
    },
    {
      key: 'amount',
      title: 'Amount',
      align: 'right',
      render: (value) => <span className="font-medium">${value.toFixed(2)}</span>
    },
    {
      key: 'status',
      title: 'Status',
      render: (value) => {
        const statusColors = {
          pending: 'bg-yellow-100 text-yellow-800',
          processing: 'bg-blue-100 text-blue-800',
          shipped: 'bg-indigo-100 text-indigo-800',
          delivered: 'bg-green-100 text-green-800'
        };
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[value as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}`}>
            {value}
          </span>
        );
      }
    },
    {
      key: 'date',
      title: 'Date',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  // Table columns for low stock products
  const productColumns: TableColumn<LowStockProduct>[] = [
    {
      key: 'name',
      title: 'Product Name',
      render: (value, record) => (
        <Link to={`/admin/products/${record.id}`} className="text-indigo-600 hover:text-indigo-900 font-medium">
          {value}
        </Link>
      )
    },
    {
      key: 'stock',
      title: 'Current Stock',
      align: 'center',
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
          {value} left
        </span>
      )
    },
    {
      key: 'threshold',
      title: 'Threshold',
      align: 'center',
      render: (value) => <span className="text-gray-600">{value}</span>
    }
  ];

  return (
    <AdminLayout>
      <AdminPageHeader
        title="Dashboard"
        subtitle={`Welcome back, ${user?.firstName}! Here's what's happening with your store.`}
        stats={[
          {
            label: 'Total Revenue',
            value: `$${stats.totalRevenue.toLocaleString()}`,
            change: { value: stats.revenueGrowth, type: 'increase' }
          },
          {
            label: 'Total Orders',
            value: stats.totalOrders.toLocaleString(),
            change: { value: stats.orderGrowth, type: 'increase' }
          },
          {
            label: 'Total Products',
            value: stats.totalProducts.toLocaleString()
          },
          {
            label: 'Total Users',
            value: stats.totalUsers.toLocaleString()
          }
        ]}
        actions={[
          {
            label: 'Add Product',
            href: '/admin/products/new',
            variant: 'primary',
            icon: PlusIcon
          },
          {
            label: 'View Analytics',
            href: '/admin/analytics',
            variant: 'secondary',
            icon: ChartBarIcon
          }
        ]}
      />

      {/* Data Environment Indicator */}
      <div className="mb-6">
        <DataModeIndicator showToggle={true} showDetails={false} />
      </div>

      {/* Mock Data Manager - Only show when in mock mode */}
      {isMockMode && (
        <div className="mb-6">
          <MockDataManager />
        </div>
      )}

      {/* Enhanced Dashboard Widgets */}
      <div className="space-y-6">
        {/* Enhanced Quick Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Inventory Sync Status */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inventory Sync</p>
                <div className="flex items-center mt-2">
                  <div className={`w-3 h-3 rounded-full mr-2 ${
                    stats.inventorySyncStatus === 'synced' ? 'bg-green-500' :
                    stats.inventorySyncStatus === 'syncing' ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`} />
                  <p className="text-2xl font-bold text-gray-900 capitalize">
                    {stats.inventorySyncStatus}
                  </p>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Last sync: {new Date(stats.lastSyncTime).toLocaleTimeString()}
                </p>
              </div>
              <ArrowPathIcon className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          {/* Active Coupons */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Coupons</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeCoupons}</p>
                <p className="text-xs text-green-600 mt-1">
                  <span className="inline-flex items-center">
                    <GiftIcon className="h-3 w-3 mr-1" />
                    Discount system active
                  </span>
                </p>
              </div>
              <GiftIcon className="h-8 w-8 text-purple-600" />
            </div>
          </div>

          {/* Total Referrals */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Referrals</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalReferrals}</p>
                <p className="text-xs text-blue-600 mt-1">
                  <span className="inline-flex items-center">
                    <ShareIcon className="h-3 w-3 mr-1" />
                    Referral program active
                  </span>
                </p>
              </div>
              <UserGroupIcon className="h-8 w-8 text-indigo-600" />
            </div>
          </div>

          {/* Pending Reviews */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingReviews}</p>
                <p className="text-xs text-orange-600 mt-1">
                  <span className="inline-flex items-center">
                    <StarIcon className="h-3 w-3 mr-1" />
                    Awaiting moderation
                  </span>
                </p>
              </div>
              <StarIcon className="h-8 w-8 text-yellow-600" />
            </div>
          </div>
        </div>

        {/* Alert Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Shipping Issues Alert */}
          {stats.shippingIssues > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2" />
                <h3 className="text-sm font-medium text-red-800">Shipping Issues</h3>
              </div>
              <p className="text-sm text-red-700 mt-1">
                {stats.shippingIssues} orders have shipping issues that need attention.
              </p>
              <button className="text-sm text-red-600 hover:text-red-800 font-medium mt-2">
                View Details →
              </button>
            </div>
          )}

          {/* Low Stock Alert */}
          {stats.lowStockProducts > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
                <h3 className="text-sm font-medium text-yellow-800">Low Stock Alert</h3>
              </div>
              <p className="text-sm text-yellow-700 mt-1">
                {stats.lowStockProducts} products are running low on stock.
              </p>
              <button className="text-sm text-yellow-600 hover:text-yellow-800 font-medium mt-2">
                Manage Inventory →
              </button>
            </div>
          )}

          {/* Social Media Activity */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <ShareIcon className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-sm font-medium text-blue-800">Social Activity</h3>
            </div>
            <p className="text-sm text-blue-700 mt-1">
              {stats.socialShares} social shares this month. Great engagement!
            </p>
            <button className="text-sm text-blue-600 hover:text-blue-800 font-medium mt-2">
              View Analytics →
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Recent Orders</h3>
              <Link
                to="/admin/orders"
                className="text-sm text-indigo-600 hover:text-indigo-900 font-medium flex items-center"
              >
                <EyeIcon className="h-4 w-4 mr-1" />
                View All
              </Link>
            </div>
          </div>
          <AdminDataTable
            columns={orderColumns}
            data={recentOrders}
            loading={loading}
            emptyText="No recent orders"
          />
        </div>

        {/* Low Stock Products */}
        <LowStockWidget
          lowStockThreshold={10}
          maxItems={5}
        />
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            to="/admin/products/new"
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow text-center group"
          >
            <CubeIcon className="h-8 w-8 text-indigo-600 mx-auto mb-2 group-hover:text-indigo-700" />
            <p className="font-medium text-gray-900">Add Product</p>
          </Link>

          <Link
            to="/admin/orders"
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow text-center group"
          >
            <ShoppingBagIcon className="h-8 w-8 text-blue-600 mx-auto mb-2 group-hover:text-blue-700" />
            <p className="font-medium text-gray-900">Manage Orders</p>
          </Link>

          <Link
            to="/admin/users"
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow text-center group"
          >
            <UserGroupIcon className="h-8 w-8 text-green-600 mx-auto mb-2 group-hover:text-green-700" />
            <p className="font-medium text-gray-900">View Users</p>
          </Link>

          <Link
            to="/admin/analytics"
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow text-center group"
          >
            <ChartBarIcon className="h-8 w-8 text-purple-600 mx-auto mb-2 group-hover:text-purple-700" />
            <p className="font-medium text-gray-900">View Analytics</p>
          </Link>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
