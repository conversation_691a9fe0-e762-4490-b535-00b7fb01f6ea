module.exports = {
  apps: [
    {
      name: 'nirvana-testing',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend',
      instances: 1,
      exec_mode: 'fork',

      env: {
        NODE_ENV: 'testing',
        PORT: 5000
      },
      env_file: '.env',

      max_memory_restart: '512M',
      node_args: '--max-old-space-size=512',

      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/testing-error.log',
      out_file: './logs/testing-out.log',
      log_file: './logs/testing-combined.log',
      merge_logs: true,
      time: true,

      autorestart: true,
      max_restarts: 10,
      min_uptime: '30s',
      restart_delay: 3000,

      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'dist',
        '.git'
      ],

      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000
    }
  ]
};