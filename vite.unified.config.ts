import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'
import fs from 'fs'

// Plugin to create unified build structure
function unifiedBuildPlugin() {
  return {
    name: 'unified-build',
    writeBundle() {
      // This will run after the build is complete
      console.log('🔧 Creating unified build structure...')
    },
    generateBundle() {
      // This runs during the build process
      console.log('📦 Generating unified bundle...')
    }
  }
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isAdminBuild = process.env.VITE_BUILD_TARGET === 'admin'
  
  return {
    plugins: [
      react(),
      unifiedBuildPlugin()
    ],
    define: {
      'import.meta.env.VITE_ADMIN_MODE': JSON.stringify(isAdminBuild ? 'true' : 'false'),
      'import.meta.env.VITE_APP_MODE': JSON.stringify(isAdminBuild ? 'admin' : 'main'),
    },
    css: {
      postcss: './postcss.config.cjs',
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@admin': path.resolve(__dirname, './src/pages/admin'),
        '@components': path.resolve(__dirname, './src/components'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@services': path.resolve(__dirname, './src/services'),
        '@store': path.resolve(__dirname, './src/store'),
        '@types': path.resolve(__dirname, './src/types')
      }
    },
    build: {
      outDir: isAdminBuild ? 'dist-temp-admin' : 'dist-temp-main',
      emptyOutDir: true,
      // Set base path for admin builds
      base: isAdminBuild ? '/admin/' : '/',
      // Browser compatibility target
      target: ['es2020', 'chrome120', 'firefox115', 'safari16', 'edge120'],
      rollupOptions: {
        input: isAdminBuild
          ? path.resolve(__dirname, 'admin.html')
          : path.resolve(__dirname, 'index.html'),
        output: {
          manualChunks: {
            // Vendor chunks
            vendor: ['react', 'react-dom'],
            router: ['react-router-dom'],
            redux: ['@reduxjs/toolkit', 'react-redux'],
            ui: ['@headlessui/react', '@heroicons/react'],
          },
          // Different asset naming for admin vs main
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            const ext = info[info.length - 1]
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `assets/images/[name]-[hash][extname]`
            }
            return `assets/[name]-[hash][extname]`
          },
          chunkFileNames: `assets/[name]-[hash].js`,
          entryFileNames: `assets/[name]-[hash].js`
        }
      },
      // Optimize chunk size
      chunkSizeWarningLimit: 1000,
      // Enable source maps for debugging
      sourcemap: mode === 'development',
      // CSS code splitting
      cssCodeSplit: true,
      // Minification
      minify: mode === 'production' ? 'esbuild' : false,
      // Asset inlining threshold
      assetsInlineLimit: 4096
    },
    // Performance optimizations
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@reduxjs/toolkit',
        'react-redux',
        '@headlessui/react',
        '@heroicons/react/24/outline',
        '@heroicons/react/24/solid',
        'axios',
        'dayjs'
      ]
    },
    // Development server optimizations
    server: {
      port: isAdminBuild ? 5174 : 5173,
      host: true,
      // Enable HMR
      hmr: true,
      // Optimize file watching
      watch: {
        usePolling: false,
        interval: 100
      },
      proxy: {
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false
        }
      }
    },
    preview: {
      port: isAdminBuild ? 4174 : 4173,
      host: true
    }
  }
})
