/**
 * File Management Utilities for Hostinger Hosting
 * Handles file operations, cleanup, and optimization
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const sharp = require('sharp');

class FileManager {
  constructor() {
    this.uploadPath = this.getUploadPath();
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE) || (5 * 1024 * 1024); // Use env var or default to 5MB

    // Get allowed file types from environment
    const allowedTypesString = process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,webp,gif,pdf';
    const allowedExtensions = allowedTypesString.split(',').map(ext => ext.trim());

    this.allowedImageTypes = [];
    this.allowedDocumentTypes = [];

    // Parse allowed types into image and document categories
    allowedExtensions.forEach(ext => {
      switch(ext.toLowerCase()) {
        case 'jpg': case 'jpeg':
          this.allowedImageTypes.push('image/jpeg');
          break;
        case 'png':
          this.allowedImageTypes.push('image/png');
          break;
        case 'webp':
          this.allowedImageTypes.push('image/webp');
          break;
        case 'gif':
          this.allowedImageTypes.push('image/gif');
          break;
        case 'pdf':
          this.allowedDocumentTypes.push('application/pdf');
          break;
        case 'doc':
          this.allowedDocumentTypes.push('application/msword');
          break;
        case 'docx':
          this.allowedDocumentTypes.push('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
          break;
        default:
          // Assume it's an image type if not recognized
          this.allowedImageTypes.push(`image/${ext}`);
      }
    });
  }

  /**
   * Get upload path based on environment
   */
  getUploadPath() {
    if (process.env.NODE_ENV === 'production') {
      return process.env.UPLOAD_PATH || path.join(process.cwd(), 'public/uploads');
    }
    return path.join(__dirname, '../../public/uploads');
  }

  /**
   * Ensure directory exists
   */
  async ensureDirectory(dirPath) {
    try {
      await fs.access(dirPath);
    } catch (error) {
      await fs.mkdir(dirPath, { recursive: true, mode: 0o755 });
      console.log(`Created directory: ${dirPath}`);
    }
  }

  /**
   * Generate unique filename
   */
  generateUniqueFilename(originalName, prefix = '') {
    const timestamp = Date.now();
    const random = Math.round(Math.random() * 1E9);
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext).toLowerCase().replace(/[^a-z0-9]/g, '-');
    
    return `${prefix}${baseName}-${timestamp}-${random}${ext}`;
  }

  /**
   * Validate file type and size
   */
  validateFile(file, type = 'image') {
    const errors = [];

    // Check file size
    if (file.size > this.maxFileSize) {
      errors.push(`File size exceeds ${this.maxFileSize / (1024 * 1024)}MB limit`);
    }

    // Check file type
    const allowedTypes = type === 'image' ? this.allowedImageTypes : this.allowedDocumentTypes;
    if (!allowedTypes.includes(file.mimetype)) {
      errors.push(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Optimize image for web
   */
  async optimizeImage(inputPath, outputPath, options = {}) {
    const defaultOptions = {
      width: 1200,
      height: 1200,
      quality: 85,
      format: 'webp'
    };

    const config = { ...defaultOptions, ...options };

    try {
      await sharp(inputPath)
        .resize(config.width, config.height, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .webp({ quality: config.quality })
        .toFile(outputPath);

      return true;
    } catch (error) {
      console.error('Image optimization failed:', error);
      return false;
    }
  }

  /**
   * Create multiple image sizes (thumbnails, medium, large)
   */
  async createImageVariants(inputPath, baseName, outputDir) {
    const variants = {
      thumbnail: { width: 150, height: 150, quality: 80 },
      medium: { width: 500, height: 500, quality: 85 },
      large: { width: 1200, height: 1200, quality: 90 }
    };

    const results = {};

    for (const [size, options] of Object.entries(variants)) {
      const outputPath = path.join(outputDir, `${baseName}-${size}.webp`);
      const success = await this.optimizeImage(inputPath, outputPath, options);
      
      if (success) {
        results[size] = path.relative(this.uploadPath, outputPath);
      }
    }

    return results;
  }

  /**
   * Save uploaded file with optimization
   */
  async saveFile(file, category = 'products', optimize = true) {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Ensure category directory exists
      const categoryDir = path.join(this.uploadPath, category);
      await this.ensureDirectory(categoryDir);

      // Generate unique filename
      const filename = this.generateUniqueFilename(file.originalname, `${category}-`);
      const filePath = path.join(categoryDir, filename);

      // Save original file
      await fs.writeFile(filePath, file.buffer);

      let result = {
        original: path.relative(this.uploadPath, filePath),
        filename: filename,
        size: file.size,
        mimetype: file.mimetype
      };

      // Optimize image if requested and file is an image
      if (optimize && file.mimetype.startsWith('image/')) {
        const baseName = path.basename(filename, path.extname(filename));
        const variants = await this.createImageVariants(filePath, baseName, categoryDir);
        result.variants = variants;
      }

      return result;
    } catch (error) {
      console.error('File save failed:', error);
      throw error;
    }
  }

  /**
   * Delete file and its variants
   */
  async deleteFile(filePath) {
    try {
      const fullPath = path.join(this.uploadPath, filePath);
      
      // Delete main file
      if (fsSync.existsSync(fullPath)) {
        await fs.unlink(fullPath);
        console.log(`Deleted file: ${fullPath}`);
      }

      // Delete variants if they exist
      const dir = path.dirname(fullPath);
      const baseName = path.basename(fullPath, path.extname(fullPath));
      const variants = ['thumbnail', 'medium', 'large'];

      for (const variant of variants) {
        const variantPath = path.join(dir, `${baseName}-${variant}.webp`);
        if (fsSync.existsSync(variantPath)) {
          await fs.unlink(variantPath);
          console.log(`Deleted variant: ${variantPath}`);
        }
      }

      return true;
    } catch (error) {
      console.error('File deletion failed:', error);
      return false;
    }
  }

  /**
   * Clean up orphaned files (files not referenced in database)
   */
  async cleanupOrphanedFiles(referencedFiles = []) {
    try {
      const uploadDirs = ['products', 'categories', 'banners', 'documents'];
      let deletedCount = 0;

      for (const dir of uploadDirs) {
        const dirPath = path.join(this.uploadPath, dir);
        
        if (!fsSync.existsSync(dirPath)) continue;

        const files = await fs.readdir(dirPath);
        
        for (const file of files) {
          const filePath = path.join(dir, file);
          
          // Skip if file is referenced
          if (referencedFiles.includes(filePath)) continue;

          // Delete orphaned file
          await this.deleteFile(filePath);
          deletedCount++;
        }
      }

      console.log(`Cleaned up ${deletedCount} orphaned files`);
      return deletedCount;
    } catch (error) {
      console.error('Cleanup failed:', error);
      return 0;
    }
  }

  /**
   * Get file URL for frontend
   */
  getFileUrl(filePath) {
    if (!filePath) return null;
    
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    return `${baseUrl}/uploads/${filePath}`;
  }

  /**
   * Get disk usage statistics
   */
  async getDiskUsage() {
    try {
      const stats = await this.getDirectorySize(this.uploadPath);
      return {
        totalSize: stats.size,
        totalFiles: stats.files,
        formattedSize: this.formatBytes(stats.size)
      };
    } catch (error) {
      console.error('Failed to get disk usage:', error);
      return null;
    }
  }

  /**
   * Get directory size recursively
   */
  async getDirectorySize(dirPath) {
    let totalSize = 0;
    let totalFiles = 0;

    try {
      const items = await fs.readdir(dirPath);

      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);

        if (stats.isDirectory()) {
          const subStats = await this.getDirectorySize(itemPath);
          totalSize += subStats.size;
          totalFiles += subStats.files;
        } else {
          totalSize += stats.size;
          totalFiles++;
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
    }

    return { size: totalSize, files: totalFiles };
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }
}

module.exports = new FileManager();
