const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

/**
 * Social Media Analytics Model
 * Stores daily/weekly/monthly analytics data for social media accounts
 */
const SocialMediaAnalytics = sequelize.define('SocialMediaAnalytics', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // Account reference
  accountId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'account_id',
    references: {
      model: 'social_media_accounts',
      key: 'id'
    }
  },
  
  // Time period
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    field: 'date'
  },
  
  period: {
    type: DataTypes.ENUM('daily', 'weekly', 'monthly'),
    allowNull: false,
    field: 'period'
  },
  
  // Follower metrics
  followersCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'followers_count'
  },
  
  followersGrowth: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'followers_growth'
  },
  
  followingCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'following_count'
  },
  
  // Engagement metrics
  totalLikes: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_likes'
  },
  
  totalComments: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_comments'
  },
  
  totalShares: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_shares'
  },
  
  totalSaves: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_saves'
  },
  
  // Reach and impressions
  totalReach: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_reach'
  },
  
  totalImpressions: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_impressions'
  },
  
  uniqueReach: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'unique_reach'
  },
  
  // Content metrics
  postsCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'posts_count'
  },
  
  storiesCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'stories_count'
  },
  
  videosCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'videos_count'
  },
  
  // Engagement rates
  engagementRate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    field: 'engagement_rate'
  },
  
  likeRate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    field: 'like_rate'
  },
  
  commentRate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    field: 'comment_rate'
  },
  
  shareRate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    field: 'share_rate'
  },
  
  // Audience demographics
  audienceDemographics: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'audience_demographics',
    comment: 'Age, gender, location breakdown'
  },
  
  // Top performing content
  topPosts: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'top_posts',
    comment: 'Array of top performing post IDs and metrics'
  },
  
  // Hashtag performance
  topHashtags: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'top_hashtags',
    comment: 'Array of best performing hashtags'
  },
  
  // Optimal posting times
  bestPostingTimes: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'best_posting_times',
    comment: 'Optimal hours and days for posting'
  },
  
  // Platform-specific metrics
  platformSpecificMetrics: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'platform_specific_metrics',
    comment: 'Platform-unique metrics (e.g., retweets for Twitter)'
  },
  
  // Website traffic from social
  websiteClicks: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'website_clicks'
  },
  
  profileViews: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'profile_views'
  },
  
  // Competitor analysis
  competitorMetrics: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'competitor_metrics',
    comment: 'Comparison with competitor accounts'
  },
  
  // Data quality
  dataCompleteness: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 1.00,
    field: 'data_completeness',
    comment: 'Percentage of complete data (0.00 to 1.00)'
  },
  
  lastUpdated: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'last_updated'
  }
}, {
  tableName: 'social_media_analytics',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['account_id', 'date', 'period']
    },
    {
      fields: ['account_id']
    },
    {
      fields: ['date']
    },
    {
      fields: ['period']
    },
    {
      fields: ['engagement_rate']
    }
  ]
});

// Instance methods
SocialMediaAnalytics.prototype.calculateEngagementRate = function() {
  if (this.totalImpressions === 0) return 0;
  const totalEngagements = this.totalLikes + this.totalComments + this.totalShares;
  this.engagementRate = (totalEngagements / this.totalImpressions) * 100;
  return this.engagementRate;
};

SocialMediaAnalytics.prototype.calculateGrowthRate = function(previousPeriod) {
  if (!previousPeriod || previousPeriod.followersCount === 0) return 0;
  return ((this.followersCount - previousPeriod.followersCount) / previousPeriod.followersCount) * 100;
};

SocialMediaAnalytics.prototype.getPerformanceScore = function() {
  // Calculate a composite performance score (0-100)
  const engagementScore = Math.min(this.engagementRate * 10, 40); // Max 40 points
  const reachScore = Math.min((this.totalReach / this.followersCount) * 30, 30); // Max 30 points
  const growthScore = Math.min(Math.abs(this.followersGrowth) / 10, 30); // Max 30 points
  
  return Math.round(engagementScore + reachScore + growthScore);
};

// Class methods
SocialMediaAnalytics.findByAccount = function(accountId, startDate, endDate, period = 'daily') {
  const whereClause = {
    accountId,
    period
  };
  
  if (startDate && endDate) {
    whereClause.date = {
      [sequelize.Sequelize.Op.between]: [startDate, endDate]
    };
  }
  
  return this.findAll({
    where: whereClause,
    order: [['date', 'ASC']]
  });
};

SocialMediaAnalytics.getAccountSummary = function(accountId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAll({
    where: {
      accountId,
      period: 'daily',
      date: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    attributes: [
      [sequelize.fn('AVG', sequelize.col('engagement_rate')), 'avgEngagementRate'],
      [sequelize.fn('SUM', sequelize.col('total_likes')), 'totalLikes'],
      [sequelize.fn('SUM', sequelize.col('total_comments')), 'totalComments'],
      [sequelize.fn('SUM', sequelize.col('total_shares')), 'totalShares'],
      [sequelize.fn('SUM', sequelize.col('total_reach')), 'totalReach'],
      [sequelize.fn('SUM', sequelize.col('posts_count')), 'totalPosts'],
      [sequelize.fn('MAX', sequelize.col('followers_count')), 'currentFollowers'],
      [sequelize.fn('MIN', sequelize.col('followers_count')), 'startingFollowers']
    ],
    raw: true
  });
};

SocialMediaAnalytics.getTopPerformingContent = function(accountId, days = 30, limit = 10) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAll({
    where: {
      accountId,
      period: 'daily',
      date: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    order: [['engagement_rate', 'DESC']],
    limit
  });
};

SocialMediaAnalytics.getGrowthTrend = function(accountId, period = 'daily', days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAll({
    where: {
      accountId,
      period,
      date: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    attributes: ['date', 'followersCount', 'followersGrowth', 'engagementRate'],
    order: [['date', 'ASC']]
  });
};

SocialMediaAnalytics.getBenchmarkData = function(platform, period = 'monthly', months = 6) {
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - months);
  
  return sequelize.query(`
    SELECT 
      AVG(engagement_rate) as avg_engagement_rate,
      AVG(followers_growth) as avg_growth_rate,
      PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY engagement_rate) as median_engagement_rate,
      PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY engagement_rate) as top_quartile_engagement_rate
    FROM social_media_analytics sma
    JOIN social_media_accounts sma2 ON sma.account_id = sma2.id
    WHERE sma2.platform = :platform 
    AND sma.period = :period 
    AND sma.date >= :startDate
  `, {
    replacements: { platform, period, startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

module.exports = SocialMediaAnalytics;
