const { User, Newsletter, EmailCampaign, EmailTemplate } = require('../models');
const { getUsersBySegment } = require('./notificationController');
const emailService = require('../services/emailService');
const { Op } = require('sequelize');

// Subscribe to newsletter
const subscribeToNewsletter = async (req, res) => {
  try {
    const { email, preferences = {} } = req.body;
    const userId = req.user?.id;

    // Check if email already exists
    let subscription = await Newsletter.findOne({ where: { email } });

    if (subscription) {
      if (subscription.isSubscribed) {
        return res.status(400).json({
          success: false,
          message: '<PERSON><PERSON> is already subscribed to newsletter'
        });
      } else {
        // Reactivate subscription
        await subscription.update({
          isSubscribed: true,
          subscribedAt: new Date(),
          preferences,
          userId
        });
      }
    } else {
      // Create new subscription
      subscription = await Newsletter.create({
        email,
        userId,
        isSubscribed: true,
        subscribedAt: new Date(),
        preferences
      });
    }

    // Send welcome email
    try {
      await emailService.sendWelcomeEmail(email);
    } catch (emailError) {
      console.error('Welcome email error:', emailError);
    }

    res.json({
      success: true,
      message: 'Successfully subscribed to newsletter',
      data: { subscription }
    });

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to subscribe to newsletter',
      error: error.message
    });
  }
};

// Unsubscribe from newsletter
const unsubscribeFromNewsletter = async (req, res) => {
  try {
    const { email, token } = req.body;

    const subscription = await Newsletter.findOne({ where: { email } });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Email not found in newsletter subscriptions'
      });
    }

    await subscription.update({
      isSubscribed: false,
      unsubscribedAt: new Date()
    });

    res.json({
      success: true,
      message: 'Successfully unsubscribed from newsletter'
    });

  } catch (error) {
    console.error('Newsletter unsubscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unsubscribe from newsletter',
      error: error.message
    });
  }
};

// Send promotional email (Admin only)
const sendPromotionalEmail = async (req, res) => {
  try {
    const { 
      subject, 
      htmlContent, 
      textContent, 
      recipients, 
      segment, 
      templateId,
      scheduledAt 
    } = req.body;

    let targetEmails = [];

    if (recipients && recipients.length > 0) {
      // Send to specific recipients
      targetEmails = recipients;
    } else if (segment) {
      // Send to user segment
      if (segment === 'newsletter') {
        const subscriptions = await Newsletter.findAll({
          where: { isSubscribed: true },
          attributes: ['email']
        });
        targetEmails = subscriptions.map(sub => sub.email);
      } else {
        const users = await getUsersBySegment(segment);
        targetEmails = users.map(user => user.email);
      }
    } else {
      // Send to all newsletter subscribers
      const subscriptions = await Newsletter.findAll({
        where: { isSubscribed: true },
        attributes: ['email']
      });
      targetEmails = subscriptions.map(sub => sub.email);
    }

    // Create email campaign record
    const campaign = await EmailCampaign.create({
      subject,
      htmlContent,
      textContent,
      recipientCount: targetEmails.length,
      segment: segment || 'custom',
      status: scheduledAt ? 'scheduled' : 'sending',
      scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
      createdBy: req.user.id
    });

    if (scheduledAt && new Date(scheduledAt) > new Date()) {
      // Schedule email for later
      res.json({
        success: true,
        message: `Email campaign scheduled for ${new Date(scheduledAt).toLocaleString()}`,
        data: { campaign }
      });
      return;
    }

    // Send emails immediately
    const sendPromises = targetEmails.map(async (email) => {
      try {
        await emailService.sendPromotionalEmail({
          to: email,
          subject,
          htmlContent,
          textContent,
          campaignId: campaign.id
        });
        return { email, status: 'sent' };
      } catch (error) {
        console.error(`Failed to send email to ${email}:`, error);
        return { email, status: 'failed', error: error.message };
      }
    });

    const results = await Promise.all(sendPromises);
    
    const successCount = results.filter(r => r.status === 'sent').length;
    const failureCount = results.filter(r => r.status === 'failed').length;

    // Update campaign status
    await campaign.update({
      status: 'completed',
      sentCount: successCount,
      failedCount: failureCount,
      sentAt: new Date()
    });

    res.json({
      success: true,
      message: `Email campaign sent to ${successCount} recipients, ${failureCount} failed`,
      data: {
        campaign,
        totalSent: successCount,
        totalFailed: failureCount,
        results
      }
    });

  } catch (error) {
    console.error('Send promotional email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send promotional email',
      error: error.message
    });
  }
};

// Get email campaigns (Admin only)
const getEmailCampaigns = async (req, res) => {
  try {
    const { page = 1, limit = 20, status = 'all' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};
    if (status !== 'all') {
      whereClause.status = status;
    }

    const campaigns = await EmailCampaign.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        campaigns: campaigns.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(campaigns.count / limit),
          totalCampaigns: campaigns.count,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get email campaigns error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email campaigns',
      error: error.message
    });
  }
};

// Get newsletter subscribers (Admin only)
const getNewsletterSubscribers = async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = { isSubscribed: true };
    
    if (search) {
      whereClause.email = { [Op.iLike]: `%${search}%` };
    }

    const subscribers = await Newsletter.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName'],
          required: false
        }
      ],
      order: [['subscribedAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        subscribers: subscribers.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(subscribers.count / limit),
          totalSubscribers: subscribers.count,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get newsletter subscribers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch newsletter subscribers',
      error: error.message
    });
  }
};

// Get email marketing statistics (Admin only)
const getEmailStats = async (req, res) => {
  try {
    const totalSubscribers = await Newsletter.count({
      where: { isSubscribed: true }
    });

    const totalCampaigns = await EmailCampaign.count();

    const campaignStats = await EmailCampaign.findOne({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('sentCount')), 'totalSent'],
        [sequelize.fn('SUM', sequelize.col('failedCount')), 'totalFailed'],
        [sequelize.fn('AVG', sequelize.col('sentCount')), 'averageSent']
      ]
    });

    // Get recent subscriber growth
    const subscriberGrowth = await Newsletter.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('subscribedAt')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        subscribedAt: {
          [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      group: [sequelize.fn('DATE', sequelize.col('subscribedAt'))],
      order: [[sequelize.fn('DATE', sequelize.col('subscribedAt')), 'ASC']]
    });

    res.json({
      success: true,
      data: {
        totalSubscribers,
        totalCampaigns,
        totalSent: parseInt(campaignStats?.dataValues?.totalSent || 0),
        totalFailed: parseInt(campaignStats?.dataValues?.totalFailed || 0),
        averageSent: parseFloat(campaignStats?.dataValues?.averageSent || 0),
        subscriberGrowth
      }
    });

  } catch (error) {
    console.error('Get email stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email marketing statistics',
      error: error.message
    });
  }
};

module.exports = {
  subscribeToNewsletter,
  unsubscribeFromNewsletter,
  sendPromotionalEmail,
  getEmailCampaigns,
  getNewsletterSubscribers,
  getEmailStats
};
