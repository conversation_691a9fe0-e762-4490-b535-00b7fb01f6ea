#!/usr/bin/env node

/**
 * Add More Sample Products to Nirvana Organics Database
 * Expands the product catalog with additional CBD products
 */

const { sequelize, models, initializeDatabase } = require('../server/models/database');
const { log } = require('./generate-sample-data');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Additional Products Data
const additionalProducts = [
  {
    name: 'CBD Oil Tincture - 1000mg',
    slug: 'cbd-oil-tincture-1000mg',
    description: 'Premium full-spectrum CBD oil tincture with 1000mg CBD. Made from organic hemp and MCT oil for optimal absorption.',
    shortDescription: 'Full-spectrum CBD oil tincture, 1000mg strength',
    price: 79.99,
    comparePrice: 99.99,
    costPrice: 40.00,
    sku: 'CBD-OIL-1000-001',
    barcode: '123456789003',
    trackQuantity: true,
    quantity: 50,
    lowStockThreshold: 5,
    weight: 30,
    weightUnit: 'g',
    dimensions: { length: 12, width: 4, height: 4 },
    brand: 'Nirvana Organics',
    vendor: 'Pure Extract Co.',
    tags: ['cbd', 'oil', 'tincture', 'full-spectrum', '1000mg'],
    images: [
      {
        url: '/images/products/cbd-oil-1000mg-1.jpg',
        alt: 'CBD Oil Tincture - 1000mg',
        position: 0
      }
    ],
    variants: [
      { name: '500mg', price: 49.99, sku: 'CBD-OIL-500-001' },
      { name: '1000mg', price: 79.99, sku: 'CBD-OIL-1000-001' },
      { name: '2000mg', price: 139.99, sku: 'CBD-OIL-2000-001' }
    ],
    attributes: {
      cbdContent: '1000mg',
      servingSize: '1ml',
      servingsPerContainer: '30',
      carrier: 'MCT Oil',
      extraction: 'CO2',
      spectrum: 'Full Spectrum',
      thcContent: '<0.3%',
      labTested: true,
      organic: true
    },
    isActive: true,
    isFeatured: true,
    categorySlug: 'cbd-oils-tinctures'
  },
  {
    name: 'CBD Topical Cream - Pain Relief',
    slug: 'cbd-topical-cream-pain-relief',
    description: 'Soothing CBD topical cream with menthol and arnica for targeted pain relief. Perfect for muscle soreness and joint discomfort.',
    shortDescription: 'CBD pain relief cream with menthol and arnica',
    price: 34.99,
    comparePrice: 44.99,
    costPrice: 18.00,
    sku: 'CBD-CREAM-PR-001',
    barcode: '123456789004',
    trackQuantity: true,
    quantity: 75,
    lowStockThreshold: 10,
    weight: 60,
    weightUnit: 'g',
    dimensions: { length: 8, width: 8, height: 5 },
    brand: 'Nirvana Organics',
    vendor: 'Topical Solutions Inc.',
    tags: ['cbd', 'topical', 'cream', 'pain-relief', 'menthol'],
    images: [
      {
        url: '/images/products/cbd-cream-pain-relief-1.jpg',
        alt: 'CBD Topical Cream - Pain Relief',
        position: 0
      }
    ],
    variants: [
      { name: '2oz', price: 34.99, sku: 'CBD-CREAM-PR-001-2OZ' },
      { name: '4oz', price: 59.99, sku: 'CBD-CREAM-PR-001-4OZ' }
    ],
    attributes: {
      cbdContent: '500mg',
      size: '2oz',
      ingredients: ['CBD Extract', 'Menthol', 'Arnica', 'Shea Butter'],
      application: 'Topical',
      labTested: true,
      vegan: true,
      crueltyfree: true
    },
    isActive: true,
    isFeatured: false,
    categorySlug: 'cbd-topicals'
  },
  {
    name: 'CBD Flower - OG Kush',
    slug: 'cbd-flower-og-kush',
    description: 'Classic OG Kush CBD flower with relaxing indica effects. Perfect for evening use with earthy, pine flavors.',
    shortDescription: 'Relaxing OG Kush CBD flower with indica effects',
    price: 32.99,
    comparePrice: 42.99,
    costPrice: 16.00,
    sku: 'CBD-FLOWER-OG-001',
    barcode: '123456789005',
    trackQuantity: true,
    quantity: 80,
    lowStockThreshold: 10,
    weight: 3.5,
    weightUnit: 'g',
    dimensions: { length: 10, width: 8, height: 2 },
    brand: 'Nirvana Organics',
    vendor: 'Premium Hemp Co.',
    tags: ['cbd', 'flower', 'indica', 'relaxing', 'og-kush'],
    images: [
      {
        url: '/images/products/cbd-flower-og-kush-1.jpg',
        alt: 'CBD Flower - OG Kush',
        position: 0
      }
    ],
    variants: [
      { name: '1g', price: 14.99, sku: 'CBD-FLOWER-OG-001-1G' },
      { name: '3.5g', price: 32.99, sku: 'CBD-FLOWER-OG-001-3.5G' },
      { name: '7g', price: 59.99, sku: 'CBD-FLOWER-OG-001-7G' }
    ],
    attributes: {
      strain: 'OG Kush',
      type: 'Indica Dominant',
      cbdContent: '20-24%',
      thcContent: '<0.3%',
      terpenes: ['Myrcene', 'Limonene', 'Caryophyllene'],
      effects: ['Relaxing', 'Calming', 'Sleep'],
      labTested: true
    },
    isActive: true,
    isFeatured: false,
    categorySlug: 'cbd-flower'
  },
  {
    name: 'CBD Chocolate Bars - Dark Chocolate',
    slug: 'cbd-chocolate-bars-dark-chocolate',
    description: 'Rich dark chocolate infused with premium CBD. Each bar contains 100mg CBD divided into 10 easy-to-dose squares.',
    shortDescription: 'Dark chocolate CBD bars, 100mg total CBD',
    price: 24.99,
    comparePrice: 29.99,
    costPrice: 12.00,
    sku: 'CBD-CHOC-DARK-001',
    barcode: '123456789006',
    trackQuantity: true,
    quantity: 60,
    lowStockThreshold: 15,
    weight: 85,
    weightUnit: 'g',
    dimensions: { length: 15, width: 8, height: 1 },
    brand: 'Nirvana Organics',
    vendor: 'Artisan Chocolates Co.',
    tags: ['cbd', 'edibles', 'chocolate', 'dark-chocolate', 'premium'],
    images: [
      {
        url: '/images/products/cbd-chocolate-dark-1.jpg',
        alt: 'CBD Chocolate Bars - Dark Chocolate',
        position: 0
      }
    ],
    variants: [
      { name: 'Dark Chocolate', price: 24.99, sku: 'CBD-CHOC-DARK-001' },
      { name: 'Milk Chocolate', price: 24.99, sku: 'CBD-CHOC-MILK-001' },
      { name: 'White Chocolate', price: 24.99, sku: 'CBD-CHOC-WHITE-001' }
    ],
    attributes: {
      cbdPerBar: '100mg',
      cbdPerSquare: '10mg',
      squares: '10',
      cocoaContent: '70%',
      ingredients: ['Organic Cocoa', 'CBD Extract', 'Organic Sugar'],
      vegan: true,
      glutenFree: false,
      labTested: true
    },
    isActive: true,
    isFeatured: false,
    categorySlug: 'cbd-edibles'
  },
  {
    name: 'Glass Water Pipe - 12 inch',
    slug: 'glass-water-pipe-12-inch',
    description: 'High-quality borosilicate glass water pipe with percolator for smooth hits. Perfect for dry herbs.',
    shortDescription: 'Premium glass water pipe with percolator',
    price: 89.99,
    comparePrice: 119.99,
    costPrice: 45.00,
    sku: 'PIPE-GLASS-12-001',
    barcode: '123456789007',
    trackQuantity: true,
    quantity: 25,
    lowStockThreshold: 5,
    weight: 450,
    weightUnit: 'g',
    dimensions: { length: 30, width: 15, height: 15 },
    brand: 'Nirvana Organics',
    vendor: 'Glass Works Studio',
    tags: ['accessories', 'glass', 'water-pipe', 'percolator', 'premium'],
    images: [
      {
        url: '/images/products/glass-water-pipe-12-1.jpg',
        alt: 'Glass Water Pipe - 12 inch',
        position: 0
      }
    ],
    variants: [
      { name: '8 inch', price: 59.99, sku: 'PIPE-GLASS-8-001' },
      { name: '12 inch', price: 89.99, sku: 'PIPE-GLASS-12-001' },
      { name: '16 inch', price: 129.99, sku: 'PIPE-GLASS-16-001' }
    ],
    attributes: {
      material: 'Borosilicate Glass',
      height: '12 inches',
      percolator: 'Tree Percolator',
      jointSize: '14mm',
      thickness: '5mm',
      includes: ['Bowl', 'Downstem'],
      handmade: true
    },
    isActive: true,
    isFeatured: false,
    categorySlug: 'accessories'
  }
];

/**
 * Add additional products to database
 */
async function addMoreProducts() {
  log.info('🛍️ Adding More Products to Nirvana Organics...');
  log.info('===============================================');

  try {
    // Initialize database
    const dbInitialized = await initializeDatabase();
    if (!dbInitialized) {
      throw new Error('Database initialization failed');
    }

    // Get categories
    const categories = await models.Category.findAll();
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.slug] = cat.id;
    });

    let addedCount = 0;
    let existingCount = 0;

    for (const productData of additionalProducts) {
      const categoryId = categoryMap[productData.categorySlug];
      if (!categoryId) {
        log.warning(`Category not found for slug: ${productData.categorySlug}`);
        continue;
      }

      const [product, created] = await models.Product.findOrCreate({
        where: { slug: productData.slug },
        defaults: {
          ...productData,
          categoryId,
          categorySlug: undefined // Remove this field as it's not in the model
        }
      });

      if (created) {
        log.success(`Added product: ${product.name}`);
        addedCount++;
      } else {
        log.info(`Product already exists: ${product.name}`);
        existingCount++;
      }
    }

    log.info('===============================================');
    log.success(`🎉 Product addition completed!`);
    log.info(`   ✅ Added: ${addedCount} new products`);
    log.info(`   ℹ️ Existing: ${existingCount} products`);
    log.info(`   📦 Total attempted: ${additionalProducts.length} products`);

    return true;
  } catch (error) {
    log.error(`Failed to add products: ${error.message}`);
    return false;
  } finally {
    await sequelize.close();
    log.info('Database connection closed');
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const success = await addMoreProducts();
    
    if (success) {
      log.success('✅ Product addition completed successfully!');
      process.exit(0);
    } else {
      log.error('❌ Product addition failed!');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Product addition failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { addMoreProducts, additionalProducts };
