/**
 * Debug Middleware
 * Provides debugging functionality based on environment variables
 */

const logger = require('../utils/logger');

/**
 * Debug mode middleware
 * Adds debug information to responses when DEBUG_MODE is enabled
 */
const debugMode = (req, res, next) => {
  if (process.env.DEBUG_MODE !== 'true') {
    return next();
  }

  // Store original res.json
  const originalJson = res.json;
  
  // Override res.json to add debug information
  res.json = function(data) {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      responseTime: Date.now() - req.startTime,
      statusCode: res.statusCode,
      headers: req.headers,
      query: req.query,
      params: req.params
    };

    // Add debug info to response if it's an object
    if (typeof data === 'object' && data !== null) {
      data._debug = debugInfo;
    }

    return originalJson.call(this, data);
  };

  // Track request start time
  req.startTime = Date.now();
  
  next();
};

/**
 * CORS debug middleware
 * Logs CORS-related information when ENABLE_CORS_DEBUG is enabled
 */
const corsDebug = (req, res, next) => {
  if (process.env.ENABLE_CORS_DEBUG !== 'true') {
    return next();
  }

  const origin = req.get('Origin');
  const method = req.method;
  const headers = req.get('Access-Control-Request-Headers');

  if (origin) {
    console.log(`🌐 CORS Debug - Origin: ${origin}, Method: ${method}`);
    
    if (headers) {
      console.log(`🌐 CORS Debug - Requested Headers: ${headers}`);
    }

    // Log CORS response headers
    res.on('finish', () => {
      const corsHeaders = {
        'Access-Control-Allow-Origin': res.get('Access-Control-Allow-Origin'),
        'Access-Control-Allow-Methods': res.get('Access-Control-Allow-Methods'),
        'Access-Control-Allow-Headers': res.get('Access-Control-Allow-Headers'),
        'Access-Control-Allow-Credentials': res.get('Access-Control-Allow-Credentials')
      };
      
      console.log('🌐 CORS Debug - Response Headers:', corsHeaders);
    });
  }

  next();
};

/**
 * SQL logging middleware
 * Enables SQL query logging when ENABLE_SQL_LOGGING is enabled
 */
const sqlLogging = () => {
  if (process.env.ENABLE_SQL_LOGGING !== 'true') {
    return false;
  }

  return {
    logging: (sql, timing) => {
      const duration = timing ? `(${timing}ms)` : '';
      console.log(`🗄️ SQL Debug ${duration}:`, sql);
      
      // Also log to file if logger is available
      if (logger && logger.debug) {
        logger.debug('SQL Query', { sql, timing });
      }
    }
  };
};

/**
 * Request logging middleware
 * Logs detailed request information in debug mode
 */
const requestLogging = (req, res, next) => {
  if (process.env.DEBUG_MODE !== 'true') {
    return next();
  }

  const startTime = Date.now();
  
  console.log(`📥 ${req.method} ${req.originalUrl} - ${req.ip}`);
  
  if (Object.keys(req.query).length > 0) {
    console.log('📥 Query:', req.query);
  }
  
  if (req.body && Object.keys(req.body).length > 0) {
    // Don't log sensitive information
    const sanitizedBody = { ...req.body };
    if (sanitizedBody.password) sanitizedBody.password = '[REDACTED]';
    if (sanitizedBody.token) sanitizedBody.token = '[REDACTED]';
    if (sanitizedBody.secret) sanitizedBody.secret = '[REDACTED]';
    
    console.log('📥 Body:', sanitizedBody);
  }

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const statusColor = res.statusCode >= 400 ? '🔴' : res.statusCode >= 300 ? '🟡' : '🟢';
    console.log(`📤 ${statusColor} ${res.statusCode} ${req.method} ${req.originalUrl} - ${duration}ms`);
  });

  next();
};

/**
 * Performance debugging middleware
 * Tracks and logs performance metrics
 */
const performanceDebug = (req, res, next) => {
  if (process.env.DEBUG_MODE !== 'true') {
    return next();
  }

  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    const memoryDiff = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external
    };

    if (duration > 1000 || memoryDiff.heapUsed > 1024 * 1024) { // Log slow requests or high memory usage
      console.log(`⚡ Performance Debug - ${req.method} ${req.originalUrl}:`);
      console.log(`   Duration: ${duration.toFixed(2)}ms`);
      console.log(`   Memory Delta: ${(memoryDiff.heapUsed / 1024 / 1024).toFixed(2)}MB heap`);
    }
  });

  next();
};

/**
 * Error debugging middleware
 * Provides detailed error information in debug mode
 */
const errorDebug = (err, req, res, next) => {
  if (process.env.DEBUG_MODE === 'true') {
    console.error('🐛 Debug Error Details:');
    console.error('   URL:', req.originalUrl);
    console.error('   Method:', req.method);
    console.error('   IP:', req.ip);
    console.error('   User Agent:', req.get('User-Agent'));
    console.error('   Stack:', err.stack);
    
    if (req.body && Object.keys(req.body).length > 0) {
      const sanitizedBody = { ...req.body };
      if (sanitizedBody.password) sanitizedBody.password = '[REDACTED]';
      console.error('   Body:', sanitizedBody);
    }
  }

  next(err);
};

/**
 * Debug information endpoint
 * Provides system debug information when debug mode is enabled
 */
const debugInfo = (req, res) => {
  if (process.env.DEBUG_MODE !== 'true') {
    return res.status(404).json({ error: 'Debug mode not enabled' });
  }

  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  const debugData = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
    platform: process.platform,
    uptime: process.uptime(),
    memory: {
      rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
      heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
      heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
      external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    environmentVariables: {
      DEBUG_MODE: process.env.DEBUG_MODE,
      ENABLE_CORS_DEBUG: process.env.ENABLE_CORS_DEBUG,
      ENABLE_SQL_LOGGING: process.env.ENABLE_SQL_LOGGING,
      LOG_LEVEL: process.env.LOG_LEVEL,
      NODE_ENV: process.env.NODE_ENV
    }
  };

  res.json(debugData);
};

/**
 * Initialize debug middleware based on environment variables
 */
const initializeDebugMiddleware = (app) => {
  if (process.env.DEBUG_MODE === 'true') {
    console.log('🐛 Debug mode enabled');
    app.use(debugMode);
    app.use(requestLogging);
    app.use(performanceDebug);
  }

  if (process.env.ENABLE_CORS_DEBUG === 'true') {
    console.log('🌐 CORS debug enabled');
    app.use(corsDebug);
  }

  // Add debug endpoint
  app.get('/api/debug', debugInfo);
};

module.exports = {
  debugMode,
  corsDebug,
  sqlLogging,
  requestLogging,
  performanceDebug,
  errorDebug,
  debugInfo,
  initializeDebugMiddleware
};
