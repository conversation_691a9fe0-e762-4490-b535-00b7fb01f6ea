#!/usr/bin/env node

/**
 * Comprehensive Database Seeding Script for Nirvana Organics
 * Seeds all sample data including users, products, orders, reviews, etc.
 */

const { sequelize, models, initializeDatabase } = require('../server/models/database');
const { sampleUsers, sampleCategories, sampleProducts, log } = require('./generate-sample-data');
const bcrypt = require('bcrypt');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

/**
 * Seed Users
 */
async function seedUsers() {
  log.step('Seeding users...');
  
  try {
    const createdUsers = [];
    
    for (const userData of sampleUsers) {
      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      
      const [user, created] = await models.User.findOrCreate({
        where: { email: userData.email },
        defaults: {
          ...userData,
          password: hashedPassword
        }
      });
      
      if (created) {
        log.success(`Created user: ${user.firstName} ${user.lastName} (${user.email})`);
      } else {
        log.info(`User already exists: ${user.email}`);
      }
      
      createdUsers.push(user);
    }
    
    return createdUsers;
  } catch (error) {
    log.error(`Failed to seed users: ${error.message}`);
    throw error;
  }
}

/**
 * Seed Categories
 */
async function seedCategories() {
  log.step('Seeding categories...');
  
  try {
    const createdCategories = [];
    
    for (const categoryData of sampleCategories) {
      const [category, created] = await models.Category.findOrCreate({
        where: { slug: categoryData.slug },
        defaults: categoryData
      });
      
      if (created) {
        log.success(`Created category: ${category.name}`);
      } else {
        log.info(`Category already exists: ${category.name}`);
      }
      
      createdCategories.push(category);
    }
    
    return createdCategories;
  } catch (error) {
    log.error(`Failed to seed categories: ${error.message}`);
    throw error;
  }
}

/**
 * Seed Products
 */
async function seedProducts(categories) {
  log.step('Seeding products...');
  
  try {
    const createdProducts = [];
    
    for (const productData of sampleProducts) {
      // Find appropriate category
      let categoryId;
      if (productData.name.includes('Flower')) {
        categoryId = categories.find(c => c.slug === 'cbd-flower')?.id;
      } else if (productData.name.includes('Gummies')) {
        categoryId = categories.find(c => c.slug === 'cbd-edibles')?.id;
      } else if (productData.name.includes('Oil') || productData.name.includes('Tincture')) {
        categoryId = categories.find(c => c.slug === 'cbd-oils-tinctures')?.id;
      } else if (productData.name.includes('Cream') || productData.name.includes('Balm')) {
        categoryId = categories.find(c => c.slug === 'cbd-topicals')?.id;
      } else {
        categoryId = categories.find(c => c.slug === 'accessories')?.id;
      }
      
      const [product, created] = await models.Product.findOrCreate({
        where: { slug: productData.slug },
        defaults: {
          ...productData,
          categoryId
        }
      });
      
      if (created) {
        log.success(`Created product: ${product.name}`);
      } else {
        log.info(`Product already exists: ${product.name}`);
      }
      
      createdProducts.push(product);
    }
    
    return createdProducts;
  } catch (error) {
    log.error(`Failed to seed products: ${error.message}`);
    throw error;
  }
}

/**
 * Seed Addresses
 */
async function seedAddresses(users) {
  log.step('Seeding addresses...');
  
  try {
    const sampleAddresses = [
      {
        userId: users[0].id, // John Smith
        type: 'billing',
        firstName: 'John',
        lastName: 'Smith',
        address1: '123 Main Street',
        address2: 'Apt 4B',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'US',
        phone: '******-0101',
        isDefault: true
      },
      {
        userId: users[0].id, // John Smith shipping
        type: 'shipping',
        firstName: 'John',
        lastName: 'Smith',
        address1: '456 Oak Avenue',
        city: 'Beverly Hills',
        state: 'CA',
        zipCode: '90211',
        country: 'US',
        phone: '******-0101',
        isDefault: true
      },
      {
        userId: users[1].id, // Sarah Johnson
        type: 'billing',
        firstName: 'Sarah',
        lastName: 'Johnson',
        address1: '789 Pine Street',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94102',
        country: 'US',
        phone: '******-0102',
        isDefault: true
      }
    ];
    
    for (const addressData of sampleAddresses) {
      const [address, created] = await models.Address.findOrCreate({
        where: {
          userId: addressData.userId,
          type: addressData.type,
          address1: addressData.address1
        },
        defaults: addressData
      });
      
      if (created) {
        log.success(`Created address for user ${addressData.userId}`);
      }
    }
    
    return true;
  } catch (error) {
    log.error(`Failed to seed addresses: ${error.message}`);
    throw error;
  }
}

/**
 * Seed Orders
 */
async function seedOrders(users, products) {
  log.step('Seeding orders...');
  
  try {
    const sampleOrders = [
      {
        orderNumber: 'NRV-2025-001',
        userId: users[0].id, // John Smith
        email: users[0].email,
        status: 'delivered',
        paymentStatus: 'paid',
        fulfillmentStatus: 'fulfilled',
        subtotal: 69.98,
        tax: 5.60,
        shipping: 9.99,
        total: 85.57,
        currency: 'USD',
        billingAddress: {
          firstName: 'John',
          lastName: 'Smith',
          address1: '123 Main Street',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'US'
        },
        shippingAddress: {
          firstName: 'John',
          lastName: 'Smith',
          address1: '456 Oak Avenue',
          city: 'Beverly Hills',
          state: 'CA',
          zipCode: '90211',
          country: 'US'
        },
        paymentMethod: 'credit_card',
        paymentDetails: {
          last4: '4242',
          brand: 'visa'
        },
        shippingMethod: 'standard',
        trackingNumber: 'TRK123456789',
        processedAt: new Date('2025-01-15'),
        shippedAt: new Date('2025-01-16'),
        deliveredAt: new Date('2025-01-18')
      },
      {
        orderNumber: 'NRV-2025-002',
        userId: users[1].id, // Sarah Johnson
        email: users[1].email,
        status: 'processing',
        paymentStatus: 'paid',
        fulfillmentStatus: 'unfulfilled',
        subtotal: 39.99,
        tax: 3.20,
        shipping: 9.99,
        total: 53.18,
        currency: 'USD',
        billingAddress: {
          firstName: 'Sarah',
          lastName: 'Johnson',
          address1: '789 Pine Street',
          city: 'San Francisco',
          state: 'CA',
          zipCode: '94102',
          country: 'US'
        },
        shippingAddress: {
          firstName: 'Sarah',
          lastName: 'Johnson',
          address1: '789 Pine Street',
          city: 'San Francisco',
          state: 'CA',
          zipCode: '94102',
          country: 'US'
        },
        paymentMethod: 'credit_card',
        paymentDetails: {
          last4: '1234',
          brand: 'mastercard'
        },
        shippingMethod: 'express',
        processedAt: new Date('2025-01-20')
      }
    ];
    
    const createdOrders = [];
    
    for (const orderData of sampleOrders) {
      const [order, created] = await models.Order.findOrCreate({
        where: { orderNumber: orderData.orderNumber },
        defaults: orderData
      });
      
      if (created) {
        log.success(`Created order: ${order.orderNumber}`);
      }
      
      createdOrders.push(order);
    }
    
    return createdOrders;
  } catch (error) {
    log.error(`Failed to seed orders: ${error.message}`);
    throw error;
  }
}

/**
 * Seed Order Items
 */
async function seedOrderItems(orders, products) {
  log.step('Seeding order items...');
  
  try {
    const orderItems = [
      // Order 1 items
      {
        orderId: orders[0].id,
        productId: products[0].id, // CBD Flower
        name: products[0].name,
        sku: products[0].sku,
        quantity: 2,
        price: 29.99,
        total: 59.98,
        fulfillmentStatus: 'fulfilled'
      },
      {
        orderId: orders[0].id,
        productId: products[1].id, // CBD Gummies
        name: products[1].name,
        sku: products[1].sku,
        quantity: 1,
        price: 39.99,
        total: 39.99,
        fulfillmentStatus: 'fulfilled'
      },
      // Order 2 items
      {
        orderId: orders[1].id,
        productId: products[1].id, // CBD Gummies
        name: products[1].name,
        sku: products[1].sku,
        quantity: 1,
        price: 39.99,
        total: 39.99,
        fulfillmentStatus: 'unfulfilled'
      }
    ];
    
    for (const itemData of orderItems) {
      const [item, created] = await models.OrderItem.findOrCreate({
        where: {
          orderId: itemData.orderId,
          productId: itemData.productId
        },
        defaults: itemData
      });
      
      if (created) {
        log.success(`Created order item: ${item.name}`);
      }
    }
    
    return true;
  } catch (error) {
    log.error(`Failed to seed order items: ${error.message}`);
    throw error;
  }
}

/**
 * Seed Reviews
 */
async function seedReviews(users, products, orders) {
  log.step('Seeding reviews...');

  try {
    const sampleReviews = [
      {
        userId: users[0].id, // John Smith
        productId: products[0].id, // CBD Flower
        orderId: orders[0].id,
        rating: 5,
        title: 'Excellent quality!',
        comment: 'This CBD flower is amazing! Great taste and effects. Highly recommend.',
        isVerifiedPurchase: true,
        isApproved: true,
        helpfulCount: 3
      },
      {
        userId: users[0].id, // John Smith
        productId: products[1].id, // CBD Gummies
        orderId: orders[0].id,
        rating: 4,
        title: 'Tasty and effective',
        comment: 'Love the berry flavor. Effects are noticeable and long-lasting.',
        isVerifiedPurchase: true,
        isApproved: true,
        helpfulCount: 2
      },
      {
        userId: users[2].id, // Michael Brown
        productId: products[0].id, // CBD Flower
        rating: 5,
        title: 'Premium quality',
        comment: 'Best CBD flower I\'ve tried. Will definitely order again.',
        isVerifiedPurchase: false,
        isApproved: true,
        helpfulCount: 1
      }
    ];

    for (const reviewData of sampleReviews) {
      const [review, created] = await models.Review.findOrCreate({
        where: {
          userId: reviewData.userId,
          productId: reviewData.productId
        },
        defaults: reviewData
      });

      if (created) {
        log.success(`Created review by user ${reviewData.userId} for product ${reviewData.productId}`);
      }
    }

    return true;
  } catch (error) {
    log.error(`Failed to seed reviews: ${error.message}`);
    throw error;
  }
}

/**
 * Seed Newsletter Subscriptions
 */
async function seedNewsletterSubscriptions(users) {
  log.step('Seeding newsletter subscriptions...');

  try {
    const subscriptions = [
      {
        email: users[0].email,
        userId: users[0].id,
        isSubscribed: true,
        preferences: { productUpdates: true, promotions: true }
      },
      {
        email: users[1].email,
        userId: users[1].id,
        isSubscribed: true,
        preferences: { productUpdates: true, promotions: false }
      },
      {
        email: '<EMAIL>',
        isSubscribed: true,
        preferences: { productUpdates: true, promotions: true }
      }
    ];

    for (const subData of subscriptions) {
      const [subscription, created] = await models.Newsletter.findOrCreate({
        where: { email: subData.email },
        defaults: subData
      });

      if (created) {
        log.success(`Created newsletter subscription: ${subscription.email}`);
      }
    }

    return true;
  } catch (error) {
    log.error(`Failed to seed newsletter subscriptions: ${error.message}`);
    throw error;
  }
}

/**
 * Main seeding function
 */
async function seedAllData() {
  log.info('🌱 Starting Comprehensive Database Seeding...');
  log.info('================================================');

  try {
    // Initialize database
    const dbInitialized = await initializeDatabase();
    if (!dbInitialized) {
      throw new Error('Database initialization failed');
    }

    // Seed data in order
    const users = await seedUsers();
    const categories = await seedCategories();
    const products = await seedProducts(categories);
    await seedAddresses(users);
    const orders = await seedOrders(users, products);
    await seedOrderItems(orders, products);
    await seedReviews(users, products, orders);
    await seedNewsletterSubscriptions(users);

    log.info('================================================');
    log.success('🎉 Comprehensive database seeding completed successfully!');
    log.highlight('Your Nirvana Organics e-commerce database is ready with:');
    log.info(`   👥 ${users.length} users (including admin and manager)`);
    log.info(`   📂 ${categories.length} product categories`);
    log.info(`   🛍️ ${products.length} products`);
    log.info(`   📍 Multiple addresses`);
    log.info(`   📦 ${orders.length} sample orders`);
    log.info(`   ⭐ Multiple product reviews`);
    log.info(`   📧 Newsletter subscriptions`);
    log.info('');
    log.highlight('Admin Credentials:');
    log.info('   Email: <EMAIL>');
    log.info('   Password: AdminPass123!');
    log.info('');
    log.highlight('Manager Credentials:');
    log.info('   Email: <EMAIL>');
    log.info('   Password: ManagerPass123!');
    log.info('');
    log.highlight('Sample Customer Credentials:');
    log.info('   Email: <EMAIL>');
    log.info('   Password: SecurePass123!');

    return true;
  } catch (error) {
    log.error(`Database seeding failed: ${error.message}`);
    return false;
  } finally {
    await sequelize.close();
    log.info('Database connection closed');
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const success = await seedAllData();

    if (success) {
      log.success('✅ All done! Your e-commerce database is ready for production.');
      process.exit(0);
    } else {
      log.error('❌ Seeding failed!');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Seeding failed: ${error.message}`);
    process.exit(1);
  }
}

// Run seeding if called directly
if (require.main === module) {
  main();
}

module.exports = {
  seedUsers,
  seedCategories,
  seedProducts,
  seedAddresses,
  seedOrders,
  seedOrderItems,
  seedReviews,
  seedNewsletterSubscriptions,
  seedAllData
};
