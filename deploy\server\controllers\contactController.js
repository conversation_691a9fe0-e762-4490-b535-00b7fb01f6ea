const { validationResult } = require('express-validator');
const emailService = require('../services/emailService');
const performanceMonitor = require('../utils/performanceMonitor');

/**
 * Handle contact form submission
 */
const submitContactForm = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, email, phone, subject, message, inquiryType } = req.body;

    // Track contact form submission
    performanceMonitor.trackEvent('contact_form_submission', {
      inquiryType: inquiryType || 'general',
      hasPhone: !!phone
    });

    // Send contact form email to customer service
    await emailService.sendContactFormEmail({
      name,
      email,
      phone,
      subject,
      message,
      inquiryType: inquiryType || 'general'
    });

    // Send confirmation email to customer
    await emailService.sendEmail({
      to: email,
      subject: 'Thank you for contacting Nirvana Organics',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Thank You for Contacting Us!</h1>
          </div>
          
          <div style="padding: 40px; background: #f9fafb;">
            <h2 style="color: #16a34a; margin-bottom: 20px;">Hi ${name},</h2>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              Thank you for reaching out to Nirvana Organics. We've received your message and our customer service team will respond within 24 hours.
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #374151; margin-top: 0;">Your Message Summary</h3>
              <p><strong>Subject:</strong> ${subject}</p>
              <p><strong>Inquiry Type:</strong> ${inquiryType || 'General'}</p>
              <p><strong>Message:</strong></p>
              <p style="color: #6b7280; font-style: italic; padding: 10px; background: #f9fafb; border-radius: 4px;">${message}</p>
            </div>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              In the meantime, you can:
            </p>
            
            <ul style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              <li>Browse our <a href="${process.env.FRONTEND_URL}/shop" style="color: #16a34a;">product catalog</a></li>
              <li>Check our <a href="${process.env.FRONTEND_URL}/faq" style="color: #16a34a;">frequently asked questions</a></li>
              <li>Follow us on social media for updates and promotions</li>
            </ul>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL}/shop" 
                 style="display: inline-block; background: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                Shop Now
              </a>
            </div>
          </div>
          
          <div style="background: #374151; padding: 20px; text-align: center;">
            <p style="color: #9ca3af; margin: 0; font-size: 14px;">
              © ${new Date().getFullYear()} Nirvana Organics. All rights reserved.
            </p>
            <p style="color: #9ca3af; margin: 5px 0 0 0; font-size: 12px;">
              This is an automated confirmation. Please do not reply to this email.
            </p>
          </div>
        </div>
      `,
      text: `
        Thank You for Contacting Nirvana Organics!
        
        Hi ${name},
        
        Thank you for reaching out to Nirvana Organics. We've received your message and our customer service team will respond within 24 hours.
        
        Your Message Summary:
        - Subject: ${subject}
        - Inquiry Type: ${inquiryType || 'General'}
        - Message: ${message}
        
        In the meantime, you can:
        - Browse our product catalog: ${process.env.FRONTEND_URL}/shop
        - Check our FAQ: ${process.env.FRONTEND_URL}/faq
        - Follow us on social media for updates and promotions
        
        © ${new Date().getFullYear()} Nirvana Organics. All rights reserved.
        This is an automated confirmation. Please do not reply to this email.
      `
    }, emailService.EMAIL_TYPES.CUSTOMER_SERVICE);

    console.log('✅ Contact form submitted successfully:', {
      name,
      email,
      subject,
      inquiryType: inquiryType || 'general'
    });

    res.json({
      success: true,
      message: 'Thank you for your message! We\'ll get back to you within 24 hours.',
      data: {
        submittedAt: new Date().toISOString(),
        inquiryType: inquiryType || 'general'
      }
    });

  } catch (error) {
    console.error('❌ Contact form submission failed:', error);
    
    // Track error
    performanceMonitor.trackEvent('contact_form_error', {
      error: error.message,
      inquiryType: req.body.inquiryType || 'general'
    });

    res.status(500).json({
      success: false,
      message: 'Failed to send your message. Please try again or contact us directly.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get contact information
 */
const getContactInfo = async (req, res) => {
  try {
    const contactInfo = {
      phone: '+****************',
      email: '<EMAIL>',
      address: {
        street: '123 Wellness Way',
        city: 'Denver',
        state: 'CO',
        zipCode: '80202',
        country: 'USA'
      },
      businessHours: {
        weekdays: 'Mon-Fri: 9AM-6PM EST',
        weekends: 'Sat-Sun: 10AM-4PM EST'
      },
      socialMedia: {
        facebook: 'https://facebook.com/nirvanaorganics',
        instagram: 'https://instagram.com/nirvanaorganics',
        twitter: 'https://twitter.com/nirvanaorganics'
      }
    };

    res.json({
      success: true,
      data: contactInfo
    });

  } catch (error) {
    console.error('❌ Failed to get contact info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve contact information'
    });
  }
};

module.exports = {
  submitContactForm,
  getContactInfo
};
