<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - {{orderNumber}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #28a745;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        .order-number {
            font-size: 24px;
            color: #28a745;
            font-weight: bold;
            margin: 20px 0;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #28a745;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        .item-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .item-row:last-child {
            border-bottom: none;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: bold;
            color: #333;
        }
        .item-sku {
            font-size: 12px;
            color: #666;
        }
        .item-price {
            font-weight: bold;
            color: #28a745;
        }
        .pricing-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }
        .pricing-row.total {
            border-top: 2px solid #28a745;
            font-weight: bold;
            font-size: 18px;
            color: #28a745;
            margin-top: 10px;
            padding-top: 15px;
        }
        .discount {
            color: #dc3545;
        }
        .address {
            background-color: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            background-color: #28a745;
            color: white;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
        }
        .contact-info {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 10px 0;
        }
        .btn:hover {
            background-color: #218838;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .item-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .item-price {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🌿 Nirvana Organics</div>
            <h1>Order Confirmation</h1>
            <div class="order-number">Order #{{orderNumber}}</div>
            <span class="status-badge">Confirmed</span>
        </div>

        <!-- Customer Greeting -->
        <div style="margin-bottom: 30px;">
            <h2>Thank you, {{customerName}}!</h2>
            <p>Your order has been received and is being processed. We'll send you another email when your order ships.</p>
            <p><strong>Order Date:</strong> {{orderDate}}</p>
            <p><strong>Estimated Delivery:</strong> {{estimatedDelivery}}</p>
        </div>

        <!-- Order Items -->
        <div class="section">
            <h3>📦 Order Items</h3>
            {{#each items}}
            <div class="item-row">
                <div class="item-details">
                    <div class="item-name">{{productName}}</div>
                    <div class="item-sku">SKU: {{sku}}</div>
                    <div>Quantity: {{quantity}}</div>
                </div>
                <div class="item-price">${{total}}</div>
            </div>
            {{/each}}
        </div>

        <!-- Pricing Summary -->
        <div class="section">
            <h3>💰 Order Summary</h3>
            <div class="pricing-row">
                <span>Subtotal:</span>
                <span>${{subtotal}}</span>
            </div>
            {{#if discount}}
            <div class="pricing-row discount">
                <span>Discount:</span>
                <span>-${{discount}}</span>
            </div>
            {{/if}}
            <div class="pricing-row">
                <span>Tax:</span>
                <span>${{tax}}</span>
            </div>
            <div class="pricing-row">
                <span>Shipping ({{shippingMethod}}):</span>
                <span>${{shipping}}</span>
            </div>
            <div class="pricing-row total">
                <span>Total:</span>
                <span>${{orderTotal}}</span>
            </div>
        </div>

        <!-- Shipping Information -->
        <div class="section">
            <h3>🚚 Shipping Information</h3>
            <p><strong>Shipping Method:</strong> {{shippingMethod}}</p>
            <p><strong>Estimated Delivery:</strong> {{estimatedDelivery}}</p>
            <div class="address">
                <strong>Shipping Address:</strong><br>
                {{shippingAddress}}
            </div>
        </div>

        <!-- Contact Information -->
        <div class="contact-info">
            <h3>📞 Need Help?</h3>
            <p>If you have any questions about your order, please don't hesitate to contact us:</p>
            <p>
                <strong>Email:</strong> <a href="mailto:{{supportEmail}}">{{supportEmail}}</a><br>
                <strong>Phone:</strong> 1-800-NIRVANA<br>
                <strong>Hours:</strong> Monday - Friday, 9 AM - 6 PM EST
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Thank you for choosing {{companyName}}!</p>
            <p>🌿 Premium organic products for your wellness journey 🌿</p>
            <p style="font-size: 12px; color: #999;">
                This email was sent to {{customerEmail}}. 
                If you have questions, reply to this email or contact us at {{supportEmail}}.
            </p>
        </div>
    </div>
</body>
</html>
