#!/usr/bin/env node

/**
 * Fix Admin Password Script
 * Updates the admin user password to ensure it works correctly
 */

const { sequelize, models, initializeDatabase } = require('../server/models/database');
const bcrypt = require('bcrypt');
const { log } = require('./generate-sample-data');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function fixAdminPassword() {
  log.info('🔧 Fixing Admin Password...');
  log.info('===========================');

  try {
    // Initialize database
    const dbInitialized = await initializeDatabase();
    if (!dbInitialized) {
      throw new Error('Database initialization failed');
    }

    // Find admin user
    const adminUser = await models.User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      log.error('❌ Admin user not found');
      return false;
    }

    log.info(`Found admin user: ${adminUser.email}`);

    // Hash the correct password
    const correctPassword = 'AdminPass123!';
    const hashedPassword = await bcrypt.hash(correctPassword, 12);

    // Update admin password
    await adminUser.update({ 
      password: hashedPassword,
      isEmailVerified: true,
      isActive: true
    });

    log.success('✅ Admin password updated successfully');
    log.info(`   Email: ${adminUser.email}`);
    log.info(`   Password: ${correctPassword}`);
    log.info(`   Role: ${adminUser.role}`);

    // Test the password
    const isPasswordValid = await bcrypt.compare(correctPassword, hashedPassword);
    if (isPasswordValid) {
      log.success('✅ Password verification successful');
    } else {
      log.error('❌ Password verification failed');
    }

    // Also fix manager password
    const managerUser = await models.User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (managerUser) {
      const managerPassword = 'ManagerPass123!';
      const hashedManagerPassword = await bcrypt.hash(managerPassword, 12);
      
      await managerUser.update({ 
        password: hashedManagerPassword,
        isEmailVerified: true,
        isActive: true
      });

      log.success('✅ Manager password updated successfully');
      log.info(`   Email: ${managerUser.email}`);
      log.info(`   Password: ${managerPassword}`);
    }

    return true;
  } catch (error) {
    log.error(`❌ Failed to fix admin password: ${error.message}`);
    return false;
  } finally {
    await sequelize.close();
    log.info('Database connection closed');
  }
}

async function main() {
  try {
    const success = await fixAdminPassword();
    
    if (success) {
      log.success('✅ Admin password fixed successfully!');
      process.exit(0);
    } else {
      log.error('❌ Failed to fix admin password!');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Script failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixAdminPassword };
