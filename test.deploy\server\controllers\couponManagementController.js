const { Coupon, User, Order, AuditLog } = require('../models');
const { Op, sequelize } = require('sequelize');
const { validationResult } = require('express-validator');

// Get all coupons with filtering and pagination
const getCoupons = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      type,
      status,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      dateFrom,
      dateTo,
      includeCreator = true
    } = req.query;

    // Build where clause
    const where = {};
    
    if (type && type !== 'all') {
      where.type = type;
    }
    
    if (status === 'active') {
      where.isActive = true;
      where[Op.or] = [
        { expiresAt: null },
        { expiresAt: { [Op.gt]: new Date() } }
      ];
    } else if (status === 'inactive') {
      where.isActive = false;
    } else if (status === 'expired') {
      where.expiresAt = { [Op.lt]: new Date() };
    }
    
    if (search) {
      where[Op.or] = [
        { code: { [Op.iLike]: `%${search}%` } },
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt[Op.gte] = new Date(dateFrom);
      if (dateTo) where.createdAt[Op.lte] = new Date(dateTo);
    }

    // Build include array
    const include = [];
    if (includeCreator === 'true') {
      include.push({
        model: User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'email']
      });
    }

    const { count, rows: coupons } = await Coupon.findAndCountAll({
      where,
      include,
      order: [[sortBy, sortOrder.toUpperCase()]],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit),
      distinct: true
    });

    // Add computed fields
    const couponsWithStatus = coupons.map(coupon => {
      const couponData = coupon.toJSON();
      couponData.isValid = coupon.isValid();
      couponData.isExpired = coupon.expiresAt && new Date() > coupon.expiresAt;
      couponData.usagePercentage = coupon.usageLimit ? 
        Math.round((coupon.usageCount / coupon.usageLimit) * 100) : 0;
      return couponData;
    });

    const totalPages = Math.ceil(count / parseInt(limit));

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_COUPONS', 'COUPON', null, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { filters: req.query }
    });

    res.json({
      success: true,
      data: {
        coupons: couponsWithStatus,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCoupons: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get coupons error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch coupons',
      error: error.message
    });
  }
};

// Get single coupon by ID
const getCouponById = async (req, res) => {
  try {
    const { couponId } = req.params;

    const coupon = await Coupon.findByPk(couponId, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    // Add computed fields
    const couponData = coupon.toJSON();
    couponData.isValid = coupon.isValid();
    couponData.isExpired = coupon.expiresAt && new Date() > coupon.expiresAt;
    couponData.usagePercentage = coupon.usageLimit ? 
      Math.round((coupon.usageCount / coupon.usageLimit) * 100) : 0;

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_COUPON', 'COUPON', couponId, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: { coupon: couponData }
    });

  } catch (error) {
    console.error('Get coupon by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch coupon',
      error: error.message
    });
  }
};

// Create new coupon
const createCoupon = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      code,
      name,
      description,
      type,
      value,
      minimumAmount,
      maximumAmount,
      usageLimit,
      userUsageLimit,
      isActive = true,
      startsAt,
      expiresAt,
      applicableProducts = [],
      applicableCategories = []
    } = req.body;

    // Check if coupon code already exists
    const existingCoupon = await Coupon.findOne({ 
      where: { code: code.toUpperCase() } 
    });
    
    if (existingCoupon) {
      return res.status(400).json({
        success: false,
        message: 'Coupon code already exists'
      });
    }

    // Create coupon
    const coupon = await Coupon.create({
      code: code.toUpperCase(),
      name,
      description,
      type,
      value,
      minimumAmount,
      maximumAmount,
      usageLimit,
      userUsageLimit,
      isActive,
      startsAt: startsAt ? new Date(startsAt) : null,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      applicableProducts,
      applicableCategories,
      createdBy: req.user.id
    });

    // Get created coupon with creator info
    const createdCoupon = await Coupon.findByPk(coupon.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    // Log admin action
    await AuditLog.logCreate(req.user.id, 'COUPON', coupon.id, createdCoupon.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.status(201).json({
      success: true,
      message: 'Coupon created successfully',
      data: { coupon: createdCoupon }
    });

  } catch (error) {
    console.error('Create coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create coupon',
      error: error.message
    });
  }
};

// Update coupon
const updateCoupon = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { couponId } = req.params;
    const updateData = req.body;

    // Find coupon
    const coupon = await Coupon.findByPk(couponId);
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    // Store old values for audit log
    const oldValues = coupon.toJSON();

    // Check if code is being changed and if it already exists
    if (updateData.code && updateData.code.toUpperCase() !== coupon.code) {
      const existingCoupon = await Coupon.findOne({ 
        where: { 
          code: updateData.code.toUpperCase(),
          id: { [Op.ne]: couponId }
        } 
      });
      
      if (existingCoupon) {
        return res.status(400).json({
          success: false,
          message: 'Coupon code already exists'
        });
      }
      
      updateData.code = updateData.code.toUpperCase();
    }

    // Convert date strings to Date objects
    if (updateData.startsAt) {
      updateData.startsAt = new Date(updateData.startsAt);
    }
    if (updateData.expiresAt) {
      updateData.expiresAt = new Date(updateData.expiresAt);
    }

    // Update coupon
    await coupon.update(updateData);

    // Get updated coupon data
    const updatedCoupon = await Coupon.findByPk(couponId, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    // Log admin action
    await AuditLog.logUpdate(req.user.id, 'COUPON', couponId, oldValues, updatedCoupon.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.json({
      success: true,
      message: 'Coupon updated successfully',
      data: { coupon: updatedCoupon }
    });

  } catch (error) {
    console.error('Update coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update coupon',
      error: error.message
    });
  }
};

// Delete coupon
const deleteCoupon = async (req, res) => {
  try {
    const { couponId } = req.params;

    // Find coupon
    const coupon = await Coupon.findByPk(couponId);
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    // Store old values for audit log
    const oldValues = coupon.toJSON();

    // Delete coupon
    await coupon.destroy();

    // Log admin action
    await AuditLog.logDelete(req.user.id, 'COUPON', couponId, oldValues, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'high'
    });

    res.json({
      success: true,
      message: 'Coupon deleted successfully'
    });

  } catch (error) {
    console.error('Delete coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete coupon',
      error: error.message
    });
  }
};

// Toggle coupon status (activate/deactivate)
const toggleCouponStatus = async (req, res) => {
  try {
    const { couponId } = req.params;

    // Find coupon
    const coupon = await Coupon.findByPk(couponId);
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    // Store old values for audit log
    const oldValues = coupon.toJSON();

    // Toggle status
    const newStatus = !coupon.isActive;
    await coupon.update({ isActive: newStatus });

    // Get updated coupon
    const updatedCoupon = await Coupon.findByPk(couponId);

    // Log admin action
    await AuditLog.logUpdate(req.user.id, 'COUPON', couponId, oldValues, updatedCoupon.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.json({
      success: true,
      message: `Coupon ${newStatus ? 'activated' : 'deactivated'} successfully`,
      data: { coupon: updatedCoupon }
    });

  } catch (error) {
    console.error('Toggle coupon status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle coupon status',
      error: error.message
    });
  }
};

// Bulk operations on coupons
const bulkUpdateCoupons = async (req, res) => {
  try {
    const { couponIds, action, data } = req.body;

    if (!couponIds || !Array.isArray(couponIds) || couponIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Coupon IDs array is required'
      });
    }

    let updateData = {};
    let actionName = '';

    switch (action) {
      case 'activate':
        updateData = { isActive: true };
        actionName = 'BULK_ACTIVATE';
        break;
      case 'deactivate':
        updateData = { isActive: false };
        actionName = 'BULK_DEACTIVATE';
        break;
      case 'delete':
        actionName = 'BULK_DELETE';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    let affectedCount = 0;

    if (action === 'delete') {
      // Delete coupons
      affectedCount = await Coupon.destroy({
        where: {
          id: { [Op.in]: couponIds }
        }
      });
    } else {
      // Update coupons
      const [updatedCount] = await Coupon.update(updateData, {
        where: {
          id: { [Op.in]: couponIds }
        }
      });
      affectedCount = updatedCount;
    }

    // Log admin action
    await AuditLog.logBulkAction(req.user.id, actionName, 'COUPON', couponIds, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { updateData },
      severity: 'high'
    });

    res.json({
      success: true,
      message: `Successfully ${action}d ${affectedCount} coupons`,
      data: { affectedCount }
    });

  } catch (error) {
    console.error('Bulk update coupons error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update coupons',
      error: error.message
    });
  }
};

// Validate coupon code
const validateCouponCode = async (req, res) => {
  try {
    const { code } = req.params;
    const { subtotal = 0, userId = null } = req.query;

    const coupon = await Coupon.findOne({
      where: { code: code.toUpperCase() }
    });

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    const isValid = coupon.isValid();
    const canBeUsed = userId ? await coupon.canBeUsedBy(userId) : true;
    const discount = coupon.calculateDiscount(parseFloat(subtotal));

    res.json({
      success: true,
      data: {
        coupon: {
          id: coupon.id,
          code: coupon.code,
          name: coupon.name,
          type: coupon.type,
          value: coupon.value,
          minimumAmount: coupon.minimumAmount,
          maximumAmount: coupon.maximumAmount
        },
        isValid,
        canBeUsed,
        discount,
        message: !isValid ? 'Coupon is not valid' :
                !canBeUsed ? 'Coupon usage limit exceeded' :
                'Coupon is valid'
      }
    });

  } catch (error) {
    console.error('Validate coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate coupon',
      error: error.message
    });
  }
};

// Get coupon statistics
const getCouponStatistics = async (req, res) => {
  try {
    const stats = await Promise.all([
      // Total coupons
      Coupon.count(),

      // Active coupons
      Coupon.count({ where: { isActive: true } }),

      // Coupons by type
      Coupon.findAll({
        attributes: [
          'type',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['type']
      }),

      // Expired coupons
      Coupon.count({
        where: {
          expiresAt: { [Op.lt]: new Date() }
        }
      }),

      // Coupons created this month
      Coupon.count({
        where: {
          createdAt: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),

      // Most used coupons
      Coupon.findAll({
        attributes: ['id', 'code', 'name', 'usageCount', 'usageLimit'],
        order: [['usageCount', 'DESC']],
        limit: 10
      }),

      // Total usage count
      Coupon.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('usageCount')), 'totalUsage']
        ]
      })
    ]);

    const [
      totalCoupons,
      activeCoupons,
      couponsByType,
      expiredCoupons,
      couponsThisMonth,
      mostUsedCoupons,
      totalUsageResult
    ] = stats;

    const totalUsage = totalUsageResult?.dataValues?.totalUsage || 0;

    res.json({
      success: true,
      data: {
        totalCoupons,
        activeCoupons,
        inactiveCoupons: totalCoupons - activeCoupons,
        expiredCoupons,
        couponsByType,
        couponsThisMonth,
        mostUsedCoupons,
        totalUsage
      }
    });

  } catch (error) {
    console.error('Get coupon statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch coupon statistics',
      error: error.message
    });
  }
};

module.exports = {
  getCoupons,
  getCouponById,
  createCoupon,
  updateCoupon,
  deleteCoupon,
  toggleCouponStatus,
  bulkUpdateCoupons,
  validateCouponCode,
  getCouponStatistics
};
