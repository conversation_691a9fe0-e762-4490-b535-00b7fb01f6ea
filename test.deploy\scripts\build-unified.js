#!/usr/bin/env node

/**
 * Unified Build Script for Nirvana Organics E-commerce
 * 
 * This script creates a unified deployment structure:
 * - Main frontend at dist/ (root level)
 * - Admin panel at dist/admin/ (subfolder)
 * - Shared assets and proper routing configuration
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warning: (msg) => console.warn(`⚠️  ${msg}`),
  header: (msg) => {
    console.log('\n' + '='.repeat(60));
    console.log(`🚀 ${msg}`);
    console.log('='.repeat(60));
  }
};

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log.info(`Created directory: ${dirPath}`);
  }
}

function copyDirectory(src, dest) {
  ensureDirectoryExists(dest);
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    log.info(`Removed directory: ${dirPath}`);
  }
}

function updateHtmlForAdmin(htmlPath) {
  if (!fs.existsSync(htmlPath)) {
    log.warning(`HTML file not found: ${htmlPath}`);
    return;
  }

  let content = fs.readFileSync(htmlPath, 'utf8');

  // Fix asset paths for admin panel
  // Convert absolute asset paths to relative paths that work with /admin base
  content = content.replace(/href="\/assets\//g, 'href="/admin/assets/');
  content = content.replace(/src="\/assets\//g, 'src="/admin/assets/');

  // Fix other asset references
  content = content.replace(/href="\/([^"]+\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))"/g, (match, path) => {
    if (path.startsWith('admin/')) {
      return `href="/${path}"`;
    }
    return `href="/admin/${path}"`;
  });

  content = content.replace(/src="\/([^"]+\.(js|png|jpg|jpeg|gif|ico|svg))"/g, (match, path) => {
    if (path.startsWith('admin/')) {
      return `src="/${path}"`;
    }
    return `src="/admin/${path}"`;
  });

  // Ensure base href is set correctly for admin panel
  if (!content.includes('<base')) {
    content = content.replace('<head>', '<head>\n    <base href="/admin/">');
  } else {
    // Update existing base href
    content = content.replace(/<base href="[^"]*"/, '<base href="/admin/"');
  }

  fs.writeFileSync(htmlPath, content);
  log.success(`Updated HTML file with correct asset paths: ${htmlPath}`);
}

async function buildUnified() {
  try {
    log.header('Starting Unified Build Process');
    
    // Clean up any existing temp directories
    log.info('Cleaning up temporary directories...');
    removeDirectory('dist-temp-main');
    removeDirectory('dist-temp-admin');
    removeDirectory('dist');
    
    // Step 1: Build main frontend
    log.header('Building Main Frontend Application');
    process.env.VITE_BUILD_TARGET = 'main';
    execSync('vite build --config vite.unified.config.ts --mode production', { 
      stdio: 'inherit',
      env: { ...process.env, VITE_BUILD_TARGET: 'main' }
    });
    log.success('Main frontend build completed');
    
    // Step 2: Build admin panel
    log.header('Building Admin Panel Application');
    process.env.VITE_BUILD_TARGET = 'admin';
    execSync('vite build --config vite.unified.config.ts --mode production', { 
      stdio: 'inherit',
      env: { ...process.env, VITE_BUILD_TARGET: 'admin' }
    });
    log.success('Admin panel build completed');
    
    // Step 3: Create unified structure
    log.header('Creating Unified Deployment Structure');
    
    // Create main dist directory
    ensureDirectoryExists('dist');
    
    // Copy main frontend to root of dist
    if (fs.existsSync('dist-temp-main')) {
      log.info('Copying main frontend to dist/...');
      copyDirectory('dist-temp-main', 'dist');
      log.success('Main frontend copied to dist/');
    } else {
      throw new Error('Main frontend build not found');
    }
    
    // Copy admin panel to dist/admin
    if (fs.existsSync('dist-temp-admin')) {
      log.info('Copying admin panel to dist/admin/...');
      copyDirectory('dist-temp-admin', 'dist/admin');
      
      // Update admin HTML file for proper routing
      const adminHtmlPath = path.join('dist', 'admin', 'admin.html');
      if (fs.existsSync(adminHtmlPath)) {
        updateHtmlForAdmin(adminHtmlPath);
      }
      
      log.success('Admin panel copied to dist/admin/');
    } else {
      throw new Error('Admin panel build not found');
    }
    
    // Step 4: Create routing configuration
    log.header('Creating Routing Configuration');
    
    // Create a simple routing file for reference
    const routingConfig = {
      main: {
        path: '/',
        entry: 'index.html',
        description: 'Main e-commerce frontend'
      },
      admin: {
        path: '/admin',
        entry: 'admin/admin.html',
        description: 'Admin panel'
      }
    };
    
    fs.writeFileSync(
      path.join('dist', 'routing-config.json'),
      JSON.stringify(routingConfig, null, 2)
    );
    log.success('Created routing configuration');
    
    // Step 5: Clean up temporary directories
    log.header('Cleaning Up');
    removeDirectory('dist-temp-main');
    removeDirectory('dist-temp-admin');
    log.success('Cleaned up temporary directories');
    
    // Step 6: Generate build summary
    log.header('Build Summary');
    const distSize = getDirSize('dist');
    log.success(`Unified build completed successfully!`);
    log.info(`Total build size: ${(distSize / 1024 / 1024).toFixed(2)} MB`);
    log.info(`Main frontend: dist/`);
    log.info(`Admin panel: dist/admin/`);
    log.info(`Routing config: dist/routing-config.json`);
    
  } catch (error) {
    log.error(`Build failed: ${error.message}`);
    process.exit(1);
  }
}

function getDirSize(dirPath) {
  let size = 0;
  
  if (!fs.existsSync(dirPath)) return 0;
  
  const files = fs.readdirSync(dirPath);
  
  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      size += getDirSize(filePath);
    } else {
      size += stats.size;
    }
  }
  
  return size;
}

// Run the build if this script is executed directly
if (require.main === module) {
  buildUnified();
}

module.exports = { buildUnified };
