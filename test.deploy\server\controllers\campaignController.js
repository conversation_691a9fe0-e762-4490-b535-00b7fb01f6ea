const { models } = require('../models');
const { validationResult } = require('express-validator');
const campaignService = require('../services/campaignService');
const { Op } = require('sequelize');

/**
 * Campaign Controller
 * Handles campaign management, email marketing, and newsletter operations
 */
class CampaignController {
  /**
   * Get all campaigns with pagination and filtering
   * @route GET /api/admin/campaigns
   * @access Private (Admin)
   */
  static async getAllCampaigns(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        search = '',
        type = '',
        status = '',
        sortBy = 'createdAt',
        sortOrder = 'DESC'
      } = req.query;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Apply filters
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ];
      }

      if (type) {
        whereClause.type = type;
      }

      if (status) {
        whereClause.status = status;
      }

      const { count, rows: campaigns } = await models.Campaign.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: models.User,
            as: 'creator',
            attributes: ['firstName', 'lastName']
          },
          {
            model: models.CampaignRecipient,
            as: 'recipients',
            attributes: ['id', 'status']
          }
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      res.json({
        success: true,
        data: {
          campaigns,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalCampaigns: count,
            limit: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('Get all campaigns error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch campaigns',
        error: error.message
      });
    }
  }

  /**
   * Create new campaign
   * @route POST /api/admin/campaigns
   * @access Private (Admin)
   */
  static async createCampaign(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const result = await campaignService.createCampaign(req.body, req.user.id);

      if (result.success) {
        res.status(201).json(result);
      } else {
        res.status(400).json(result);
      }

    } catch (error) {
      console.error('Create campaign error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create campaign',
        error: error.message
      });
    }
  }

  /**
   * Update campaign
   * @route PUT /api/admin/campaigns/:id
   * @access Private (Admin)
   */
  static async updateCampaign(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const campaign = await models.Campaign.findByPk(id);
      if (!campaign) {
        return res.status(404).json({
          success: false,
          message: 'Campaign not found'
        });
      }

      // Don't allow updating sent campaigns
      if (campaign.status === 'completed') {
        return res.status(400).json({
          success: false,
          message: 'Cannot update completed campaigns'
        });
      }

      await campaign.update(req.body);

      res.json({
        success: true,
        data: campaign,
        message: 'Campaign updated successfully'
      });

    } catch (error) {
      console.error('Update campaign error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update campaign',
        error: error.message
      });
    }
  }

  /**
   * Send campaign
   * @route POST /api/admin/campaigns/:id/send
   * @access Private (Admin)
   */
  static async sendCampaign(req, res) {
    try {
      const { id } = req.params;

      const campaign = await models.Campaign.findByPk(id);
      if (!campaign) {
        return res.status(404).json({
          success: false,
          message: 'Campaign not found'
        });
      }

      if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
        return res.status(400).json({
          success: false,
          message: 'Campaign cannot be sent in current status'
        });
      }

      let result;
      switch (campaign.type) {
        case 'email':
          result = await campaignService.sendEmailCampaign(id);
          break;
        case 'push':
          // Implement push notification sending
          result = { success: false, message: 'Push notifications not implemented yet' };
          break;
        default:
          result = { success: false, message: 'Unsupported campaign type' };
      }

      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }

    } catch (error) {
      console.error('Send campaign error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send campaign',
        error: error.message
      });
    }
  }

  /**
   * Get campaign analytics
   * @route GET /api/admin/campaigns/:id/analytics
   * @access Private (Admin)
   */
  static async getCampaignAnalytics(req, res) {
    try {
      const { id } = req.params;

      const result = await campaignService.getCampaignAnalytics(id);

      if (result.success) {
        res.json(result);
      } else {
        res.status(404).json(result);
      }

    } catch (error) {
      console.error('Get campaign analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch campaign analytics',
        error: error.message
      });
    }
  }

  /**
   * Delete campaign
   * @route DELETE /api/admin/campaigns/:id
   * @access Private (Admin)
   */
  static async deleteCampaign(req, res) {
    try {
      const { id } = req.params;

      const campaign = await models.Campaign.findByPk(id);
      if (!campaign) {
        return res.status(404).json({
          success: false,
          message: 'Campaign not found'
        });
      }

      // Don't allow deleting sent campaigns
      if (campaign.status === 'completed' || campaign.status === 'active') {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete active or completed campaigns'
        });
      }

      await campaign.destroy();

      res.json({
        success: true,
        message: 'Campaign deleted successfully'
      });

    } catch (error) {
      console.error('Delete campaign error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete campaign',
        error: error.message
      });
    }
  }

  /**
   * Get email templates
   * @route GET /api/admin/email-templates
   * @access Private (Admin)
   */
  static async getEmailTemplates(req, res) {
    try {
      const { category = '', isActive = '' } = req.query;
      const whereClause = {};

      if (category) {
        whereClause.category = category;
      }

      if (isActive !== '') {
        whereClause.isActive = isActive === 'true';
      }

      const templates = await models.EmailTemplate.findAll({
        where: whereClause,
        include: [
          {
            model: models.User,
            as: 'creator',
            attributes: ['firstName', 'lastName']
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      res.json({
        success: true,
        data: templates
      });

    } catch (error) {
      console.error('Get email templates error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch email templates',
        error: error.message
      });
    }
  }

  /**
   * Create email template
   * @route POST /api/admin/email-templates
   * @access Private (Admin)
   */
  static async createEmailTemplate(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const result = await campaignService.createEmailTemplate(req.body, req.user.id);

      if (result.success) {
        res.status(201).json(result);
      } else {
        res.status(400).json(result);
      }

    } catch (error) {
      console.error('Create email template error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create email template',
        error: error.message
      });
    }
  }

  /**
   * Subscribe to newsletter
   * @route POST /api/newsletter/subscribe
   * @access Public
   */
  static async subscribeToNewsletter(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, firstName, lastName, source } = req.body;
      const userId = req.user?.id || null;

      const result = await campaignService.subscribeToNewsletter(email, {
        userId,
        firstName,
        lastName,
        source
      });

      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }

    } catch (error) {
      console.error('Newsletter subscribe error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to subscribe to newsletter',
        error: error.message
      });
    }
  }

  /**
   * Unsubscribe from newsletter
   * @route GET /api/newsletter/unsubscribe/:token
   * @access Public
   */
  static async unsubscribeFromNewsletter(req, res) {
    try {
      const { token } = req.params;

      const result = await campaignService.unsubscribeFromNewsletter(token);

      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }

    } catch (error) {
      console.error('Newsletter unsubscribe error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to unsubscribe from newsletter',
        error: error.message
      });
    }
  }
}

module.exports = CampaignController;
