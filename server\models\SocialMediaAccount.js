const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

/**
 * Social Media Account Model
 * Stores OAuth tokens and account information for social media platforms
 */
const SocialMediaAccount = sequelize.define('SocialMediaAccount', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // Platform information
  platform: {
    type: DataTypes.ENUM('facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok'),
    allowNull: false,
    field: 'platform'
  },
  
  // Account details
  accountId: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'account_id'
  },
  
  accountName: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'account_name'
  },
  
  accountHandle: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'account_handle'
  },
  
  profilePicture: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'profile_picture'
  },
  
  // OAuth tokens
  accessToken: {
    type: DataTypes.TEXT,
    allowNull: false,
    field: 'access_token'
  },
  
  refreshToken: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'refresh_token'
  },
  
  tokenExpiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'token_expires_at'
  },
  
  // Account status
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  
  isVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_verified'
  },
  
  // Account metrics
  followersCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'followers_count'
  },
  
  followingCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'following_count'
  },
  
  postsCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'posts_count'
  },
  
  // Platform-specific data
  platformData: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'platform_data'
  },
  
  // Permissions and scopes
  permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'permissions'
  },
  
  // Last sync information
  lastSyncAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_sync_at'
  },
  
  syncStatus: {
    type: DataTypes.ENUM('pending', 'syncing', 'completed', 'failed'),
    defaultValue: 'pending',
    field: 'sync_status'
  },
  
  // Error tracking
  lastError: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'last_error'
  },
  
  errorCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'error_count'
  }
}, {
  tableName: 'social_media_accounts',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['platform', 'account_id']
    },
    {
      fields: ['platform']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['last_sync_at']
    }
  ]
});

// Instance methods
SocialMediaAccount.prototype.isTokenExpired = function() {
  if (!this.tokenExpiresAt) return false;
  return new Date() >= this.tokenExpiresAt;
};

SocialMediaAccount.prototype.needsRefresh = function() {
  if (!this.tokenExpiresAt) return false;
  // Refresh if token expires within 1 hour
  const oneHourFromNow = new Date(Date.now() + 60 * 60 * 1000);
  return oneHourFromNow >= this.tokenExpiresAt;
};

SocialMediaAccount.prototype.updateMetrics = function(metrics) {
  this.followersCount = metrics.followersCount || this.followersCount;
  this.followingCount = metrics.followingCount || this.followingCount;
  this.postsCount = metrics.postsCount || this.postsCount;
  this.lastSyncAt = new Date();
  this.syncStatus = 'completed';
  return this.save();
};

SocialMediaAccount.prototype.recordError = function(error) {
  this.lastError = error.message || error;
  this.errorCount += 1;
  this.syncStatus = 'failed';
  return this.save();
};

// Class methods
SocialMediaAccount.findByPlatform = function(platform) {
  return this.findAll({
    where: { platform, isActive: true },
    order: [['created_at', 'DESC']]
  });
};

SocialMediaAccount.findActiveAccounts = function() {
  return this.findAll({
    where: { isActive: true },
    order: [['platform', 'ASC'], ['created_at', 'DESC']]
  });
};

SocialMediaAccount.findExpiredTokens = function() {
  return this.findAll({
    where: {
      isActive: true,
      tokenExpiresAt: {
        [sequelize.Sequelize.Op.lte]: new Date()
      }
    }
  });
};

module.exports = SocialMediaAccount;
