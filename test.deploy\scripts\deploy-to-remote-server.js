#!/usr/bin/env node

/**
 * Deploy Nirvana Backend to Remote Server
 * This script helps deploy the backend to root@srv928821
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const REMOTE_SERVER = 'root@srv928821';
const REMOTE_PATH = '/var/www/nirvana-backend';
const LOCAL_BACKEND_PATH = './server';

console.log('🚀 Nirvana Backend Deployment Script');
console.log(`📡 Target Server: ${REMOTE_SERVER}`);
console.log(`📁 Remote Path: ${REMOTE_PATH}\n`);

// Step 1: Create deployment package
console.log('1️⃣ Creating deployment package...');

const deploymentFiles = [
  'server/',
  'package.json',
  'package-lock.json',
  '.env',
  'scripts/',
  'public/uploads/',
  'uploads/'
];

const excludePatterns = [
  'node_modules/',
  '.git/',
  'dist/',
  'dist-admin/',
  'logs/',
  '*.log',
  '.DS_Store',
  'Thumbs.db'
];

console.log('📦 Files to deploy:');
deploymentFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`);
  } else {
    console.log(`   ⚠️  ${file} (not found)`);
  }
});

// Step 2: Create production environment file
console.log('\n2️⃣ Creating production environment configuration...');

const productionEnv = `# Production Environment Configuration for Remote Server
# Database Configuration (Update with your remote database details)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_production
DB_USER=nirvana_user
DB_PASSWORD=your_secure_password

# JWT Configuration
JWT_SECRET=nirvana-organics-super-secret-jwt-key-2025-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=nirvana-organics-refresh-token-secret-2025-production
JWT_REFRESH_EXPIRES_IN=30d

# Server Configuration
PORT=5000
NODE_ENV=production
CORS_ORIGIN=https://your-domain.com,https://www.your-domain.com

# Frontend Configuration
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://your-domain.com
API_BASE_URL=https://your-domain.com/api

# Session Configuration
SESSION_SECRET=nirvana-organics-session-secret-2025-production

# Social Authentication Configuration
GOOGLE_CLIENT_ID=53561266132-eaf40j8lto1vm3i0b48qg4jl0lt2vev5.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_OAUTH_CALLBACK_URL=https://your-domain.com/api/auth/google/callback

# Email Configuration (Update with your production email settings)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Your Store Name

# Payment Configuration (Update with production Square credentials)
SQUARE_APPLICATION_ID=your_production_square_app_id
SQUARE_ACCESS_TOKEN=your_production_square_access_token
SQUARE_LOCATION_ID=your_production_square_location_id
SQUARE_ENVIRONMENT=production
SQUARE_WEBHOOK_SIGNATURE_KEY=your_production_webhook_signature

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
FORCE_HTTPS=true

# Web Push Notifications
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>
`;

fs.writeFileSync('.env.production', productionEnv);
console.log('✅ Created .env.production file');

// Step 3: Generate deployment commands
console.log('\n3️⃣ Deployment Commands:');

const deploymentCommands = `
# ============================================================================
# NIRVANA BACKEND DEPLOYMENT COMMANDS
# ============================================================================

# 1. Create deployment directory on remote server
ssh ${REMOTE_SERVER} "mkdir -p ${REMOTE_PATH}"

# 2. Copy files to remote server (choose one method):

# Method A: Using rsync (recommended)
rsync -avz --exclude='node_modules' --exclude='.git' --exclude='logs' \\
  ./ ${REMOTE_SERVER}:${REMOTE_PATH}/

# Method B: Using scp
scp -r server/ package.json package-lock.json .env.production \\
  ${REMOTE_SERVER}:${REMOTE_PATH}/

# 3. Install dependencies and setup on remote server
ssh ${REMOTE_SERVER} "cd ${REMOTE_PATH} && \\
  npm install --production && \\
  cp .env.production .env && \\
  mkdir -p uploads logs && \\
  chmod +x scripts/*.js"

# 4. Setup database (if needed)
ssh ${REMOTE_SERVER} "cd ${REMOTE_PATH} && \\
  node scripts/setup-database.js && \\
  node scripts/create-database-tables.js"

# 5. Start the server with PM2 (process manager)
ssh ${REMOTE_SERVER} "cd ${REMOTE_PATH} && \\
  npm install -g pm2 && \\
  pm2 start server/index.js --name nirvana-backend && \\
  pm2 startup && \\
  pm2 save"

# 6. Setup nginx reverse proxy (optional)
ssh ${REMOTE_SERVER} "apt update && apt install -y nginx"

# 7. Check server status
ssh ${REMOTE_SERVER} "cd ${REMOTE_PATH} && pm2 status"
`;

fs.writeFileSync('deployment-commands.sh', deploymentCommands);
console.log('✅ Created deployment-commands.sh file');

console.log('\n📋 Next Steps:');
console.log('1. Review and update .env.production with your production settings');
console.log('2. Run the commands in deployment-commands.sh');
console.log('3. Or use the automated deployment script below\n');

console.log('🔧 Quick Deployment (run these commands):');
console.log(`chmod +x deployment-commands.sh`);
console.log(`./deployment-commands.sh`);
