#!/usr/bin/env node

/**
 * Comprehensive Local Testing Script
 * Tests all components that can be tested without full database connectivity
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
};

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject({ stdout, stderr, code });
      }
    });

    child.on('error', (error) => {
      reject({ error: error.message, code: -1 });
    });
  });
}

async function testEnvironmentSetup() {
  log.header('🔧 Testing Environment Setup');

  // Check .env file
  if (fs.existsSync('.env')) {
    log.success('.env file exists');
  } else {
    log.error('.env file missing');
    return false;
  }

  // Load environment variables
  require('dotenv').config();

  // Check required variables
  const requiredVars = ['JWT_SECRET', 'JWT_REFRESH_SECRET', 'PORT', 'NODE_ENV'];
  let allPresent = true;

  for (const varName of requiredVars) {
    if (process.env[varName]) {
      log.success(`${varName} is configured`);
    } else {
      log.error(`${varName} is missing`);
      allPresent = false;
    }
  }

  return allPresent;
}

async function testDependencies() {
  log.header('📦 Testing Dependencies');

  try {
    // Check if node_modules exists
    if (!fs.existsSync('node_modules')) {
      log.error('node_modules directory not found');
      return false;
    }
    log.success('node_modules directory exists');

    // Test TypeScript compilation
    log.info('Testing TypeScript compilation...');
    try {
      await runCommand('npx', ['tsc', '--noEmit']);
      log.success('TypeScript compilation successful');
    } catch (error) {
      log.warning('TypeScript compilation has issues (this may be normal)');
    }

    return true;
  } catch (error) {
    log.error(`Dependency test failed: ${error.message}`);
    return false;
  }
}

async function testBuildProcess() {
  log.header('🏗️ Testing Build Process');

  try {
    // Test frontend build
    log.info('Testing frontend build...');
    await runCommand('npm', ['run', 'build:check']);
    log.success('Frontend build successful');

    // Check if dist directory was created
    if (fs.existsSync('dist')) {
      log.success('Build output directory created');
      
      // Check for key files
      const keyFiles = ['index.html', 'assets'];
      for (const file of keyFiles) {
        if (fs.existsSync(path.join('dist', file))) {
          log.success(`Build file '${file}' exists`);
        } else {
          log.warning(`Build file '${file}' missing`);
        }
      }
    } else {
      log.warning('Build output directory not found');
    }

    return true;
  } catch (error) {
    log.error(`Build test failed: ${error.stderr || error.message}`);
    return false;
  }
}

async function testServerStart() {
  log.header('🚀 Testing Server Startup');

  try {
    // Test if server can start (without database)
    log.info('Testing server startup (will timeout after 10 seconds)...');
    
    const serverProcess = spawn('node', ['server/index.js'], {
      stdio: 'pipe',
      env: { ...process.env, NODE_ENV: 'test' }
    });

    let serverOutput = '';
    let serverStarted = false;

    serverProcess.stdout.on('data', (data) => {
      serverOutput += data.toString();
      if (data.toString().includes('Server running on port')) {
        serverStarted = true;
      }
    });

    serverProcess.stderr.on('data', (data) => {
      serverOutput += data.toString();
    });

    // Wait for server to start or timeout
    await new Promise((resolve) => {
      const timeout = setTimeout(() => {
        serverProcess.kill();
        resolve();
      }, 10000);

      serverProcess.on('close', () => {
        clearTimeout(timeout);
        resolve();
      });

      // Check if server started successfully
      const checkInterval = setInterval(() => {
        if (serverStarted) {
          clearInterval(checkInterval);
          clearTimeout(timeout);
          serverProcess.kill();
          resolve();
        }
      }, 500);
    });

    if (serverStarted) {
      log.success('Server started successfully');
      return true;
    } else {
      log.warning('Server startup test inconclusive');
      log.info('Server output:');
      console.log(serverOutput);
      return false;
    }

  } catch (error) {
    log.error(`Server test failed: ${error.message}`);
    return false;
  }
}

async function testLinting() {
  log.header('🔍 Testing Code Quality');

  try {
    log.info('Running ESLint...');
    await runCommand('npm', ['run', 'lint']);
    log.success('Linting passed');
    return true;
  } catch (error) {
    log.warning('Linting found issues (this may be normal)');
    return true; // Don't fail the entire test for linting issues
  }
}

async function runComprehensiveTest() {
  log.header('🧪 Starting Comprehensive Local Testing');

  const tests = [
    { name: 'Environment Setup', fn: testEnvironmentSetup },
    { name: 'Dependencies', fn: testDependencies },
    { name: 'Code Quality', fn: testLinting },
    { name: 'Build Process', fn: testBuildProcess },
    { name: 'Server Startup', fn: testServerStart }
  ];

  const results = [];

  for (const test of tests) {
    log.header(`Running ${test.name} Test`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      
      if (result) {
        log.success(`${test.name} test passed`);
      } else {
        log.error(`${test.name} test failed`);
      }
    } catch (error) {
      log.error(`${test.name} test error: ${error.message}`);
      results.push({ name: test.name, passed: false, error: error.message });
    }
  }

  // Summary
  log.header('📊 Test Summary');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;

  results.forEach(result => {
    if (result.passed) {
      log.success(`✅ ${result.name}`);
    } else {
      log.error(`❌ ${result.name}${result.error ? ` (${result.error})` : ''}`);
    }
  });

  log.header(`Results: ${passed}/${total} tests passed`);

  if (passed === total) {
    log.success('🎉 All tests passed! Ready for database setup and full testing.');
  } else if (passed >= total * 0.7) {
    log.warning('⚠️ Most tests passed. Some issues need attention before deployment.');
  } else {
    log.error('❌ Multiple test failures. Please fix issues before proceeding.');
  }

  return passed >= total * 0.7;
}

// Run comprehensive test
runComprehensiveTest().catch(error => {
  log.error(`Test suite failed: ${error.message}`);
  process.exit(1);
});
