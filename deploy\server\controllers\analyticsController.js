const { User, Product, Order, Review, Category, Coupon, AuditLog } = require('../models');
const { Op, sequelize } = require('sequelize');
const analyticsService = require('../services/analyticsService');
const { validationResult } = require('express-validator');

// Get dashboard overview statistics
const getDashboardOverview = async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const stats = await Promise.all([
      // Total users
      User.count(),
      
      // New users in period
      User.count({
        where: {
          createdAt: { [Op.gte]: startDate }
        }
      }),
      
      // Total products
      Product.count(),
      
      // Active products
      Product.count({ where: { isActive: true } }),
      
      // Total orders
      Order.count(),
      
      // Orders in period
      Order.count({
        where: {
          createdAt: { [Op.gte]: startDate }
        }
      }),
      
      // Total revenue
      Order.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('total')), 'totalRevenue']
        ],
        where: { status: { [Op.in]: ['completed', 'delivered'] } }
      }),
      
      // Revenue in period
      Order.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('total')), 'periodRevenue']
        ],
        where: {
          status: { [Op.in]: ['completed', 'delivered'] },
          createdAt: { [Op.gte]: startDate }
        }
      }),
      
      // Total reviews
      Review.count(),
      
      // Average rating
      Review.findOne({
        attributes: [
          [sequelize.fn('AVG', sequelize.col('rating')), 'averageRating']
        ],
        where: { status: 'approved' }
      }),
      
      // Pending reviews
      Review.count({ where: { status: 'pending' } }),
      
      // Active coupons
      Coupon.count({ where: { isActive: true } }),
      
      // Low stock products (quantity < 10)
      Product.count({
        where: {
          quantity: { [Op.lt]: 10 },
          isActive: true
        }
      })
    ]);

    const [
      totalUsers,
      newUsers,
      totalProducts,
      activeProducts,
      totalOrders,
      periodOrders,
      totalRevenueResult,
      periodRevenueResult,
      totalReviews,
      averageRatingResult,
      pendingReviews,
      activeCoupons,
      lowStockProducts
    ] = stats;

    const totalRevenue = totalRevenueResult?.dataValues?.totalRevenue || 0;
    const periodRevenue = periodRevenueResult?.dataValues?.periodRevenue || 0;
    const averageRating = averageRatingResult?.dataValues?.averageRating || 0;

    // Calculate growth rates
    const userGrowthRate = totalUsers > 0 ? ((newUsers / totalUsers) * 100) : 0;
    const orderGrowthRate = totalOrders > 0 ? ((periodOrders / totalOrders) * 100) : 0;

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_DASHBOARD', 'ANALYTICS', null, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { period }
    });

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          newUsers,
          userGrowthRate: Math.round(userGrowthRate * 100) / 100,
          totalProducts,
          activeProducts,
          inactiveProducts: totalProducts - activeProducts,
          totalOrders,
          periodOrders,
          orderGrowthRate: Math.round(orderGrowthRate * 100) / 100,
          totalRevenue: Math.round(totalRevenue * 100) / 100,
          periodRevenue: Math.round(periodRevenue * 100) / 100,
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          pendingReviews,
          activeCoupons,
          lowStockProducts
        },
        period: parseInt(period)
      }
    });

  } catch (error) {
    console.error('Get dashboard overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard overview',
      error: error.message
    });
  }
};

// Get sales analytics
const getSalesAnalytics = async (req, res) => {
  try {
    const { 
      period = '30',
      groupBy = 'day',
      startDate: customStartDate,
      endDate: customEndDate
    } = req.query;

    let startDate, endDate;
    
    if (customStartDate && customEndDate) {
      startDate = new Date(customStartDate);
      endDate = new Date(customEndDate);
    } else {
      endDate = new Date();
      startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(period));
    }

    // Determine date format based on groupBy
    let dateFormat;
    switch (groupBy) {
      case 'hour':
        dateFormat = '%Y-%m-%d %H:00:00';
        break;
      case 'day':
        dateFormat = '%Y-%m-%d';
        break;
      case 'week':
        dateFormat = '%Y-%u';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    const salesData = await Order.findAll({
      attributes: [
        [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat), 'period'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'orderCount'],
        [sequelize.fn('SUM', sequelize.col('total')), 'revenue'],
        [sequelize.fn('AVG', sequelize.col('total')), 'averageOrderValue']
      ],
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        },
        status: { [Op.in]: ['completed', 'delivered'] }
      },
      group: [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat)],
      order: [[sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat), 'ASC']]
    });

    // Get top selling products
    const topProducts = await sequelize.query(`
      SELECT 
        p.id,
        p.name,
        p.slug,
        p.price,
        SUM(oi.quantity) as totalSold,
        SUM(oi.quantity * oi.price) as totalRevenue
      FROM products p
      JOIN order_items oi ON p.id = oi.product_id
      JOIN orders o ON oi.order_id = o.id
      WHERE o.created_at BETWEEN :startDate AND :endDate
        AND o.status IN ('completed', 'delivered')
      GROUP BY p.id, p.name, p.slug, p.price
      ORDER BY totalSold DESC
      LIMIT 10
    `, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT
    });

    // Get sales by category
    const categoryStats = await sequelize.query(`
      SELECT 
        c.id,
        c.name,
        COUNT(DISTINCT o.id) as orderCount,
        SUM(oi.quantity) as totalQuantity,
        SUM(oi.quantity * oi.price) as totalRevenue
      FROM categories c
      JOIN products p ON c.id = p.category_id
      JOIN order_items oi ON p.id = oi.product_id
      JOIN orders o ON oi.order_id = o.id
      WHERE o.created_at BETWEEN :startDate AND :endDate
        AND o.status IN ('completed', 'delivered')
      GROUP BY c.id, c.name
      ORDER BY totalRevenue DESC
    `, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT
    });

    res.json({
      success: true,
      data: {
        salesData: salesData.map(item => ({
          period: item.period,
          orderCount: parseInt(item.orderCount),
          revenue: Math.round(parseFloat(item.revenue) * 100) / 100,
          averageOrderValue: Math.round(parseFloat(item.averageOrderValue) * 100) / 100
        })),
        topProducts: topProducts.map(product => ({
          ...product,
          totalRevenue: Math.round(parseFloat(product.totalRevenue) * 100) / 100
        })),
        categoryStats: categoryStats.map(category => ({
          ...category,
          totalRevenue: Math.round(parseFloat(category.totalRevenue) * 100) / 100
        })),
        period: { startDate, endDate, groupBy }
      }
    });

  } catch (error) {
    console.error('Get sales analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sales analytics',
      error: error.message
    });
  }
};

// Get customer analytics
const getCustomerAnalytics = async (req, res) => {
  try {
    const { period = '30' } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const stats = await Promise.all([
      // Customer acquisition
      User.findAll({
        attributes: [
          [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), '%Y-%m-%d'), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'newCustomers']
        ],
        where: {
          createdAt: { [Op.gte]: startDate },
          role: 'customer'
        },
        group: [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), '%Y-%m-%d')],
        order: [[sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), '%Y-%m-%d'), 'ASC']]
      }),
      
      // Customer lifetime value
      sequelize.query(`
        SELECT 
          u.id,
          u.first_name,
          u.last_name,
          u.email,
          COUNT(o.id) as orderCount,
          SUM(o.total) as lifetimeValue,
          AVG(o.total) as averageOrderValue,
          MAX(o.created_at) as lastOrderDate
        FROM users u
        LEFT JOIN orders o ON u.id = o.user_id AND o.status IN ('completed', 'delivered')
        WHERE u.role = 'customer'
        GROUP BY u.id, u.first_name, u.last_name, u.email
        HAVING COUNT(o.id) > 0
        ORDER BY lifetimeValue DESC
        LIMIT 20
      `, { type: sequelize.QueryTypes.SELECT }),
      
      // Customer segments
      sequelize.query(`
        SELECT 
          CASE 
            WHEN order_count = 0 THEN 'No Orders'
            WHEN order_count = 1 THEN 'One-time Buyer'
            WHEN order_count BETWEEN 2 AND 5 THEN 'Regular Customer'
            WHEN order_count > 5 THEN 'VIP Customer'
          END as segment,
          COUNT(*) as customerCount
        FROM (
          SELECT 
            u.id,
            COUNT(o.id) as order_count
          FROM users u
          LEFT JOIN orders o ON u.id = o.user_id AND o.status IN ('completed', 'delivered')
          WHERE u.role = 'customer'
          GROUP BY u.id
        ) customer_orders
        GROUP BY segment
      `, { type: sequelize.QueryTypes.SELECT }),
      
      // Geographic distribution
      sequelize.query(`
        SELECT 
          a.city,
          a.state,
          a.country,
          COUNT(DISTINCT u.id) as customerCount
        FROM users u
        JOIN addresses a ON u.id = a.user_id
        WHERE u.role = 'customer' AND a.is_default = true
        GROUP BY a.city, a.state, a.country
        ORDER BY customerCount DESC
        LIMIT 20
      `, { type: sequelize.QueryTypes.SELECT })
    ]);

    const [customerAcquisition, topCustomers, customerSegments, geographicDistribution] = stats;

    res.json({
      success: true,
      data: {
        customerAcquisition: customerAcquisition.map(item => ({
          date: item.date,
          newCustomers: parseInt(item.newCustomers)
        })),
        topCustomers: topCustomers.map(customer => ({
          ...customer,
          lifetimeValue: Math.round(parseFloat(customer.lifetimeValue || 0) * 100) / 100,
          averageOrderValue: Math.round(parseFloat(customer.averageOrderValue || 0) * 100) / 100
        })),
        customerSegments,
        geographicDistribution,
        period: parseInt(period)
      }
    });

  } catch (error) {
    console.error('Get customer analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer analytics',
      error: error.message
    });
  }
};

// Get product analytics
const getProductAnalytics = async (req, res) => {
  try {
    const { period = '30' } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const stats = await Promise.all([
      // Product performance
      sequelize.query(`
        SELECT
          p.id,
          p.name,
          p.slug,
          p.price,
          p.quantity as stock,
          COALESCE(SUM(oi.quantity), 0) as totalSold,
          COALESCE(SUM(oi.quantity * oi.price), 0) as revenue,
          COALESCE(AVG(r.rating), 0) as averageRating,
          COUNT(DISTINCT r.id) as reviewCount
        FROM products p
        LEFT JOIN order_items oi ON p.id = oi.product_id
        LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN ('completed', 'delivered')
          AND o.created_at >= :startDate
        LEFT JOIN reviews r ON p.id = r.product_id AND r.status = 'approved'
        WHERE p.is_active = true
        GROUP BY p.id, p.name, p.slug, p.price, p.quantity
        ORDER BY totalSold DESC
        LIMIT 50
      `, {
        replacements: { startDate },
        type: sequelize.QueryTypes.SELECT
      }),

      // Inventory alerts
      Product.findAll({
        attributes: ['id', 'name', 'slug', 'quantity', 'price'],
        where: {
          isActive: true,
          [Op.or]: [
            { quantity: { [Op.lte]: 5 } }, // Low stock
            { quantity: 0 } // Out of stock
          ]
        },
        order: [['quantity', 'ASC']]
      }),

      // Category performance
      sequelize.query(`
        SELECT
          c.id,
          c.name,
          COUNT(DISTINCT p.id) as productCount,
          COALESCE(SUM(oi.quantity), 0) as totalSold,
          COALESCE(SUM(oi.quantity * oi.price), 0) as revenue
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = true
        LEFT JOIN order_items oi ON p.id = oi.product_id
        LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN ('completed', 'delivered')
          AND o.created_at >= :startDate
        GROUP BY c.id, c.name
        ORDER BY revenue DESC
      `, {
        replacements: { startDate },
        type: sequelize.QueryTypes.SELECT
      }),

      // Recently added products
      Product.findAll({
        attributes: ['id', 'name', 'slug', 'price', 'quantity', 'createdAt'],
        where: {
          createdAt: { [Op.gte]: startDate },
          isActive: true
        },
        order: [['createdAt', 'DESC']],
        limit: 10
      })
    ]);

    const [productPerformance, inventoryAlerts, categoryPerformance, recentProducts] = stats;

    res.json({
      success: true,
      data: {
        productPerformance: productPerformance.map(product => ({
          ...product,
          revenue: Math.round(parseFloat(product.revenue || 0) * 100) / 100,
          averageRating: Math.round(parseFloat(product.averageRating || 0) * 10) / 10
        })),
        inventoryAlerts: inventoryAlerts.map(product => ({
          ...product.toJSON(),
          status: product.quantity === 0 ? 'out_of_stock' : 'low_stock'
        })),
        categoryPerformance: categoryPerformance.map(category => ({
          ...category,
          revenue: Math.round(parseFloat(category.revenue || 0) * 100) / 100
        })),
        recentProducts,
        period: parseInt(period)
      }
    });

  } catch (error) {
    console.error('Get product analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product analytics',
      error: error.message
    });
  }
};

// Get system performance metrics
const getSystemMetrics = async (req, res) => {
  try {
    const { period = '7' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const stats = await Promise.all([
      // Admin activity
      AuditLog.findAll({
        attributes: [
          [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), '%Y-%m-%d'), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'activityCount']
        ],
        where: {
          createdAt: { [Op.gte]: startDate }
        },
        group: [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), '%Y-%m-%d')],
        order: [[sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), '%Y-%m-%d'), 'ASC']]
      }),

      // Most active admins
      AuditLog.findAll({
        attributes: [
          'userId',
          [sequelize.fn('COUNT', sequelize.col('AuditLog.id')), 'actionCount']
        ],
        include: [{
          model: User,
          as: 'user',
          attributes: ['firstName', 'lastName', 'email', 'role']
        }],
        where: {
          createdAt: { [Op.gte]: startDate }
        },
        group: ['userId', 'user.id'],
        order: [[sequelize.fn('COUNT', sequelize.col('AuditLog.id')), 'DESC']],
        limit: 10
      }),

      // Action types distribution
      AuditLog.findAll({
        attributes: [
          'action',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: {
          createdAt: { [Op.gte]: startDate }
        },
        group: ['action'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 20
      }),

      // Error rates (failed actions)
      AuditLog.findAll({
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: {
          createdAt: { [Op.gte]: startDate }
        },
        group: ['status']
      }),

      // Database table sizes
      sequelize.query(`
        SELECT
          table_name,
          table_rows,
          ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        ORDER BY (data_length + index_length) DESC
      `, { type: sequelize.QueryTypes.SELECT })
    ]);

    const [adminActivity, activeAdmins, actionTypes, errorRates, tableSizes] = stats;

    // Calculate error rate percentage
    const totalActions = errorRates.reduce((sum, item) => sum + parseInt(item.count), 0);
    const failedActions = errorRates.find(item => item.status === 'failed')?.count || 0;
    const errorRatePercentage = totalActions > 0 ? (failedActions / totalActions) * 100 : 0;

    res.json({
      success: true,
      data: {
        adminActivity: adminActivity.map(item => ({
          date: item.date,
          activityCount: parseInt(item.activityCount)
        })),
        activeAdmins: activeAdmins.map(admin => ({
          userId: admin.userId,
          user: admin.user,
          actionCount: parseInt(admin.actionCount)
        })),
        actionTypes: actionTypes.map(action => ({
          action: action.action,
          count: parseInt(action.count)
        })),
        errorRates: {
          total: totalActions,
          failed: failedActions,
          percentage: Math.round(errorRatePercentage * 100) / 100
        },
        tableSizes,
        period: parseInt(period)
      }
    });

  } catch (error) {
    console.error('Get system metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system metrics',
      error: error.message
    });
  }
};

// Export data for reports
const exportData = async (req, res) => {
  try {
    const { type, format = 'json', startDate, endDate } = req.query;

    if (!type) {
      return res.status(400).json({
        success: false,
        message: 'Export type is required'
      });
    }

    let data;
    let filename;

    switch (type) {
      case 'sales':
        data = await getSalesExportData(startDate, endDate);
        filename = `sales_report_${new Date().toISOString().split('T')[0]}`;
        break;
      case 'customers':
        data = await getCustomersExportData(startDate, endDate);
        filename = `customers_report_${new Date().toISOString().split('T')[0]}`;
        break;
      case 'products':
        data = await getProductsExportData();
        filename = `products_report_${new Date().toISOString().split('T')[0]}`;
        break;
      case 'audit':
        data = await getAuditExportData(startDate, endDate);
        filename = `audit_log_${new Date().toISOString().split('T')[0]}`;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid export type'
        });
    }

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'EXPORT_DATA', 'ANALYTICS', null, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { type, format, startDate, endDate },
      severity: 'medium'
    });

    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(data);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.send(csv);
    } else {
      res.json({
        success: true,
        data,
        filename: `${filename}.json`
      });
    }

  } catch (error) {
    console.error('Export data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export data',
      error: error.message
    });
  }
};

// Helper functions for data export
const getSalesExportData = async (startDate, endDate) => {
  const where = {};
  if (startDate && endDate) {
    where.createdAt = { [Op.between]: [new Date(startDate), new Date(endDate)] };
  }

  return await Order.findAll({
    where: { ...where, status: { [Op.in]: ['completed', 'delivered'] } },
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['firstName', 'lastName', 'email']
      }
    ],
    order: [['createdAt', 'DESC']]
  });
};

const getCustomersExportData = async (startDate, endDate) => {
  const where = { role: 'customer' };
  if (startDate && endDate) {
    where.createdAt = { [Op.between]: [new Date(startDate), new Date(endDate)] };
  }

  return await User.findAll({
    where,
    attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] },
    order: [['createdAt', 'DESC']]
  });
};

const getProductsExportData = async () => {
  return await Product.findAll({
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['name']
      }
    ],
    order: [['createdAt', 'DESC']]
  });
};

const getAuditExportData = async (startDate, endDate) => {
  const where = {};
  if (startDate && endDate) {
    where.createdAt = { [Op.between]: [new Date(startDate), new Date(endDate)] };
  }

  return await AuditLog.findAll({
    where,
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['firstName', 'lastName', 'email']
      }
    ],
    order: [['createdAt', 'DESC']]
  });
};

const convertToCSV = (data) => {
  if (!data || data.length === 0) return '';

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row =>
      headers.map(header => {
        const value = row[header];
        return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
      }).join(',')
    )
  ].join('\n');

  return csvContent;
};

// Enhanced export to Excel
const exportToExcel = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { startDate, endDate, filename } = req.body;

    const dateRange = {};
    if (startDate) dateRange.startDate = startDate;
    if (endDate) dateRange.endDate = endDate;

    // Get analytics data
    const analyticsResult = await analyticsService.getDashboardAnalytics(dateRange);

    if (!analyticsResult.success) {
      return res.status(500).json(analyticsResult);
    }

    // Export to Excel
    const exportResult = await analyticsService.exportToExcel(
      analyticsResult.data,
      filename || 'analytics-export'
    );

    if (exportResult.success) {
      res.setHeader('Content-Type', exportResult.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${exportResult.filename}"`);
      res.send(exportResult.data);
    } else {
      res.status(500).json(exportResult);
    }

  } catch (error) {
    console.error('Export to Excel error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export to Excel',
      error: error.message
    });
  }
};

// Enhanced export to CSV
const exportToCSV = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { startDate, endDate, filename } = req.body;

    const dateRange = {};
    if (startDate) dateRange.startDate = startDate;
    if (endDate) dateRange.endDate = endDate;

    // Get analytics data
    const analyticsResult = await analyticsService.getDashboardAnalytics(dateRange);

    if (!analyticsResult.success) {
      return res.status(500).json(analyticsResult);
    }

    // Export to CSV
    const exportResult = await analyticsService.exportToCSV(
      analyticsResult.data,
      filename || 'analytics-export'
    );

    if (exportResult.success) {
      res.setHeader('Content-Type', exportResult.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${exportResult.filename}"`);
      res.send(exportResult.data);
    } else {
      res.status(500).json(exportResult);
    }

  } catch (error) {
    console.error('Export to CSV error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export to CSV',
      error: error.message
    });
  }
};

// Get real-time analytics
const getRealTimeAnalytics = async (req, res) => {
  try {
    const result = await analyticsService.getRealTimeAnalytics();

    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }

  } catch (error) {
    console.error('Get real-time analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch real-time analytics',
      error: error.message
    });
  }
};

// Get customer lifetime value analytics
const getCustomerLTVAnalytics = async (req, res) => {
  try {
    const result = await analyticsService.getCustomerLTVAnalytics();

    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }

  } catch (error) {
    console.error('Get customer LTV analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer LTV analytics',
      error: error.message
    });
  }
};

// Get enhanced dashboard analytics
const getEnhancedDashboardAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const dateRange = {};
    if (startDate) dateRange.startDate = startDate;
    if (endDate) dateRange.endDate = endDate;

    const result = await analyticsService.getDashboardAnalytics(dateRange);

    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }

  } catch (error) {
    console.error('Get enhanced dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch enhanced dashboard analytics',
      error: error.message
    });
  }
};

module.exports = {
  getDashboardOverview,
  getSalesAnalytics,
  getCustomerAnalytics,
  getProductAnalytics,
  getSystemMetrics,
  exportData,
  // Enhanced analytics functions
  exportToExcel,
  exportToCSV,
  getRealTimeAnalytics,
  getCustomerLTVAnalytics,
  getEnhancedDashboardAnalytics
};
