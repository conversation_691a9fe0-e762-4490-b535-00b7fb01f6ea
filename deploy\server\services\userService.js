const { models } = require('../models/database');
const bcrypt = require('bcrypt');

/**
 * User Service for managing user operations
 */
class UserService {
  /**
   * Get user by ID with related data
   */
  async getUserById(userId, includeRelated = false) {
    try {
      const includeOptions = [];
      
      if (includeRelated) {
        includeOptions.push(
          {
            model: models.Address,
            as: 'addresses',
            required: false
          },
          {
            model: models.Order,
            as: 'orders',
            required: false,
            limit: 10,
            order: [['createdAt', 'DESC']],
            include: [
              {
                model: models.OrderItem,
                as: 'items',
                required: false,
                include: [
                  {
                    model: models.Product,
                    as: 'product',
                    required: false,
                    attributes: ['id', 'name', 'slug', 'images']
                  }
                ]
              }
            ]
          },
          {
            model: models.Review,
            as: 'reviews',
            required: false,
            limit: 5,
            order: [['createdAt', 'DESC']],
            include: [
              {
                model: models.Product,
                as: 'product',
                required: false,
                attributes: ['id', 'name', 'slug', 'images']
              }
            ]
          },
          {
            model: models.Wishlist,
            as: 'wishlist',
            required: false,
            include: [
              {
                model: models.Product,
                as: 'product',
                required: false,
                attributes: ['id', 'name', 'slug', 'price', 'images', 'isActive']
              }
            ]
          }
        );
      }

      const user = await models.User.findByPk(userId, {
        attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] },
        include: includeOptions
      });

      return user;
    } catch (error) {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId, updateData) {
    try {
      const user = await models.User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate email uniqueness if email is being updated
      if (updateData.email && updateData.email !== user.email) {
        const existingUser = await models.User.findOne({ 
          where: { email: updateData.email } 
        });
        if (existingUser) {
          throw new Error('Email already exists');
        }
      }

      // Update user
      await user.update(updateData);

      // Return updated user without sensitive data
      return await this.getUserById(userId);
    } catch (error) {
      throw new Error(`Failed to update profile: ${error.message}`);
    }
  }

  /**
   * Change user password
   */
  async changePassword(userId, currentPassword, newPassword) {
    try {
      const user = await models.User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      await user.update({ password: hashedNewPassword });

      return { message: 'Password changed successfully' };
    } catch (error) {
      throw new Error(`Failed to change password: ${error.message}`);
    }
  }

  /**
   * Get user addresses
   */
  async getUserAddresses(userId) {
    try {
      const addresses = await models.Address.findAll({
        where: { userId },
        order: [['isDefault', 'DESC'], ['createdAt', 'DESC']]
      });

      return addresses;
    } catch (error) {
      throw new Error(`Failed to get addresses: ${error.message}`);
    }
  }

  /**
   * Add user address
   */
  async addAddress(userId, addressData) {
    try {
      // If this is set as default, unset other default addresses of the same type
      if (addressData.isDefault) {
        await models.Address.update(
          { isDefault: false },
          { 
            where: { 
              userId, 
              type: addressData.type 
            } 
          }
        );
      }

      const address = await models.Address.create({
        ...addressData,
        userId
      });

      return address;
    } catch (error) {
      throw new Error(`Failed to add address: ${error.message}`);
    }
  }

  /**
   * Update user address
   */
  async updateAddress(userId, addressId, updateData) {
    try {
      const address = await models.Address.findOne({
        where: { id: addressId, userId }
      });

      if (!address) {
        throw new Error('Address not found');
      }

      // If this is set as default, unset other default addresses of the same type
      if (updateData.isDefault) {
        await models.Address.update(
          { isDefault: false },
          { 
            where: { 
              userId, 
              type: updateData.type || address.type,
              id: { [models.Sequelize.Op.ne]: addressId }
            } 
          }
        );
      }

      await address.update(updateData);
      return address;
    } catch (error) {
      throw new Error(`Failed to update address: ${error.message}`);
    }
  }

  /**
   * Delete user address
   */
  async deleteAddress(userId, addressId) {
    try {
      const address = await models.Address.findOne({
        where: { id: addressId, userId }
      });

      if (!address) {
        throw new Error('Address not found');
      }

      await address.destroy();
      return { message: 'Address deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete address: ${error.message}`);
    }
  }

  /**
   * Get user orders
   */
  async getUserOrders(userId, options = {}) {
    try {
      const { page = 1, limit = 10, status } = options;
      const offset = (page - 1) * limit;

      const where = { userId };
      if (status) {
        where.status = status;
      }

      const { count, rows: orders } = await models.Order.findAndCountAll({
        where,
        include: [
          {
            model: models.OrderItem,
            as: 'items',
            include: [
              {
                model: models.Product,
                as: 'product',
                attributes: ['id', 'name', 'slug', 'images']
              }
            ]
          }
        ],
        limit,
        offset,
        order: [['createdAt', 'DESC']]
      });

      return {
        orders,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalOrders: count,
          hasNext: page < Math.ceil(count / limit),
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw new Error(`Failed to get orders: ${error.message}`);
    }
  }

  /**
   * Get user wishlist
   */
  async getUserWishlist(userId) {
    try {
      const wishlist = await models.Wishlist.findAll({
        where: { userId },
        include: [
          {
            model: models.Product,
            as: 'product',
            where: { isActive: true },
            attributes: ['id', 'name', 'slug', 'price', 'comparePrice', 'images', 'rating', 'reviewCount']
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      return wishlist;
    } catch (error) {
      throw new Error(`Failed to get wishlist: ${error.message}`);
    }
  }

  /**
   * Add item to wishlist
   */
  async addToWishlist(userId, productId, variantId = null, notes = null) {
    try {
      // Check if item already exists in wishlist
      const existingItem = await models.Wishlist.findOne({
        where: { userId, productId, variantId }
      });

      if (existingItem) {
        throw new Error('Item already in wishlist');
      }

      // Verify product exists and is active
      const product = await models.Product.findOne({
        where: { id: productId, isActive: true }
      });

      if (!product) {
        throw new Error('Product not found or inactive');
      }

      const wishlistItem = await models.Wishlist.create({
        userId,
        productId,
        variantId,
        notes
      });

      // Return with product details
      return await models.Wishlist.findByPk(wishlistItem.id, {
        include: [
          {
            model: models.Product,
            as: 'product',
            attributes: ['id', 'name', 'slug', 'price', 'comparePrice', 'images', 'rating', 'reviewCount']
          }
        ]
      });
    } catch (error) {
      throw new Error(`Failed to add to wishlist: ${error.message}`);
    }
  }

  /**
   * Remove item from wishlist
   */
  async removeFromWishlist(userId, wishlistItemId) {
    try {
      const wishlistItem = await models.Wishlist.findOne({
        where: { id: wishlistItemId, userId }
      });

      if (!wishlistItem) {
        throw new Error('Wishlist item not found');
      }

      await wishlistItem.destroy();
      return { message: 'Item removed from wishlist' };
    } catch (error) {
      throw new Error(`Failed to remove from wishlist: ${error.message}`);
    }
  }

  /**
   * Deactivate user account
   */
  async deactivateAccount(userId, password) {
    try {
      const user = await models.User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new Error('Password is incorrect');
      }

      // Deactivate account
      await user.update({ isActive: false });

      return { message: 'Account deactivated successfully' };
    } catch (error) {
      throw new Error(`Failed to deactivate account: ${error.message}`);
    }
  }
}

module.exports = new UserService();
