#!/usr/bin/env node

/**
 * Send Test Email Script for Nirvana Organics
 * Sends a test email to verify email service functionality
 */

require('dotenv').config();
const nodemailer = require('nodemailer');

// Get recipient email from command line argument or use default
const recipientEmail = process.argv[2] || '<EMAIL>';

console.log(`Sending test email to ${recipientEmail}...`);

const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT),
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
});

const testEmail = {
  from: `"Shop Nirvana Organics" <${process.env.EMAIL_USER}>`,
  to: recipientEmail,
  subject: '🧪 Email Service Test - Nirvana Organics E-commerce Platform',
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
      <div style="background: linear-gradient(135deg, #16a34a, #22c55e); padding: 40px; text-align: center;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🌿 Nirvana Organics</h1>
        <p style="color: #dcfce7; margin: 10px 0 0 0; font-size: 16px;">E-commerce Platform Email Test</p>
      </div>
      
      <div style="padding: 40px; background: #f9fafb;">
        <h2 style="color: #16a34a; margin-top: 0;">✅ Email Service Successfully Configured!</h2>
        
        <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
          Congratulations! Your Nirvana Organics e-commerce platform email service is now fully operational and ready for production use.
        </p>
        
        <div style="background: white; padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #16a34a;">
          <h3 style="color: #16a34a; margin-top: 0;">📧 Email Configuration Details:</h3>
          <ul style="color: #374151; line-height: 1.8;">
            <li><strong>SMTP Provider:</strong> Gmail (smtp.gmail.com)</li>
            <li><strong>Authentication:</strong> Gmail App Password</li>
            <li><strong>From Address:</strong> <EMAIL></li>
            <li><strong>Orders Email:</strong> <EMAIL></li>
            <li><strong>Customer Service:</strong> <EMAIL></li>
            <li><strong>Support Email:</strong> <EMAIL></li>
          </ul>
        </div>
        
        <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="color: #16a34a; margin-top: 0;">🚀 Ready Email Types:</h3>
          <ul style="color: #374151; line-height: 1.8;">
            <li>✅ Order Confirmation Emails</li>
            <li>✅ Password Reset Emails</li>
            <li>✅ Welcome & Verification Emails</li>
            <li>✅ Contact Form Notifications</li>
            <li>✅ Order Status Updates</li>
            <li>✅ Customer Service Communications</li>
          </ul>
        </div>
        
        <p style="color: #374151; line-height: 1.6;">
          Your customers will now receive professional, branded emails for all their interactions with your e-commerce platform.
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="https://shopnirvanaorganics.com" style="background: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">Visit Shop Nirvana Organics</a>
        </div>
      </div>
      
      <div style="background: #374151; color: white; padding: 25px; text-align: center;">
        <p style="margin: 0; font-size: 14px;">
          © 2025 Shop Nirvana Organics. All rights reserved.<br>
          <span style="color: #9ca3af;">This is a test email to verify email service functionality.</span>
        </p>
      </div>
    </div>
  `,
  text: `
    Nirvana Organics E-commerce Platform - Email Service Test
    
    ✅ Email Service Successfully Configured!
    
    Your Nirvana Organics e-commerce platform email service is now fully operational and ready for production use.
    
    Email Configuration Details:
    - SMTP Provider: Gmail (smtp.gmail.com)
    - Authentication: Gmail App Password
    - From Address: <EMAIL>
    - Orders Email: <EMAIL>
    - Customer Service: <EMAIL>
    - Support Email: <EMAIL>
    
    Ready Email Types:
    ✅ Order Confirmation Emails
    ✅ Password Reset Emails
    ✅ Welcome & Verification Emails
    ✅ Contact Form Notifications
    ✅ Order Status Updates
    ✅ Customer Service Communications
    
    Your customers will now receive professional, branded emails for all their interactions with your e-commerce platform.
    
    Visit: https://shopnirvanaorganics.com
    
    © 2025 Shop Nirvana Organics. All rights reserved.
    This is a test email to verify email service functionality.
  `
};

transporter.sendMail(testEmail)
  .then(info => {
    console.log(`✅ Test email sent successfully to ${recipientEmail}!`);
    console.log('📧 Message ID:', info.messageId);
    console.log('📤 Response:', info.response);
    console.log('');
    console.log(`🎯 Please check the inbox at ${recipientEmail}`);
    console.log('📱 Also check spam/junk folder if not found in inbox');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Failed to send test email:', error.message);
    process.exit(1);
  });
