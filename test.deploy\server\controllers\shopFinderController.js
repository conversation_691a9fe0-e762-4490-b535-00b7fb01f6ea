const SquareService = require('../services/squareService');
const { Product } = require('../models');
const { Op } = require('sequelize');

/**
 * Shop Finder Controller
 * Handles store location and inventory queries
 */
class ShopFinderController {
  /**
   * Get all store locations from Square
   * @route GET /api/shop-finder/locations
   * @access Public
   */
  static async getLocations(req, res) {
    try {
      const locations = await SquareService.getLocations();
      
      res.json({
        success: true,
        data: locations
      });
    } catch (error) {
      console.error('Get locations error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch store locations',
        error: error.message
      });
    }
  }

  /**
   * Search locations by address or coordinates
   * @route GET /api/shop-finder/locations/search
   * @access Public
   */
  static async searchLocations(req, res) {
    try {
      const { address, latitude, longitude, radius = 25 } = req.query;
      
      // For now, return all locations since Square API doesn't have built-in search
      // In a real implementation, you would use a geocoding service
      const locations = await SquareService.getLocations();
      
      // Filter by status - only return active locations
      const activeLocations = locations.filter(location => location.status === 'ACTIVE');
      
      res.json({
        success: true,
        data: activeLocations,
        searchParams: { address, latitude, longitude, radius }
      });
    } catch (error) {
      console.error('Search locations error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search locations',
        error: error.message
      });
    }
  }

  /**
   * Get location details by ID
   * @route GET /api/shop-finder/locations/:locationId
   * @access Public
   */
  static async getLocationById(req, res) {
    try {
      const { locationId } = req.params;
      const locations = await SquareService.getLocations();
      const location = locations.find(loc => loc.id === locationId);
      
      if (!location) {
        return res.status(404).json({
          success: false,
          message: 'Location not found'
        });
      }
      
      res.json({
        success: true,
        data: location
      });
    } catch (error) {
      console.error('Get location by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch location details',
        error: error.message
      });
    }
  }

  /**
   * Get inventory for a specific location
   * @route GET /api/shop-finder/locations/:locationId/inventory
   * @access Public
   */
  static async getLocationInventory(req, res) {
    try {
      const { locationId } = req.params;
      const { displayMode = 'live', forceRefresh = false } = req.query;
      
      // Get all products from local database
      const products = await Product.findAll({
        where: {
          isActive: true,
          quantity: { [Op.gt]: 0 }
        },
        attributes: ['id', 'name', 'sku', 'price', 'quantity', 'squareItemId'],
        order: [['name', 'ASC']]
      });
      
      const inventory = [];
      
      for (const product of products) {
        let quantity = product.quantity;
        
        // If live mode and product has Square item ID, get live inventory
        if (displayMode === 'live' && product.squareItemId) {
          try {
            const squareQuantity = await SquareService.getInventoryCount(product.squareItemId);
            if (squareQuantity !== null) {
              quantity = squareQuantity;
              
              // Update local quantity if different (background sync)
              if (quantity !== product.quantity) {
                await product.update({ quantity });
              }
            }
          } catch (error) {
            console.error(`Error fetching Square inventory for product ${product.id}:`, error);
            // Fall back to local quantity
          }
        }
        
        inventory.push({
          productId: product.id,
          productName: product.name,
          sku: product.sku,
          quantity,
          price: parseFloat(product.price),
          squareItemId: product.squareItemId,
          lastUpdated: new Date().toISOString()
        });
      }
      
      res.json({
        success: true,
        data: inventory,
        metadata: {
          locationId,
          displayMode,
          totalProducts: inventory.length,
          inStockProducts: inventory.filter(item => item.quantity > 0).length,
          lastUpdated: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Get location inventory error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch location inventory',
        error: error.message
      });
    }
  }

  /**
   * Get product availability across all locations
   * @route GET /api/shop-finder/products/:productId/availability
   * @access Public
   */
  static async getProductAvailability(req, res) {
    try {
      const { productId } = req.params;
      
      // Get product details
      const product = await Product.findByPk(productId, {
        attributes: ['id', 'name', 'sku', 'price', 'quantity', 'squareItemId']
      });
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      // Get all locations
      const locations = await SquareService.getLocations();
      const activeLocations = locations.filter(loc => loc.status === 'ACTIVE');
      
      const availability = [];
      
      for (const location of activeLocations) {
        let quantity = product.quantity;
        
        // If product has Square item ID, get live inventory
        if (product.squareItemId) {
          try {
            const squareQuantity = await SquareService.getInventoryCount(product.squareItemId);
            if (squareQuantity !== null) {
              quantity = squareQuantity;
            }
          } catch (error) {
            console.error(`Error fetching Square inventory for location ${location.id}:`, error);
          }
        }
        
        availability.push({
          locationId: location.id,
          locationName: location.name,
          quantity,
          price: parseFloat(product.price),
          lastUpdated: new Date().toISOString()
        });
      }
      
      res.json({
        success: true,
        data: availability,
        product: {
          id: product.id,
          name: product.name,
          sku: product.sku
        }
      });
    } catch (error) {
      console.error('Get product availability error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product availability',
        error: error.message
      });
    }
  }

  /**
   * Reserve product at a specific location
   * @route POST /api/shop-finder/reservations
   * @access Private
   */
  static async reserveProduct(req, res) {
    try {
      const { locationId, productId, quantity, customerEmail, customerPhone } = req.body;
      
      // Validate input
      if (!locationId || !productId || !quantity || !customerEmail) {
        return res.status(400).json({
          success: false,
          message: 'Missing required fields: locationId, productId, quantity, customerEmail'
        });
      }
      
      // Check if product exists and has sufficient quantity
      const product = await Product.findByPk(productId);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      if (product.quantity < quantity) {
        return res.status(400).json({
          success: false,
          message: 'Insufficient quantity available'
        });
      }
      
      // Create reservation (simplified - in production you'd have a reservations table)
      const reservationId = `RES-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      
      // In a real implementation, you would:
      // 1. Create a reservation record in the database
      // 2. Temporarily reduce the available quantity
      // 3. Set up a cleanup job to release expired reservations
      
      res.json({
        success: true,
        data: {
          reservationId,
          expiresAt: expiresAt.toISOString()
        },
        message: 'Product reserved successfully'
      });
    } catch (error) {
      console.error('Reserve product error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reserve product',
        error: error.message
      });
    }
  }

  /**
   * Get reservation details
   * @route GET /api/shop-finder/reservations/:reservationId
   * @access Private
   */
  static async getReservation(req, res) {
    try {
      const { reservationId } = req.params;
      
      // In a real implementation, you would fetch from a reservations table
      // For now, return a mock response
      res.json({
        success: true,
        data: {
          id: reservationId,
          locationId: 'mock-location-id',
          productId: 1,
          quantity: 1,
          status: 'active',
          expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Get reservation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch reservation details',
        error: error.message
      });
    }
  }

  /**
   * Cancel a reservation
   * @route DELETE /api/shop-finder/reservations/:reservationId
   * @access Private
   */
  static async cancelReservation(req, res) {
    try {
      const { reservationId } = req.params;
      
      // In a real implementation, you would:
      // 1. Find the reservation in the database
      // 2. Update its status to 'cancelled'
      // 3. Release the reserved quantity back to available stock
      
      res.json({
        success: true,
        message: 'Reservation cancelled successfully'
      });
    } catch (error) {
      console.error('Cancel reservation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel reservation',
        error: error.message
      });
    }
  }
}

module.exports = ShopFinderController;
