const { body, param, query } = require('express-validator');

// Validation for review ID parameter
const validateReviewId = [
  param('reviewId')
    .isInt({ min: 1 })
    .withMessage('Review ID must be a positive integer')
];

// Validation for product ID parameter
const validateProductId = [
  param('productId')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer')
];

// Validation for updating review status
const validateReviewStatus = [
  param('reviewId')
    .isInt({ min: 1 })
    .withMessage('Review ID must be a positive integer'),
    
  body('status')
    .isIn(['pending', 'approved', 'rejected'])
    .withMessage('Status must be pending, approved, or rejected'),
    
  body('adminResponse')
    .optional()
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Admin response must be between 1 and 1000 characters')
];

// Validation for admin response
const validateAdminResponse = [
  param('reviewId')
    .isInt({ min: 1 })
    .withMessage('Review ID must be a positive integer'),
    
  body('adminResponse')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Admin response must be between 1 and 1000 characters')
    .notEmpty()
    .withMessage('Admin response is required')
];

// Validation for bulk operations
const validateBulkReviewOperation = [
  body('reviewIds')
    .isArray({ min: 1 })
    .withMessage('Review IDs must be a non-empty array')
    .custom((reviewIds) => {
      if (!reviewIds.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('All review IDs must be positive integers');
      }
      return true;
    }),
    
  body('action')
    .isIn(['approve', 'reject', 'pending', 'delete'])
    .withMessage('Action must be approve, reject, pending, or delete')
];

// Validation for review listing query parameters
const validateReviewQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
    
  query('status')
    .optional()
    .isIn(['all', 'pending', 'approved', 'rejected'])
    .withMessage('Status filter must be all, pending, approved, or rejected'),
    
  query('rating')
    .optional()
    .custom((value) => {
      if (value === 'all') return true;
      const rating = parseInt(value);
      if (!Number.isInteger(rating) || rating < 1 || rating > 5) {
        throw new Error('Rating filter must be "all" or an integer between 1 and 5');
      }
      return true;
    }),
    
  query('verified')
    .optional()
    .isBoolean()
    .withMessage('Verified filter must be a boolean value'),
    
  query('productId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
    
  query('userId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer'),
    
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'rating', 'helpful', 'status'])
    .withMessage('Sort field must be createdAt, rating, helpful, or status'),
    
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
    
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid ISO 8601 date'),
    
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid ISO 8601 date'),
    
  query('includeProduct')
    .optional()
    .isBoolean()
    .withMessage('Include product must be a boolean value'),
    
  query('includeUser')
    .optional()
    .isBoolean()
    .withMessage('Include user must be a boolean value')
];

// Validation for product reviews query
const validateProductReviewQuery = [
  param('productId')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
    
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  query('status')
    .optional()
    .isIn(['all', 'pending', 'approved', 'rejected'])
    .withMessage('Status filter must be all, pending, approved, or rejected')
];

// Custom validation for date ranges
const validateDateRange = (req, res, next) => {
  const { dateFrom, dateTo } = req.query;
  
  if (dateFrom && dateTo) {
    const fromDate = new Date(dateFrom);
    const toDate = new Date(dateTo);
    
    if (fromDate >= toDate) {
      return res.status(400).json({
        success: false,
        message: 'Date from must be before date to'
      });
    }
    
    // Check if date range is not too large (e.g., more than 1 year)
    const daysDiff = (toDate - fromDate) / (1000 * 60 * 60 * 24);
    if (daysDiff > 365) {
      return res.status(400).json({
        success: false,
        message: 'Date range cannot exceed 365 days'
      });
    }
  }
  
  next();
};

// Role-based access control for review management
const requireReviewManagementAccess = (req, res, next) => {
  if (!['admin', 'manager', 'super_admin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Review management access required'
    });
  }
  next();
};

// Middleware to check if review exists
const checkReviewExists = async (req, res, next) => {
  try {
    const { reviewId } = req.params;
    const { Review } = require('../models');
    
    const review = await Review.findByPk(reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }
    
    req.review = review;
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error checking review existence',
      error: error.message
    });
  }
};

// Middleware to check if product exists
const checkProductExists = async (req, res, next) => {
  try {
    const { productId } = req.params;
    const { Product } = require('../models');
    
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    req.product = product;
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error checking product existence',
      error: error.message
    });
  }
};

// Sanitize review content
const sanitizeReviewContent = (req, res, next) => {
  if (req.body.adminResponse) {
    // Basic HTML sanitization (in production, use a proper library like DOMPurify)
    req.body.adminResponse = req.body.adminResponse
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
  next();
};

module.exports = {
  validateReviewId,
  validateProductId,
  validateReviewStatus,
  validateAdminResponse,
  validateBulkReviewOperation,
  validateReviewQuery,
  validateProductReviewQuery,
  validateDateRange,
  requireReviewManagementAccess,
  checkReviewExists,
  checkProductExists,
  sanitizeReviewContent
};
